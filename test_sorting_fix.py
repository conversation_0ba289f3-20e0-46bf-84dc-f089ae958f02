#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试排序修复
验证移除自动排序后表格显示正常
"""

import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_auto_sorting_removal():
    """测试自动排序移除"""
    print("=== 测试自动排序移除 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否移除了自动排序
        auto_sort_patterns = [
            "sortItems.*DescendingOrder",  # 自动排序调用
            "按ID列降序排序",  # 自动排序注释
        ]
        
        import re
        for pattern in auto_sort_patterns:
            if re.search(pattern, content):
                print(f"❌ 仍存在自动排序: {pattern}")
                return False
            else:
                print(f"✅ 已移除自动排序: {pattern}")
        
        # 检查是否保留了排序功能
        sort_features = [
            "setSortingEnabled\\(True\\)",  # 启用排序
            "sectionClicked.connect.*update_vm_table_sequence",  # 排序信号连接
            "可点击列标题进行排序",  # 手动排序提示
        ]
        
        for feature in sort_features:
            if re.search(feature, content):
                print(f"✅ 排序功能保留: {feature}")
            else:
                print(f"❌ 排序功能缺失: {feature}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 自动排序移除测试失败: {str(e)}")
        return False

def test_data_level_sorting():
    """测试数据层面排序"""
    print("\n=== 测试数据层面排序 ===")
    
    try:
        with open("src/core/video_material_manager.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查数据层面的排序
        data_sort_features = [
            "sort_values.*更新日期.*ID",  # 多列排序
            "ascending=\\[False, False\\]",  # 降序排序
            "按更新日期和ID降序排列",  # 排序日志
        ]
        
        import re
        for feature in data_sort_features:
            if re.search(feature, content):
                print(f"✅ 数据层面排序存在: {feature}")
            else:
                print(f"❌ 数据层面排序缺失: {feature}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据层面排序测试失败: {str(e)}")
        return False

def test_table_filling_integrity():
    """测试表格填充完整性"""
    print("\n=== 测试表格填充完整性 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查表格填充完整性
        filling_features = [
            "数据填充完成，共填充.*行",  # 填充完成日志
            "setSortingEnabled\\(True\\)",  # 启用排序
            "表格填充完成，可点击列标题进行排序",  # 完成提示
        ]
        
        import re
        for feature in filling_features:
            if re.search(feature, content):
                print(f"✅ 表格填充完整性存在: {feature}")
            else:
                print(f"❌ 表格填充完整性缺失: {feature}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 表格填充完整性测试失败: {str(e)}")
        return False

def test_sorting_behavior():
    """测试排序行为"""
    print("\n=== 测试排序行为 ===")
    
    try:
        import pandas as pd
        
        # 模拟数据排序行为
        data = {
            'ID': [38050, 38039, 38032, 38033, 38034],
            '更新日期': ['2025-07-31', '2025-07-31', '2025-07-31', '2025-07-30', '2025-07-29'],
            'name': ['张三', '李四', '王五', '赵六', '钱七']
        }
        
        df = pd.DataFrame(data)
        print(f"原始数据顺序: {list(df['ID'])}")
        
        # 按更新日期和ID降序排序
        df_sorted = df.sort_values(['更新日期', 'ID'], ascending=[False, False])
        print(f"排序后数据顺序: {list(df_sorted['ID'])}")
        
        # 验证排序结果
        expected_order = [38050, 38039, 38032, 38033, 38034]  # 按日期和ID降序
        actual_order = list(df_sorted['ID'])
        
        if actual_order == expected_order:
            print("✅ 数据排序行为正确")
            return True
        else:
            print(f"❌ 数据排序行为错误，期望{expected_order}，实际{actual_order}")
            return False
        
    except Exception as e:
        print(f"❌ 排序行为测试失败: {str(e)}")
        return False

def test_debug_logging():
    """测试调试日志"""
    print("\n=== 测试调试日志 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            ui_content = f.read()
        
        with open("src/core/video_material_manager.py", "r", encoding="utf-8") as f:
            vm_content = f.read()
        
        # 检查调试日志
        debug_logs = [
            ("设置成功.*verify_item.text", ui_content),  # 设置验证日志
            ("数据填充完成", ui_content),  # 填充完成日志
            ("按更新日期和ID降序排列", vm_content),  # 排序日志
            ("表格填充完成，可点击列标题进行排序", ui_content),  # 完成提示
        ]
        
        import re
        for log_pattern, content in debug_logs:
            if re.search(log_pattern, content):
                print(f"✅ 调试日志存在: {log_pattern}")
            else:
                print(f"❌ 调试日志缺失: {log_pattern}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 调试日志测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 排序修复测试")
    print("=" * 60)
    
    # 测试自动排序移除
    if not test_auto_sorting_removal():
        print("❌ 自动排序移除测试失败")
        return False
    
    # 测试数据层面排序
    if not test_data_level_sorting():
        print("❌ 数据层面排序测试失败")
        return False
    
    # 测试表格填充完整性
    if not test_table_filling_integrity():
        print("❌ 表格填充完整性测试失败")
        return False
    
    # 测试排序行为
    if not test_sorting_behavior():
        print("❌ 排序行为测试失败")
        return False
    
    # 测试调试日志
    if not test_debug_logging():
        print("❌ 调试日志测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！")
    print("\n📋 修复总结:")
    print("✅ 1. 移除自动排序:")
    print("  - 移除了填充后的自动sortItems调用")
    print("  - 保留了手动排序功能")
    print("  - 避免了排序破坏数据对应关系")
    
    print("\n✅ 2. 数据层面排序:")
    print("  - 在数据读取时进行排序")
    print("  - 按更新日期和ID双重排序")
    print("  - 确保数据顺序的一致性")
    
    print("\n✅ 3. 表格填充完整性:")
    print("  - 数据填充完成后不自动排序")
    print("  - 保持数据填充时的顺序")
    print("  - 用户可手动点击列标题排序")
    
    print("\n✅ 4. 排序行为优化:")
    print("  - 多列排序确保数据有序")
    print("  - 降序排序符合用户习惯")
    print("  - 排序逻辑清晰可控")
    
    print("\n✅ 5. 调试日志完善:")
    print("  - 详细的设置验证日志")
    print("  - 清晰的排序状态提示")
    print("  - 完整的操作过程跟踪")
    
    print("\n🎯 解决的问题:")
    print("  - 自动排序破坏数据对应关系 → 移除自动排序")
    print("  - 表格显示与数据不符 → 数据层面排序")
    print("  - ID显示错误 → 保持填充时的数据顺序")
    print("  - 排序功能缺失 → 保留手动排序功能")
    
    print("\n🚀 预期效果:")
    print("  - 刷新表格后数据显示正确")
    print("  - ID和其他列内容完全对应")
    print("  - 用户可手动点击列标题排序")
    print("  - 数据顺序稳定可预测")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
