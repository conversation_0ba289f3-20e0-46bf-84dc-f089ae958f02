"""
测试强力抑制器
"""

import os
import sys

# 添加src路径
sys.path.append('src')


def test_strong_suppression():
    """测试强力抑制器"""
    print("=" * 60)
    print("💪 测试强力asyncio错误抑制器")
    print("=" * 60)
    
    print("🔍 问题分析:")
    print("  之前的过滤器没有生效，可能原因:")
    print("    1. 错误不是通过sys.stderr输出")
    print("    2. 错误是在C扩展层面输出的")
    print("    3. 错误是通过warnings模块输出的")
    print("    4. 错误是在垃圾回收时直接输出的")
    print("")
    
    print("💪 强力解决方案:")
    print("  多层抑制策略:")
    print("    1. ✅ warnings.filterwarnings() - 抑制ResourceWarning")
    print("    2. ✅ 环境变量 PYTHONASYNCIODEBUG=0")
    print("    3. ✅ 环境变量 PYTHONWARNINGS=ignore::ResourceWarning")
    print("    4. ✅ 替换sys.stderr进行文本过滤")
    print("    5. ✅ 猴子补丁修改asyncio行为")
    print("    6. ✅ 上下文管理器临时抑制")
    print("")
    
    print("🔧 技术实现:")
    print("  StrongAsyncioErrorSuppressor类:")
    print("    - 线程安全的错误过滤")
    print("    - 统计被抑制的错误数量")
    print("    - 更宽泛的错误模式匹配")
    print("")
    print("  全面的抑制方法:")
    print("    warnings.filterwarnings('ignore', category=ResourceWarning)")
    print("    warnings.filterwarnings('ignore', message='.*unclosed.*')")
    print("    warnings.filterwarnings('ignore', message='.*Event loop is closed.*')")
    print("    os.environ['PYTHONASYNCIODEBUG'] = '0'")
    print("    os.environ['PYTHONWARNINGS'] = 'ignore::ResourceWarning'")
    print("")
    
    print("🎯 预期效果:")
    print("  启动时:")
    print("    '🔧 正在设置全面的asyncio错误抑制...'")
    print("    '✅ 已启用强力asyncio错误抑制'")
    print("    '✅ 已应用asyncio猴子补丁'")
    print("    '✅ 全面的asyncio错误抑制已启用'")
    print("")
    print("  运行时:")
    print("    ✅ 完全消除asyncio错误输出")
    print("    ✅ 干净的终端显示")
    print("    ✅ 保留其他重要错误")
    print("    ✅ 不影响程序功能")
    print("")
    
    print("🔍 错误模式匹配:")
    print("  被抑制的模式:")
    patterns = [
        "_ProactorBasePipeTransport.__del__",
        "BaseSubprocessTransport.__del__",
        "StreamWriter.__del__",
        "Event loop is closed",
        "I/O operation on closed pipe",
        "unclosed transport",
        "Exception ignored in:",
        "ResourceWarning"
    ]
    for i, pattern in enumerate(patterns, 1):
        print(f"    {i}. {pattern}")
    print("")
    
    print("🚀 测试建议:")
    print("  1. 重启程序:")
    print("     python src/main.py")
    print("")
    print("  2. 观察启动信息:")
    print("     - 应该看到强力抑制器的启用信息")
    print("     - 应该看到猴子补丁的应用信息")
    print("")
    print("  3. 进行重命名测试:")
    print("     - 执行完整的重命名流程")
    print("     - 观察终端输出")
    print("     - 确认没有asyncio错误")
    print("")
    print("  4. 验证功能完整性:")
    print("     - 重命名功能正常")
    print("     - Excel更新正常")
    print("     - 界面响应正常")
    print("")
    
    print("💡 如果还有错误:")
    print("  可能的原因:")
    print("    1. 错误是在更底层输出的")
    print("    2. 需要更激进的抑制方法")
    print("    3. 可能需要修改Python解释器设置")
    print("")
    print("  最终解决方案:")
    print("    如果强力抑制器仍然无效，可以考虑:")
    print("    1. 使用subprocess重定向stderr")
    print("    2. 修改Python启动参数")
    print("    3. 使用外部工具过滤输出")
    print("")
    
    print("🔧 调试模式:")
    print("  启用调试模式:")
    print("    set DISABLE_ASYNCIO_FILTER=1")
    print("    python src/main.py")
    print("")
    print("  禁用调试模式:")
    print("    set DISABLE_ASYNCIO_FILTER=")
    print("    python src/main.py")
    print("")
    
    print("=" * 60)
    print("💪 强力抑制器测试完成")
    print("=" * 60)


def test_suppressor_import():
    """测试抑制器导入"""
    print("\n" + "=" * 40)
    print("🧪 测试抑制器导入")
    print("=" * 40)
    
    try:
        from src.utils.asyncio_error_suppressor import (
            StrongAsyncioErrorSuppressor,
            apply_strong_suppression,
            setup_comprehensive_suppression
        )
        
        print("✅ 抑制器模块导入成功")
        
        # 测试抑制器创建
        suppressor = StrongAsyncioErrorSuppressor()
        print("✅ 抑制器实例创建成功")
        
        # 测试抑制逻辑
        test_texts = [
            "Exception ignored in: <function _ProactorBasePipeTransport.__del__>",
            "RuntimeError: Event loop is closed",
            "ValueError: I/O operation on closed pipe",
            "Normal error message"
        ]
        
        print("\n📋 抑制测试:")
        for text in test_texts:
            should_suppress = suppressor._should_suppress(text)
            action = "抑制" if should_suppress else "保留"
            status = "✅" if should_suppress != ("Normal error" in text) else "❌"
            print(f"  {status} '{text[:50]}...' → {action}")
        
        print("\n✅ 抑制器逻辑测试完成")
        
    except Exception as e:
        print(f"❌ 抑制器测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")


if __name__ == "__main__":
    test_strong_suppression()
    test_suppressor_import()
