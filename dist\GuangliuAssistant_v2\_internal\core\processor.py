#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
处理器类，重构自main.py，用于处理声音克隆的核心逻辑
"""

import os
import sys
import json
import pandas as pd
from tqdm import tqdm
import httpx
import config
from datetime import datetime
import re
import asyncio
from concurrent.futures import ThreadPoolExecutor
import queue
from functools import partial
import urllib.parse
import time

# 尝试导入pypinyin库，用于获取汉字拼音
try:
    import pypinyin
    HAS_PYPINYIN = True
except ImportError:
    print("⚠️ 未检测到pypinyin库，尝试自动安装...")
    try:
        import subprocess
        subprocess.check_call(['pip', 'install', 'pypinyin'])
        import pypinyin
        HAS_PYPINYIN = True
        print("✓ pypinyin库安装成功")
    except Exception as e:
        print(f"❌ 无法自动安装pypinyin库: {e}")
        print("请手动安装pypinyin库: pip install pypinyin")
        print("程序将继续执行，但匹配人姓名拼音功能不可用")
        HAS_PYPINYIN = False

from PySide6.QtCore import QObject, Signal, Slot, QThread

class ProcessorSignals(QObject):
    """处理器信号类，用于向UI发送信号"""
    
    # 定义信号
    progress = Signal(int, int, str)  # 进度信号：当前项索引，总项数，状态
    item_status_changed = Signal(int, str, str)  # 项目状态变化信号：索引，状态，时间
    process_finished = Signal(bool, str)  # 处理完成信号：是否成功，消息
    log_message = Signal(str)  # 日志消息信号

class AudioProcessor(QObject):
    """音频处理器类，用于处理声音克隆的核心逻辑"""
    
    def __init__(self, config_manager):
        """初始化处理器"""
        super().__init__()
        
        # 配置管理器
        self.config_manager = config_manager
        
        # 创建信号对象
        self.signals = ProcessorSignals()
        
        # 当前工作文件夹
        self.current_work_folder = None
        self.download_folder = None
        
        # 声音ID映射
        self.voice_id_map = {}
        
        # 文案数据
        self.scripts_df = None
        
        # 处理线程
        self.processing_thread = None
        
        # 正在运行标志
        self.is_running = False
    
    def log(self, message):
        """记录日志消息"""
        # 只通过信号发送到UI，避免重复打印
        self.signals.log_message.emit(message)
    
    # 从voice_automation.py提取的辅助函数
    def extract_order_number(self, order_number):
        """
        从工单号中提取指定部分
        例如：
        "二组1-398259-口播-竖版" -> "二组1-398259"
        "二组1-398070-398069-口播-横竖" -> "二组1-398070-398069"
        """
        # 查找最后一个6位连续数字的位置
        match = re.search(r'\d{6}', str(order_number))
        if match:
            # 获取6位数字的位置
            end_pos = match.end()
            # 返回从开始到6位数字结束的部分
            return order_number[:end_pos]
        return order_number

    def extract_prefix(self, name_string):
        """
        从命名字符串中提取第一个'-'前面的内容
        例如：
        "二组2-399086-399087-" -> "二组2"
        "二组1-399104-口播-竖版" -> "二组1"
        """
        if not name_string or '-' not in str(name_string):
            return name_string
        
        # 分割并返回第一个部分
        parts = str(name_string).split('-', 1)
        return parts[0]

    def get_last_char_pinyin_initial(self, name):
        """
        获取中文名称最后一个字的拼音首字母
        例如：
        '王凡' -> 'f'
        '张蔓' -> 'm'
        '李明亮' -> 'l'
        """
        if not name or str(name).lower() == 'nan' or str(name).strip() == '':
            return ''
        
        # 清理名称，去除空格
        name = str(name).strip()
        
        # 如果名称为空，返回空字符串
        if not name:
            return ''
        
        try:
            # 获取最后一个字符
            last_char = name[-1]
            
            if HAS_PYPINYIN:
                # 获取该字符的拼音（使用pypinyin库）
                pinyin = pypinyin.lazy_pinyin(last_char, style=pypinyin.FIRST_LETTER)
                
                # 返回拼音首字母（如果有）
                if pinyin and len(pinyin) > 0:
                    return pinyin[0].lower()
            
            # 如果无法获取拼音（可能是非中文字符或未安装pypinyin），则返回原字符的小写形式
            return last_char.lower()
        except Exception as e:
            self.log(f"获取拼音首字母时出错: {e}")
            # 出错时返回空字符串
            return ''

    def setup_environment(self):
        """
        设置环境，创建必要的目录结构
        """
        # 创建输出目录
        output_dir = self.config_manager.get("output_dir", config.OUTPUT_DIR)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            self.log(f"已创建输出目录: {output_dir}")

        # 使用配置管理器创建当天工作文件夹（只创建主文件夹）
        folders = self.config_manager.create_daily_work_folder()
        self.current_work_folder = folders["main_folder"]

        self.log(f"创建主文件夹: {self.current_work_folder}")

        # 注意：download_folder 现在由主窗口在开始处理时设置
        # 这里不再自动创建下载文件夹

        return self.current_work_folder

    def get_matcher_folder(self, base_folder, matcher_name):
        """
        根据匹配人姓名获取对应的文件夹路径，如果不存在则创建
        """
        if not matcher_name or str(matcher_name).lower() == "nan" or str(matcher_name).strip() == "":
            # 如果匹配人为空，使用默认文件夹
            return base_folder
            
        # 规范化匹配人名称，去除空格等
        matcher_name = str(matcher_name).strip()
        
        # 创建匹配人文件夹（如果不存在）
        matcher_folder = os.path.join(base_folder, matcher_name)
        os.makedirs(matcher_folder, exist_ok=True)
        self.log(f"使用匹配人文件夹: {matcher_name}")
        return matcher_folder

    async def load_voice_ids_from_baidu_sheet(self, sheet_url=None):
        """
        从百度在线表格加载声音ID数据，支持百度知识空间表格
        
        Args:
            sheet_url: 百度表格URL，如果为None则从配置中获取
        
        Returns:
            dict: 包含演员名称到声音ID的映射
        """
        # 如果没有提供URL，从配置中获取
        if not sheet_url:
            sheet_url = self.config_manager.get("baidu_sheet_url", "")
            if not sheet_url:
                self.log("错误: 未配置百度表格URL，无法从在线表格加载数据")
                return {}
        
        try:
            self.log(f"正在从百度在线表格加载数据: {sheet_url}")
            
            # 使用httpx异步请求数据
            async with httpx.AsyncClient(timeout=30.0) as client:
                # 初步获取表格页面
                response = await client.get(sheet_url, follow_redirects=True)
                response.raise_for_status()
                
                # 解析页面内容，提取表格数据
                # 注意：这里需要针对百度表格实际结构进行调整
                content = response.text
                
                # 寻找表格数据的JSON部分
                data_match = re.search(r'window\.SHEET_INITIAL_DATA\s*=\s*({.*?});', content, re.DOTALL)
                if not data_match:
                    self.log("错误: 无法从百度表格页面提取数据")
                    return {}
                
                try:
                    # 提取并解析JSON数据
                    sheet_data = json.loads(data_match.group(1))
                    
                    # 解析表格数据（具体结构需要根据百度表格调整）
                    if 'tableData' in sheet_data and 'rows' in sheet_data['tableData']:
                        rows = sheet_data['tableData']['rows']
                        
                        # 提取列名（表头）
                        header_row = rows[0] if rows else []
                        header_cells = header_row.get('cells', [])
                        
                        # 找到演员名称和模型ID列的索引
                        name_col_idx = -1
                        model_id_col_idx = -1
                        
                        for i, cell in enumerate(header_cells):
                            cell_text = cell.get('text', '')
                            if cell_text == config.ACTOR_NAME_COLUMN:
                                name_col_idx = i
                            elif cell_text == config.MODEL_ID_COLUMN:
                                model_id_col_idx = i
                        
                        if name_col_idx == -1 or model_id_col_idx == -1:
                            self.log(f"错误: 百度表格中未找到必要的列: {config.ACTOR_NAME_COLUMN}或{config.MODEL_ID_COLUMN}")
                            return {}
                        
                        # 构建演员名称到声音ID的映射
                        voice_id_map = {}
                        for row_idx in range(1, len(rows)):  # 跳过表头
                            row = rows[row_idx]
                            cells = row.get('cells', [])
                            
                            if len(cells) > max(name_col_idx, model_id_col_idx):
                                actor_name = cells[name_col_idx].get('text', '').strip()
                                model_id = cells[model_id_col_idx].get('text', '').strip()
                                
                                if actor_name and model_id:
                                    voice_id_map[actor_name] = model_id
                        
                        self.log(f"已从百度表格加载 {len(voice_id_map)} 个声音ID")
                        self.voice_id_map = voice_id_map
                        return voice_id_map
                    else:
                        self.log("错误: 百度表格数据结构不正确，无法提取表格内容")
                        return {}
                
                except json.JSONDecodeError:
                    self.log("错误: 无法解析百度表格JSON数据")
                    return {}
        except Exception as e:
            self.log(f"从百度表格加载声音ID时出错: {str(e)}")
            return {}

    def clear_cache(self):
        """清除声音ID缓存文件"""
        cache_file = os.path.join("config", "baidu_sheet_cache.json")
        if os.path.exists(cache_file):
            try:
                os.remove(cache_file)
                self.log(f"已删除缓存文件: {cache_file}")
                return True
            except Exception as e:
                self.log(f"删除缓存文件失败: {str(e)}")
                return False
        return True  # 如果缓存文件不存在，视为清除成功

    def update_local_voice_id_excel(self, voice_id_map):
        """
        将在线获取的声音ID数据更新到本地Excel文件
        确保本地文件始终包含最新的数据，作为网络失败时的备用

        Args:
            voice_id_map: 声音ID映射字典 {演员名称: 声音ID}
        """
        try:
            # 获取本地Excel文件路径
            voice_id_excel = self.config_manager.get("voice_id_excel", "data/voice_id_list.xlsx")

            # 确保目录存在
            os.makedirs(os.path.dirname(voice_id_excel), exist_ok=True)

            # 创建DataFrame
            df_data = []
            for actor_name, voice_id in voice_id_map.items():
                df_data.append({
                    config.ACTOR_NAME_COLUMN: actor_name,
                    config.MODEL_ID_COLUMN: voice_id
                })

            df = pd.DataFrame(df_data)

            # 保存到Excel文件
            df.to_excel(voice_id_excel, index=False)

            self.log(f"已将最新的 {len(voice_id_map)} 个声音ID更新到本地Excel文件: {voice_id_excel}")
            self.log("本地Excel文件已更新为最新数据，可作为网络失败时的备用数据源")

        except Exception as e:
            self.log(f"更新本地Excel文件时出错: {str(e)}")
            raise

    async def load_voice_ids_from_baidu_sheet_api(self, sheet_url=None, force_refresh=False):
        """
        从百度在线表格API加载声音ID数据，使用官方API而非网页抓取
        
        Args:
            sheet_url: 百度表格URL，如果为None则从配置中获取
            force_refresh: 是否强制刷新，True则忽略缓存
            
        Returns:
            dict: 包含演员名称到声音ID的映射
        """
        # 如果强制刷新，直接删除缓存
        if force_refresh:
            self.clear_cache()
            self.log("强制刷新模式：已删除缓存文件，将从API获取最新数据")
        
        # 如果没有提供URL，从配置中获取
        if not sheet_url:
            sheet_url = self.config_manager.get("baidu_sheet_url", "")
            if not sheet_url:
                self.log("错误: 未配置百度表格URL，无法从在线表格加载数据")
                return {}
        
        try:
            self.log(f"正在使用Baidu Sheets API从在线表格加载数据: {sheet_url}")
            
            # 检查缓存是否存在且有效
            cache_file = os.path.join("config", "baidu_sheet_cache.json")
            use_cache = False
            
            if not force_refresh and os.path.exists(cache_file):
                try:
                    with open(cache_file, "r", encoding="utf-8") as f:
                        cache_data = json.load(f)
                        
                    # 检查缓存是否为当天的数据
                    cache_date = cache_data.get("timestamp", "").split(" ")[0]
                    today = datetime.now().strftime("%Y-%m-%d")
                    
                    if cache_date == today and "voice_ids" in cache_data and cache_data["voice_ids"]:
                        self.log(f"使用缓存的声音ID数据 (缓存时间: {cache_data['timestamp']})")
                        return cache_data["voice_ids"]
                except Exception as e:
                    self.log(f"读取缓存文件失败: {str(e)}")
            
            if force_refresh:
                self.log("强制刷新模式：跳过缓存，直接从API获取最新数据")
            
            # 从URL中提取百度表格的文档ID和视图ID
            parsed_url = urllib.parse.urlparse(sheet_url)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            
            # 在新格式URL中，表格ID通常在tb参数中
            datasheet_id = None
            view_id = None
            
            # 从参数中提取datasheetId和viewId
            if 'tb' in query_params and query_params['tb']:
                tb_value = query_params['tb'][0]
                # 检查tb值的格式，应该类似于 "dstzKKQrWyC6wLK0RZ_viwzvycL6E8P6"
                if tb_value.startswith('dst'):
                    # 正确提取datasheetId和viewId
                    if '_' in tb_value:
                        # 格式可能是 "dstzKKQrWyC6wLK0RZ_viwzvycL6E8P6"
                        parts = tb_value.split('_')
                        datasheet_id = parts[0]  # 包含'dst'前缀
                        
                        # 第二部分可能是viewId(以viw开头)
                        if parts[1].startswith('viw'):
                            view_id = parts[1]
                        
                        self.log(f"从URL解析: datasheetId={datasheet_id}, viewId={view_id}")
                    else:
                        # 如果没有_，整个部分可能只是datasheetId
                        datasheet_id = tb_value
                        self.log(f"从URL解析: datasheetId={datasheet_id}, 未找到viewId")
                else:
                    datasheet_id = tb_value
                    self.log(f"直接使用tb参数值作为datasheetId: {datasheet_id}")
            
            # 如果在URL的其他参数中找到viewId且当前没有viewId
            if not view_id and 'viewId' in query_params and query_params['viewId']:
                view_id = query_params['viewId'][0]
                self.log(f"从viewId参数解析: viewId={view_id}")
            
            if not datasheet_id:
                self.log("错误: 无法从URL中提取表格ID (datasheetId)")
                return {}
            
            # 获取API访问令牌
            api_token = self.config_manager.get("baidu_sheets_token", "")
            if not api_token:
                self.log("错误: 未设置百度表格API访问令牌，请在设置中配置")
                return {}
            
            # 构建正确的API URL
            api_url = f"https://ku.baidu-int.com/fusion/v1/datasheets/{datasheet_id}/records"
            
            # 构建查询参数
            params = {
                "fieldKey": "name",  # 使用列名作为字段键
                "pageSize": 1000     # 设置每页返回记录数为最大值1000条
            }
            
            # 如果有视图ID，添加到参数中
            if view_id:
                params["viewId"] = view_id
            
            # 构建请求头，正确添加Authorization
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_token}"
            }
            
            # 请求信息示意（避免显示完整token）
            token_display = "****" + api_token[-4:] if len(api_token) > 8 else "****"
            self.log(f"API请求URL: {api_url}")
            self.log(f"API请求参数: {params}")
            self.log(f"API授权令牌: {token_display}")
            
            # 使用httpx异步发送API请求
            voice_id_map = {}
            all_records = []
            page = 1
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                # 首次请求
                initial_params = params.copy()
                response = await client.get(api_url, params=initial_params, headers=headers)
                
                # 检查响应状态码
                if response.status_code != 200:
                    self.log(f"HTTP错误: 状态码 {response.status_code}")
                    error_text = response.text[:200] + "..." if len(response.text) > 200 else response.text
                    self.log(f"响应内容: {error_text}")
                    return {}
                
                # 解析API响应
                try:
                    data = response.json()
                except json.JSONDecodeError:
                    self.log(f"解析JSON响应失败: {response.text[:200]}...")
                    return {}
                
                # 检查API响应是否成功
                if "error_code" in data:
                    self.log(f"API错误: {data.get('error_msg', '未知错误')} (错误码: {data.get('error_code')})")
                    return {}
                
                if not data.get("success", False):
                    error_msg = data.get("message", "未知错误")
                    self.log(f"API请求失败: {error_msg}")
                    return {}
                
                # 根据API响应格式解析数据
                if "data" not in data:
                    self.log(f"错误: API响应中缺少data字段")
                    return {}
                
                response_data = data["data"]
                if "records" not in response_data:
                    self.log(f"错误: API响应中缺少records字段")
                    return {}
                
                records = response_data["records"]
                total_records = response_data.get("total", 0)
                self.log(f"API返回了 {len(records)} 条记录，总共: {total_records} 条")
                
                # 处理第一页数据
                all_records.extend(records)
                
                # 如果有多页数据，继续获取
                total_pages = (total_records + params["pageSize"] - 1) // params["pageSize"]
                
                if total_pages > 1:
                    self.log(f"表格数据有多页，共 {total_pages} 页，正在分页获取...")
                    
                    # 获取剩余页的数据
                    for page in range(2, total_pages + 1):
                        # 添加页码参数
                        page_params = params.copy()
                        page_params["page"] = page
                        
                        # 添加延迟，避免请求过于频繁
                        await asyncio.sleep(0.5)
                        
                        self.log(f"正在获取第 {page}/{total_pages} 页数据...")
                        try:
                            response = await client.get(api_url, params=page_params, headers=headers)
                            
                            # 检查响应状态码
                            if response.status_code != 200:
                                self.log(f"获取第 {page} 页时HTTP错误: 状态码 {response.status_code}")
                                continue
                            
                            data = response.json()
                            
                            # 检查API响应是否成功
                            if not data.get("success", False) or "data" not in data:
                                self.log(f"获取第 {page} 页时API请求失败")
                                continue
                            
                            # 获取当前页记录
                            page_records = data["data"].get("records", [])
                            self.log(f"第 {page} 页返回了 {len(page_records)} 条记录")
                            
                            # 合并记录
                            all_records.extend(page_records)
                        except Exception as e:
                            self.log(f"获取第 {page} 页数据时出错: {str(e)}")
                            continue
            
            # 处理所有记录，提取演员名称和声音ID
            for record in all_records:
                try:
                    if "fields" not in record:
                        continue
                    
                    fields = record["fields"]
                    
                    # 根据实际表格结构获取字段
                    actor_name = fields.get("名称", "")
                    model_id = fields.get("modelId", "")
                    
                    # 只添加有效的数据
                    if actor_name and model_id:
                        voice_id_map[actor_name] = model_id
                except Exception as e:
                    self.log(f"处理记录数据时出错: {str(e)}")
            
            self.log(f"成功从百度表格API加载了 {len(voice_id_map)} 个声音ID映射")

            # 自动更新本地Excel文件，确保本地数据始终是最新的
            try:
                self.update_local_voice_id_excel(voice_id_map)
            except Exception as e:
                self.log(f"更新本地Excel文件失败: {str(e)}")

            # 缓存数据到本地（只有在非强制刷新模式下才缓存）
            if not force_refresh:
                try:
                    os.makedirs(os.path.dirname(cache_file), exist_ok=True)

                    cache_data = {
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "url": sheet_url,
                        "voice_ids": voice_id_map
                    }

                    with open(cache_file, "w", encoding="utf-8") as f:
                        json.dump(cache_data, f, ensure_ascii=False, indent=2)

                    self.log(f"声音ID数据已缓存到本地文件: {cache_file}")
                except Exception as e:
                    self.log(f"缓存声音ID数据失败: {str(e)}")
            else:
                self.log("强制刷新模式：不创建缓存文件，确保使用最新数据")
            
            return voice_id_map
        
        except Exception as e:
            import traceback
            self.log(f"从百度表格API加载数据时出错: {str(e)}")
            self.log(f"错误详情: {traceback.format_exc()}")
            return {}

    def process_records(self, records, voice_id_map):
        """
        处理API返回的记录数据，提取演员名称和声音ID
        
        Args:
            records: API返回的记录数组
            voice_id_map: 要填充的演员名称到声音ID的映射字典
        """
        for record in records:
            try:
                if "fields" not in record:
                    continue
                
                fields = record["fields"]
                
                # 根据示例，字段名应该是"名称"和"modelId"
                actor_name = fields.get("名称", "")
                model_id = fields.get("modelId", "")
                
                # 只添加有效的数据
                if actor_name and model_id:
                    voice_id_map[actor_name] = model_id
            except Exception as e:
                self.log(f"处理记录数据时出错: {e}")

    def load_voice_ids(self, voice_id_excel=None, use_online=False, force_refresh=False):
        """
        加载声音ID数据，支持本地Excel文件和百度在线表格
        
        Args:
            voice_id_excel: Excel文件路径，如果为None则从配置中获取
            use_online: 是否使用在线表格，True则从百度表格获取，False则从本地Excel获取
            force_refresh: 是否强制刷新，True则忽略缓存
            
        Returns:
            dict: 包含演员名称到声音ID的映射
        """
        # 如果强制刷新且使用在线表格，清除缓存
        if force_refresh and (use_online or self.config_manager.get("use_online_sheet", False)):
            self.clear_cache()
            self.log("强制刷新模式：已删除缓存文件")
            
        # 检查是否使用在线表格
        if use_online or self.config_manager.get("use_online_sheet", False):
            # 使用异步函数但同步调用
            loop = asyncio.get_event_loop()
            if loop.is_running():
                self.log("警告: 事件循环已在运行，无法同步调用异步函数，将使用本地Excel")
                use_online = False
            else:
                try:
                    # 检查是否使用API方法
                    use_api = self.config_manager.get("use_baidu_api", False)
                    if use_api:
                        # 检查API令牌
                        api_token = self.config_manager.get("baidu_sheets_token", "")
                        if not api_token:
                            self.log("警告: 已启用API模式，但未设置访问令牌，将使用网页抓取模式")
                            use_api = False
                        else:
                            self.log("正在使用百度表格API加载声音ID数据...")
                            voice_id_map = loop.run_until_complete(self.load_voice_ids_from_baidu_sheet_api(force_refresh=force_refresh))
                            if voice_id_map:
                                self.voice_id_map = voice_id_map
                                return voice_id_map
                    
                    # 如果未使用API或API请求失败，使用网页抓取模式
                    if not use_api:
                        self.log("使用网页抓取模式加载在线表格数据...")
                        return loop.run_until_complete(self.load_voice_ids_from_baidu_sheet())
                except Exception as e:
                    self.log(f"从百度表格加载数据失败，将尝试使用本地Excel: {str(e)}")
                    use_online = False
        
        # 如果不使用在线表格或在线获取失败，则使用本地Excel
        if not use_online:
            if not voice_id_excel:
                voice_id_excel = self.config_manager.get("voice_id_excel", config.VOICE_ID_EXCEL)
        
        try:
            df = pd.read_excel(voice_id_excel)
            # 检查必要的列是否存在
            required_columns = [config.ACTOR_NAME_COLUMN, config.MODEL_ID_COLUMN]
            for col in required_columns:
                if col not in df.columns:
                    self.log(f"错误: 声音ID文件中缺少必要的列: {col}")
                    return {}
            
            # 创建演员名称到声音ID的映射
            voice_id_map = dict(zip(df[config.ACTOR_NAME_COLUMN], df[config.MODEL_ID_COLUMN]))
            self.log(f"已从本地Excel加载 {len(voice_id_map)} 个声音ID")
            self.voice_id_map = voice_id_map
            return voice_id_map
        except Exception as e:
            self.log(f"加载声音ID列表时出错: {str(e)}")
            return {}

    def load_scripts(self, script_excel=None):
        """
        从文案.xlsx加载需要克隆的文案数据
        
        Returns:
            pandas.DataFrame: 包含文案数据的DataFrame
        """
        if not script_excel:
            script_excel = self.config_manager.get("script_excel", config.SCRIPT_EXCEL)
        
        try:
            df = pd.read_excel(script_excel)
            # 检查必要的列是否存在
            required_columns = [config.SCRIPT_TEXT_COLUMN, config.ACTOR_COLUMN]
            for col in required_columns:
                if col not in df.columns:
                    self.log(f"错误: 文案.xlsx中缺少必要的列: {col}")
                    return None
            
            # 如果没有状态列，添加一个
            if config.STATUS_COLUMN not in df.columns:
                df[config.STATUS_COLUMN] = ""
            
            # 确保命名列存在
            if config.NAMING_COLUMN not in df.columns:
                df[config.NAMING_COLUMN] = [f"命名_{i+1}" for i in range(len(df))]
                self.log(f"注意: 自动添加了{config.NAMING_COLUMN}列")
            
            # 处理工单号，确保为字符串类型
            if config.WORK_ORDER_COLUMN in df.columns:
                df[config.WORK_ORDER_COLUMN] = df[config.WORK_ORDER_COLUMN].apply(
                    lambda x: str(int(float(x))) if pd.notna(x) and str(x).replace('.', '', 1).isdigit() else str(x)
                )
            else:
                df[config.WORK_ORDER_COLUMN] = ""
                self.log(f"注意: 自动添加了{config.WORK_ORDER_COLUMN}列")
            
            # 处理副单号
            if config.SECONDARY_ORDER_COLUMN in df.columns:
                df[config.SECONDARY_ORDER_COLUMN] = df[config.SECONDARY_ORDER_COLUMN].apply(
                    lambda x: str(int(float(x))) if pd.notna(x) and str(x).replace('.', '', 1).isdigit() else str(x)
                )
            else:
                df[config.SECONDARY_ORDER_COLUMN] = ""
                self.log(f"注意: 自动添加了{config.SECONDARY_ORDER_COLUMN}列")
            
            # 处理素材ID
            if config.MATERIAL_ID_COLUMN in df.columns:
                df[config.MATERIAL_ID_COLUMN] = df[config.MATERIAL_ID_COLUMN].apply(
                    lambda x: str(int(float(x))) if pd.notna(x) and str(x).replace('.', '', 1).isdigit() else str(x)
                )
            else:
                df[config.MATERIAL_ID_COLUMN] = ""
                self.log(f"注意: 自动添加了{config.MATERIAL_ID_COLUMN}列")
            
            # 不再需要匹配人列
            
            # 处理生成时间
            if config.GENERATION_TIME_COLUMN not in df.columns:
                df[config.GENERATION_TIME_COLUMN] = ""
                self.log(f"注意: 自动添加了{config.GENERATION_TIME_COLUMN}列")
            
            self.log(f"已加载 {len(df)} 条待处理文案")
            self.scripts_df = df
            return df
        except Exception as e:
            self.log(f"加载文案时出错: {str(e)}")
            return None

    def find_matching_actor(self, actor_name):
        """
        查找最匹配的演员名称和对应的声音ID
        
        Args:
            actor_name (str): 要查找的演员名称
        
        Returns:
            tuple: (找到的演员名称, 声音ID, 是否找到)
        """
        # 规范化演员名称
        actor_name = str(actor_name).strip()
        
        # 创建常见前缀列表
        common_prefixes = ["中年男-", "年轻男-", "中年女-", "年轻女-", "青年男-", "青年女-", "老年男-", "老年女-"]
        
        # 检查是否有特殊后缀
        is_tarot_search = "-塔罗" in actor_name
        is_mystery_search = "-神秘" in actor_name
        is_outdoor_search = "-室外" in actor_name
        
        # 检查搜索词是否包含前缀
        has_prefix = False
        actor_prefix = ""
        for prefix in common_prefixes:
            if actor_name.startswith(prefix):
                has_prefix = True
                actor_prefix = prefix
                break
        
        # 1. 直接匹配（优先级最高）
        if actor_name in self.voice_id_map:
            self.log(f"✓ 直接匹配到演员: '{actor_name}'")
            return actor_name, self.voice_id_map[actor_name], True
        
        if not config.ENABLE_FUZZY_ACTOR_MATCHING:
            return None, None, False
        
        # 2. 提取演员基本名称（不含后缀）
        actor_base_name = actor_name
        for suffix in ["-塔罗", "-神秘", "-室外", "-室内"]:
            if suffix in actor_base_name:
                actor_base_name = actor_base_name.replace(suffix, "")
        
        actor_base_name = actor_base_name.strip()
        
        # 3. 处理搜索词中包含前缀的情况，去除前缀后的基本名称
        actor_without_prefix = actor_base_name
        if has_prefix:
            actor_without_prefix = actor_base_name.replace(actor_prefix, "")
            actor_without_prefix = actor_without_prefix.strip()
        
        # 4. 首先尝试匹配带有相同特殊后缀的完整名称(包含前缀)
        if is_tarot_search or is_mystery_search or is_outdoor_search:
            for name, id in self.voice_id_map.items():
                # 如果是塔罗搜索，优先匹配含塔罗的声音
                if is_tarot_search and "-塔罗" in name:
                    # 检查基本名称是否匹配
                    name_base = name
                    for prefix in common_prefixes:
                        if name.startswith(prefix):
                            name_base = name[len(prefix):].strip()
                            break
                    
                    # 去除后缀
                    for suffix in ["-塔罗", "-神秘", "-室外", "-室内"]:
                        if suffix in name_base:
                            name_base = name_base.replace(suffix, "").strip()
                    
                    # 如果基本名称匹配，返回结果
                    if actor_without_prefix == name_base or actor_base_name == name_base:
                        self.log(f"✓ 找到塔罗后缀匹配: '{name}'")
                        return name, id, True
                
                # 如果是神秘搜索，优先匹配含神秘的声音
                elif is_mystery_search and "-神秘" in name:
                    # 类似的检查逻辑
                    name_base = name
                    for prefix in common_prefixes:
                        if name.startswith(prefix):
                            name_base = name[len(prefix):].strip()
                            break
                    
                    for suffix in ["-塔罗", "-神秘", "-室外", "-室内"]:
                        if suffix in name_base:
                            name_base = name_base.replace(suffix, "").strip()
                    
                    if actor_without_prefix == name_base or actor_base_name == name_base:
                        self.log(f"✓ 找到神秘后缀匹配: '{name}'")
                        return name, id, True
                
                # 如果是室外搜索，优先匹配含室外的声音
                elif is_outdoor_search and "-室外" in name:
                    # 类似的检查逻辑
                    name_base = name
                    for prefix in common_prefixes:
                        if name.startswith(prefix):
                            name_base = name[len(prefix):].strip()
                            break
                    
                    for suffix in ["-塔罗", "-神秘", "-室外", "-室内"]:
                        if suffix in name_base:
                            name_base = name_base.replace(suffix, "").strip()
                    
                    if actor_without_prefix == name_base or actor_base_name == name_base:
                        self.log(f"✓ 找到室外后缀匹配: '{name}'")
                        return name, id, True
        
        # 5. 后缀替换匹配（使用配置的替换规则）
        for suffix, replacement in config.ACTOR_SUFFIX_MAPPING.items():
            if suffix in actor_name:
                alternate_actor_name = actor_name.replace(suffix, replacement)
                self.log(f"尝试替代后缀搜索: '{alternate_actor_name}'")
                if alternate_actor_name in self.voice_id_map:
                    self.log(f"✓ 找到替代后缀匹配: '{alternate_actor_name}'")
                    return alternate_actor_name, self.voice_id_map[alternate_actor_name], True
        
        # 6. 使用基本名称进行模糊匹配
        best_match = None
        best_match_score = 0
        best_match_id = None
        
        for name, id in self.voice_id_map.items():
            # 检查后缀匹配条件
            # 排除特殊条件
            if (is_tarot_search and "-塔罗" not in name) or \
               (is_mystery_search and "-神秘" not in name) or \
               (not is_tarot_search and not is_mystery_search and ("-塔罗" in name or "-神秘" in name)):
                continue
            
            # 去除候选名称中的前缀
            candidate_name = name
            candidate_has_prefix = False
            candidate_prefix = ""
            for prefix in common_prefixes:
                if candidate_name.startswith(prefix):
                    candidate_has_prefix = True
                    candidate_prefix = prefix
                    candidate_name = candidate_name[len(prefix):].strip()
                    break
            
            # 计算匹配分数
            score = 0
            
            # 匹配逻辑
            if has_prefix and candidate_has_prefix and actor_prefix == candidate_prefix:
                if actor_without_prefix == candidate_name:
                    score = 1.0
                elif actor_without_prefix in candidate_name:
                    score = len(actor_without_prefix) / len(candidate_name) * 0.95
                elif candidate_name in actor_without_prefix:
                    score = len(candidate_name) / len(actor_without_prefix) * 0.95
            elif not has_prefix:
                if actor_base_name == candidate_name:
                    score = 1.0
                elif actor_base_name in candidate_name:
                    score = len(actor_base_name) / len(candidate_name) * 0.9
                elif candidate_name in actor_base_name:
                    score = len(candidate_name) / len(actor_base_name) * 0.9
            else:
                if actor_without_prefix == candidate_name:
                    score = 0.8
                elif actor_without_prefix in candidate_name:
                    score = len(actor_without_prefix) / len(candidate_name) * 0.7
                elif candidate_name in actor_without_prefix:
                    score = len(candidate_name) / len(actor_without_prefix) * 0.7
            
            # 如果仍未匹配，尝试部分匹配
            if score == 0:
                clean_actor = ''.join(actor_base_name.split())
                clean_candidate = ''.join(candidate_name.split())
                
                if clean_actor in clean_candidate:
                    score = len(clean_actor) / len(clean_candidate) * 0.8
                elif clean_candidate in clean_actor:
                    score = len(clean_candidate) / len(clean_actor) * 0.8
                else:
                    parts = actor_base_name.split()
                    for part in parts:
                        if part in candidate_name and len(part) > 1:
                            part_score = len(part) / len(candidate_name) * 0.7
                            score = max(score, part_score)
            
            # 显示高匹配度的结果
            if score > 0.5:
                self.log(f"候选匹配: '{name}' => '{candidate_name}' (匹配度: {score:.2f})")
            
            # 更新最佳匹配
            if score > best_match_score:
                best_match_score = score
                best_match = name
                best_match_id = id
        
        # 有一定匹配度的结果
        if best_match_score > 0.5:
            self.log(f"✓ 找到最佳匹配: '{best_match}' (匹配度: {best_match_score:.2f})")
            return best_match, best_match_id, True
        
        # 没有找到匹配
        return None, None, False 

    async def clone_voice_async(self, text, reference_id, output_file):
        """
        使用Fish Audio API进行声音克隆（异步版本）
        
        Args:
            text (str): 需要转换为语音的文本
            reference_id (str): 声音ID
            output_file (str): 输出文件路径
        
        Returns:
            bool: 是否成功克隆
        """
        try:
            # 获取配置参数
            api_endpoint = self.config_manager.get("api_endpoint", "https://api.fish.audio/v1/tts")
            api_key = self.config_manager.get("api_key", config.API_KEY)
            model = self.config_manager.get("model", "speech-1.6")
            audio_format = self.config_manager.get("audio_format", config.AUDIO_FORMAT)
            mp3_bitrate = self.config_manager.get("mp3_bitrate", config.MP3_BITRATE)
            chunk_length = self.config_manager.get("chunk_length", 200)
            normalize = self.config_manager.get("normalize", True)
            latency = self.config_manager.get("latency", "normal")
            
            # 记录使用的模型版本
            self.log(f"使用模型: {model}")
            
            # 创建请求数据
            request_data = {
                "text": text,
                "reference_id": reference_id,
                "format": audio_format,
                "mp3_bitrate": mp3_bitrate,
                "chunk_length": chunk_length,
                "normalize": normalize,
                "latency": latency
            }
            
            # 发起异步API请求
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    api_endpoint,
                    json=request_data,
                    headers={
                        "authorization": f"Bearer {api_key}",
                        "content-type": "application/json",
                        "model": model,
                    },
                    timeout=None,
                )
                
                if response.status_code != 200:
                    self.log(f"API请求失败，状态码: {response.status_code}")
                    self.log(f"错误信息: {response.text}")
                    return False
                
                # 保存音频数据
                with open(output_file, "wb") as f:
                    f.write(response.content)
            
            return True
        except Exception as e:
            self.log(f"声音克隆时出错: {str(e)}")
            return False

    def clone_voice(self, text, reference_id, output_file):
        """
        使用Fish Audio API进行声音克隆（同步版本）
        
        Args:
            text (str): 需要转换为语音的文本
            reference_id (str): 声音ID
            output_file (str): 输出文件路径
        
        Returns:
            bool: 是否成功克隆
        """
        try:
            # 获取配置参数
            api_endpoint = self.config_manager.get("api_endpoint", "https://api.fish.audio/v1/tts")
            api_key = self.config_manager.get("api_key", config.API_KEY)
            model = self.config_manager.get("model", "speech-1.6")
            audio_format = self.config_manager.get("audio_format", config.AUDIO_FORMAT)
            mp3_bitrate = self.config_manager.get("mp3_bitrate", config.MP3_BITRATE)
            chunk_length = self.config_manager.get("chunk_length", 200)
            normalize = self.config_manager.get("normalize", True)
            latency = self.config_manager.get("latency", "normal")
            
            # 记录使用的模型版本
            self.log(f"使用模型: {model}")
            
            # 创建请求数据
            request_data = {
                "text": text,
                "reference_id": reference_id,
                "format": audio_format,
                "mp3_bitrate": mp3_bitrate,
                "chunk_length": chunk_length,
                "normalize": normalize,
                "latency": latency
            }
            
            # 发起API请求
            with httpx.Client() as client, open(output_file, "wb") as f:
                response = client.post(
                    api_endpoint,
                    json=request_data,
                    headers={
                        "authorization": f"Bearer {api_key}",
                        "content-type": "application/json",
                        "model": model,
                    },
                    timeout=None,
                )
                
                if response.status_code != 200:
                    self.log(f"API请求失败，状态码: {response.status_code}")
                    self.log(f"错误信息: {response.text}")
                    return False
                
                # 保存音频数据
                f.write(response.content)
            
            return True
        except Exception as e:
            self.log(f"声音克隆时出错: {str(e)}")
            return False

    async def process_script_item(self, index, row):
        """
        处理单个文案项目（异步版本）
        
        Args:
            index (int): 行索引
            row (pandas.Series): 文案数据行
        
        Returns:
            tuple: (索引, 状态, 生成时间)
        """
        # 跳过已完成的项目
        if row[config.STATUS_COLUMN] == config.COMPLETED_STATUS:
            self.log(f"跳过第 {index+1} 行: {row[config.ACTOR_COLUMN]} - 完成状态为已完成")
            return index, row[config.STATUS_COLUMN], row.get(config.GENERATION_TIME_COLUMN, "")
        
        # 获取文案和演员
        text = row[config.SCRIPT_TEXT_COLUMN]
        actor = row[config.ACTOR_COLUMN]
        
        # 获取其他字段
        命名 = str(row[config.NAMING_COLUMN]) if pd.notna(row[config.NAMING_COLUMN]) else f"命名_{index+1}"
        工单号 = str(row[config.WORK_ORDER_COLUMN]) if pd.notna(row[config.WORK_ORDER_COLUMN]) else ""
        副单号 = str(row[config.SECONDARY_ORDER_COLUMN]) if pd.notna(row[config.SECONDARY_ORDER_COLUMN]) else ""
        素材ID = str(row[config.MATERIAL_ID_COLUMN]) if pd.notna(row[config.MATERIAL_ID_COLUMN]) else ""
        
        # 清理和验证字段
        if 工单号 == "nan" or 工单号.lower() == "none":
            工单号 = ""
        if 副单号 == "nan" or 副单号.lower() == "none":
            副单号 = ""
        if 素材ID == "nan" or 素材ID.lower() == "none":
            素材ID = ""
        
        # 检查文案和演员是否有效
        if pd.isna(text) or pd.isna(actor):
            self.log(f"警告: 第 {index+2} 行文案或演员为空，跳过")
            self.signals.item_status_changed.emit(index, "演员形象为空", "")
            return index, "演员形象为空", ""
        
        # 将演员形象转换为字符串以确保安全
        actor = str(actor).strip()
        
        # 详细输出当前处理的数据
        self.log(f"\n==== 处理第 {index+1} 行 ====")
        self.log(f"命名: {命名}")
        self.log(f"工单号: {工单号}")
        if 副单号:
            self.log(f"副单号: {副单号}")
        self.log(f"演员形象: {actor}")
        self.log(f"克隆文案: {text[:50]}..." if len(str(text)) > 50 else f"克隆文案: {text}")
        
        # 显示当前使用的模型
        model = self.config_manager.get("model", "speech-1.6")
        self.log(f"当前配置的模型: {model}")
        
        # 发送进度信号
        self.signals.progress.emit(index, len(self.scripts_df), "处理中")
        
        # 查找匹配的演员和声音ID
        found_actor, reference_id, found = self.find_matching_actor(actor)
        
        if not found:
            self.log(f"❌ 未找到演员 '{actor}' 的声音ID，跳过")
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.signals.item_status_changed.emit(index, "未搜到声音", current_time)
            return index, "未搜到声音", current_time
        
        # 直接使用下载文件夹，不再按匹配人分类
        save_folder = self.download_folder
        
        # 构建新的文件名
        # 提取命名列第一个'-'前的内容
        name_prefix = self.extract_prefix(命名)

        # 构建文件名部分
        filename_parts = []

        # 添加命名前缀
        if name_prefix:
            filename_parts.append(name_prefix)

        # 添加工单号
        if 工单号:
            filename_parts.append(工单号)

        # 添加副单号（如果有）
        if 副单号:
            filename_parts.append(副单号)

        # 添加素材ID
        if 素材ID:
            filename_parts.append(素材ID)
        
        # 用'-'连接各部分
        audio_format = self.config_manager.get("audio_format", config.AUDIO_FORMAT)
        new_filename = "-".join(filename_parts) + f".{audio_format}"
        
        # 如果文件名为空（极端情况），使用命名作为备用
        if not filename_parts:
            命名_格式化 = 命名
            if 命名_格式化.endswith("-"):
                命名_格式化 = 命名_格式化[:-1]  # 删除末尾的连字符
            new_filename = f"{命名_格式化}.{audio_format}"
        
        # 完整输出文件路径
        output_file = os.path.join(save_folder, new_filename)
        
        self.log(f"输出文件名: {new_filename}")
        self.log(f"正在处理: '{text}' 使用 '{found_actor}' 的声音")
        
        # 执行声音克隆（异步）
        success = await self.clone_voice_async(text, reference_id, output_file)
        
        # 更新状态
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        if success:
            self.log(f"成功: 已保存到 {output_file}")
            self.log(f"记录生成时间: {current_time}")
            self.signals.item_status_changed.emit(index, config.COMPLETED_STATUS, current_time)
            return index, config.COMPLETED_STATUS, current_time
        else:
            self.log(f"失败: 无法克隆 '{text}'")
            self.signals.item_status_changed.emit(index, "生成失败", current_time)
            return index, "生成失败", current_time

    async def process_scripts_async(self):
        """
        并发处理所有文案并进行声音克隆（异步版本）
        """
        if not self.scripts_df is not None or self.scripts_df.empty:
            self.log("错误: 没有可处理的文案数据")
            self.signals.process_finished.emit(False, "没有可处理的文案数据")
            return
        
        if not self.voice_id_map:
            self.log("错误: 没有加载声音ID数据")
            self.signals.process_finished.emit(False, "没有加载声音ID数据")
            return
        
        if not self.download_folder:
            # 如果没有设置下载文件夹，获取或创建一个
            self.download_folder = self.config_manager.get_or_create_download_folder()
            self.log(f"自动创建下载文件夹: {self.download_folder}")
        
        # 获取最大并发数
        max_concurrent_tasks = self.config_manager.get("max_concurrent_tasks", 3)
        self.log(f"使用最大并发数: {max_concurrent_tasks}")
        
        # 创建任务列表
        tasks = []
        for index, row in self.scripts_df.iterrows():
            task = self.process_script_item(index, row)
            tasks.append(task)
        
        # 使用信号量限制并发数量
        semaphore = asyncio.Semaphore(max_concurrent_tasks)
        
        async def bounded_process(task):
            async with semaphore:
                return await task
        
        # 绑定信号量到每个任务
        bounded_tasks = [bounded_process(task) for task in tasks]
        
        # 处理任务并更新进度
        results = []
        try:
            for i, f in enumerate(asyncio.as_completed(bounded_tasks)):
                result = await f
                results.append(result)
                # 更新进度
                self.signals.progress.emit(i+1, len(bounded_tasks), "处理中")
        except Exception as e:
            self.log(f"处理过程中出错: {e}")
            self.signals.process_finished.emit(False, f"处理过程中出错: {e}")
            return
        
        # 更新DataFrame
        for index, status, generation_time in results:
            self.scripts_df.at[index, config.STATUS_COLUMN] = status
            if generation_time:
                self.scripts_df.at[index, config.GENERATION_TIME_COLUMN] = generation_time
        
        # 尝试保存结果
        try:
            self.save_scripts()
            self.signals.process_finished.emit(True, f"成功处理 {len(results)} 个项目")
        except Exception as e:
            self.log(f"保存结果时出错: {e}")
            self.signals.process_finished.emit(False, f"保存结果时出错: {e}")

    def save_scripts(self, output_file=None):
        """
        保存更新后的文案数据到Excel文件
        
        Args:
            output_file (str, optional): 输出文件路径，如果为None则使用原始文件
        """
        if self.scripts_df is None:
            self.log("错误: 没有要保存的文案数据")
            return False
        
        try:
            # 如果没有指定输出文件，则使用每日文案文件
            if output_file is None:
                date_today = datetime.now().strftime("%Y%m%d")
                # 确保current_work_folder不为None
                if self.current_work_folder is None:
                    # 如果没有设置工作文件夹，使用配置管理器创建
                    folders = self.config_manager.create_daily_work_folder()
                    self.current_work_folder = folders["main_folder"]
                output_file = os.path.join(self.current_work_folder, f"文案_{date_today}.xlsx")
            
            # 即使DataFrame为空也保存表头
            self.scripts_df.to_excel(output_file, index=False)
            
            # 添加日志提示
            if self.scripts_df.empty:
                self.log(f"已保存空表（仅表头）到 {output_file}")
            else:
                self.log(f"已更新文案状态到 {output_file}")
            return True
        except Exception as e:
            self.log(f"保存文案时出错: {str(e)}")
            return False

    def append_scripts(self, new_df):
        """
        将新的文案数据追加到现有数据中
        
        Args:
            new_df (pandas.DataFrame): 新的文案数据
            
        Returns:
            bool: 是否成功追加
        """
        if new_df is None or new_df.empty:
            self.log("没有新的文案数据可追加")
            return False
        
        try:
            # 如果当前没有文案数据，直接使用新数据
            if self.scripts_df is None or self.scripts_df.empty:
                self.scripts_df = new_df.copy()
                self.log(f"已加载 {len(new_df)} 条文案数据")
            else:
                # 记录原始行数
                original_rows = len(self.scripts_df)
                
                # 追加新数据
                self.scripts_df = pd.concat([self.scripts_df, new_df], ignore_index=True)
                
                # 计算新增加的行数
                added_rows = len(self.scripts_df) - original_rows
                self.log(f"已追加 {added_rows} 条新文案数据")
            
            # 保存更新后的文件
            self.save_scripts()
            return True
        except Exception as e:
            self.log(f"追加文案数据时出错: {e}")
            return False

    def create_daily_task(self, create_download_folder=True):
        """
        创建今日任务环境，返回相关文件夹信息
        
        Args:
            create_download_folder: 是否创建配音文件下载文件夹
            
        Returns:
            dict: 包含主文件夹、下载文件夹和日常Excel文件路径
        """
        # 设置环境
        self.setup_environment()
        
        # 获取当前日期
        date_today = datetime.now().strftime("%Y%m%d")
        
        # 创建以当天日期命名的主文件夹
        main_folder = f"生成结果_{date_today}"
        os.makedirs(main_folder, exist_ok=True)
        
        # 创建带时间戳的下载文件夹（作为子文件夹）
        download_folder = None
        if create_download_folder:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            download_folder = os.path.join(main_folder, f"配音文件_{timestamp}")
            os.makedirs(download_folder, exist_ok=True)
            self.log(f"创建下载文件夹: {download_folder}")
        
        # 创建当天的文案Excel文件路径
        daily_excel = os.path.join(main_folder, f"文案_{date_today}.xlsx")
        
        # 加载当天的文案文件（如果存在）
        if os.path.exists(daily_excel):
            try:
                self.scripts_df = pd.read_excel(daily_excel)
                self.log(f"已加载今日文案文件，共 {len(self.scripts_df)} 条数据")
            except Exception as e:
                self.log(f"加载今日文案文件失败: {e}")
                # 创建空的DataFrame
                self.scripts_df = pd.DataFrame(columns=[
                    config.NAMING_COLUMN, 
                    config.WORK_ORDER_COLUMN,
                    config.SECONDARY_ORDER_COLUMN,
                    config.ACTOR_COLUMN,
                    config.SCRIPT_TEXT_COLUMN,
                    config.MATCHER_COLUMN,
                    config.MATERIAL_ID_COLUMN,
                    config.STATUS_COLUMN,
                    config.GENERATION_TIME_COLUMN
                ])
        else:
            # 创建空的DataFrame
            self.scripts_df = pd.DataFrame(columns=[
                config.NAMING_COLUMN, 
                config.WORK_ORDER_COLUMN,
                config.SECONDARY_ORDER_COLUMN,
                config.ACTOR_COLUMN,
                config.SCRIPT_TEXT_COLUMN,
                config.MATCHER_COLUMN,
                config.MATERIAL_ID_COLUMN,
                config.STATUS_COLUMN,
                config.GENERATION_TIME_COLUMN
            ])
            
            # 保存空白Excel文件
            self.save_scripts(daily_excel)
            self.log(f"已创建今日文案文件")
        
        # 保存文件夹信息到配置
        self.config_manager.set("last_work_date", date_today)
        self.config_manager.set("last_work_folder", main_folder)
        
        # 确保下载文件夹存在
        self.download_folder = download_folder
        
        # 返回文件夹信息
        return {
            "main_folder": main_folder,
            "download_folder": download_folder,
            "daily_excel": daily_excel
        }

    def start_processing(self):
        """启动处理线程"""
        if self.is_running:
            self.log("已有处理任务正在运行")
            return False
        
        # 创建线程
        self.processing_thread = ProcessingThread(self)
        
        # 连接信号
        self.processing_thread.finished.connect(self.on_processing_finished)
        
        # 启动线程
        self.processing_thread.start()
        self.is_running = True
        
        return True
    
    def on_processing_finished(self):
        """处理完成回调"""
        self.is_running = False
        self.processing_thread = None
        
    def get_columns(self):
        """
        获取表格列名列表
        
        Returns:
            list: 包含所有必要列名的列表
        """
        return [
            config.NAMING_COLUMN, 
            config.WORK_ORDER_COLUMN,
            config.SECONDARY_ORDER_COLUMN,
            config.ACTOR_COLUMN,
            config.SCRIPT_TEXT_COLUMN,
            config.MATCHER_COLUMN,
            config.MATERIAL_ID_COLUMN,
            config.STATUS_COLUMN,
            config.GENERATION_TIME_COLUMN
        ]

class ProcessingThread(QThread):
    """处理线程类，用于在后台执行处理任务"""
    
    def __init__(self, processor):
        """初始化处理线程"""
        super().__init__()
        self.processor = processor
    
    def run(self):
        """线程运行函数"""
        # 创建新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # 运行异步处理函数
            loop.run_until_complete(self.processor.process_scripts_async())
        finally:
            # 关闭事件循环
            loop.close()