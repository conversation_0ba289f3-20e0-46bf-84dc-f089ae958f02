"""
调试路径问题
"""

import os
import sys

# 添加src路径
sys.path.append('src')


def debug_path_issue():
    """调试路径问题"""
    print("=" * 60)
    print("🔍 调试路径问题")
    print("=" * 60)
    
    print("📂 当前环境信息:")
    print(f"  当前工作目录: {os.getcwd()}")
    print(f"  Python可执行文件: {sys.executable}")
    print(f"  脚本所在目录: {os.path.dirname(os.path.abspath(__file__))}")
    print("")
    
    print("🔧 路径构建测试:")
    current_dir = os.getcwd()
    
    # 测试不同的路径构建方式
    paths_to_test = [
        "src/video_management_runner.py",  # 相对路径
        os.path.join(current_dir, "src", "video_management_runner.py"),  # 绝对路径
        os.path.join("src", "video_management_runner.py"),  # 相对路径join
        os.path.abspath("src/video_management_runner.py"),  # 绝对路径
    ]
    
    for i, path in enumerate(paths_to_test, 1):
        exists = os.path.exists(path)
        print(f"  {i}. {path}")
        print(f"     存在: {exists}")
        if exists:
            print(f"     大小: {os.path.getsize(path)} 字节")
        print("")
    
    print("❌ 错误路径分析:")
    error_path = "d:\\project\\guangliu02\\src\\src\\video_management_runner.py"
    print(f"  错误路径: {error_path}")
    print(f"  问题: 路径中有两个'src'")
    print("")
    
    # 分析可能的原因
    print("🔍 可能的原因:")
    print("  1. 程序没有重启，还在使用旧代码")
    print("  2. 程序运行在src目录下，导致路径拼接错误")
    print("  3. 有其他地方的代码没有修复")
    print("  4. Python模块缓存问题")
    print("")
    
    # 检查程序运行目录
    print("📁 程序运行目录检查:")
    if os.getcwd().endswith('src'):
        print("  ⚠️ 程序运行在src目录下!")
        print("  这会导致路径拼接错误")
        print("  解决方案: 确保程序从项目根目录启动")
    else:
        print("  ✅ 程序运行在项目根目录")
    print("")
    
    print("🔧 解决方案:")
    print("  1. 确保程序完全重启")
    print("  2. 检查程序启动目录")
    print("  3. 使用更安全的路径构建方式")
    print("  4. 添加路径验证")
    print("")


def create_safe_path_solution():
    """创建安全的路径解决方案"""
    print("=" * 40)
    print("🛡️ 安全路径解决方案")
    print("=" * 40)
    
    print("建议的路径构建代码:")
    print("""
def get_script_path():
    \"\"\"获取安全的脚本路径\"\"\"
    # 获取当前工作目录
    current_dir = os.getcwd()
    
    # 确保我们在项目根目录
    if current_dir.endswith('src'):
        # 如果在src目录，向上一级
        project_root = os.path.dirname(current_dir)
    else:
        project_root = current_dir
    
    # 构建脚本路径
    script_path = os.path.join(project_root, "src", "video_management_runner.py")
    
    # 验证路径存在
    if not os.path.exists(script_path):
        raise FileNotFoundError(f"脚本文件不存在: {script_path}")
    
    return script_path
""")
    print("")
    
    print("使用示例:")
    print("""
try:
    script_path = get_script_path()
    cmd = [sys.executable, script_path]
    print(f"使用脚本路径: {script_path}")
except FileNotFoundError as e:
    print(f"错误: {e}")
""")


def test_current_directory():
    """测试当前目录情况"""
    print("\n" + "=" * 40)
    print("📍 当前目录测试")
    print("=" * 40)
    
    cwd = os.getcwd()
    print(f"当前工作目录: {cwd}")
    
    # 检查是否在src目录
    if cwd.endswith('src'):
        print("⚠️ 检测到程序运行在src目录!")
        print("这解释了为什么会有src/src/的路径问题")
        
        # 测试向上一级
        parent_dir = os.path.dirname(cwd)
        print(f"父目录: {parent_dir}")
        
        # 检查父目录中的脚本
        parent_script = os.path.join(parent_dir, "src", "video_management_runner.py")
        print(f"父目录脚本路径: {parent_script}")
        print(f"父目录脚本存在: {os.path.exists(parent_script)}")
    else:
        print("✅ 程序运行在正确的目录")
        
        # 检查正常路径
        normal_script = os.path.join(cwd, "src", "video_management_runner.py")
        print(f"正常脚本路径: {normal_script}")
        print(f"正常脚本存在: {os.path.exists(normal_script)}")


def create_immediate_fix():
    """创建立即修复方案"""
    print("\n" + "=" * 40)
    print("🚨 立即修复方案")
    print("=" * 40)
    
    print("立即修复代码 (替换main_window.py中的路径构建):")
    print("""
# 替换这部分代码:
# script_path = os.path.join(current_dir, "src", "video_management_runner.py")

# 改为:
if current_dir.endswith('src'):
    # 如果程序运行在src目录，向上一级
    project_root = os.path.dirname(current_dir)
else:
    project_root = current_dir

script_path = os.path.join(project_root, "src", "video_management_runner.py")

# 验证路径
if not os.path.exists(script_path):
    self.append_vm_log(f"错误: 脚本文件不存在: {script_path}")
    return

self.append_vm_log(f"使用脚本路径: {script_path}")
""")
    print("")
    
    print("或者更简单的修复:")
    print("""
# 直接使用绝对路径，避免拼接错误
script_path = os.path.abspath("src/video_management_runner.py")
""")


if __name__ == "__main__":
    debug_path_issue()
    create_safe_path_solution()
    test_current_directory()
    create_immediate_fix()
