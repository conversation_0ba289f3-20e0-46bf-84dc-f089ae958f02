#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
飞影数字人API客户端
"""

import requests
import time
from typing import Optional, Dict, Any
from PySide6.QtCore import QObject, Signal


class HiflyClient(QObject):
    """飞影数字人API客户端"""
    
    # 信号定义
    log_message = Signal(str)
    upload_progress = Signal(str, int, int)  # 任务名称, 当前进度, 总数
    upload_completed = Signal(str, bool, str)  # 任务名称, 是否成功, 消息
    
    def __init__(self, token: str):
        super().__init__()
        self.token = token
        self.base_url = "https://hfw-api.hifly.cc"
        self.headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
    
    def create_avatar_by_video(self, title: str, video_url: str) -> Optional[Dict[str, Any]]:
        """
        创建视频数字人
        
        Args:
            title: 数字人名称
            video_url: 视频URL地址
            
        Returns:
            API响应结果，包含task_id等信息
        """
        try:
            url = f"{self.base_url}/api/v2/hifly/avatar/create_by_video"
            
            payload = {
                "title": title,
                "video_url": video_url
            }
            
            self.log_message.emit(f"🚀 开始创建数字人: {title}")
            self.log_message.emit(f"📹 视频URL: {video_url}")
            
            response = requests.post(url, headers=self.headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    task_id = result.get("task_id")
                    self.log_message.emit(f"✅ 数字人创建任务提交成功，任务ID: {task_id}")
                    return result
                else:
                    error_msg = result.get("message", "未知错误")
                    self.log_message.emit(f"❌ 数字人创建失败: {error_msg}")
                    return None
            else:
                self.log_message.emit(f"❌ API请求失败，状态码: {response.status_code}")
                return None
                
        except requests.exceptions.Timeout:
            self.log_message.emit("❌ 请求超时，请检查网络连接")
            return None
        except requests.exceptions.RequestException as e:
            self.log_message.emit(f"❌ 网络请求异常: {str(e)}")
            return None
        except Exception as e:
            self.log_message.emit(f"❌ 创建数字人异常: {str(e)}")
            return None
    
    def check_avatar_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        查询数字人创建任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态信息
        """
        try:
            url = f"{self.base_url}/api/v2/hifly/avatar/task"
            params = {"task_id": task_id}
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    return result
                else:
                    error_msg = result.get("message", "未知错误")
                    self.log_message.emit(f"❌ 查询任务状态失败: {error_msg}")
                    return None
            else:
                self.log_message.emit(f"❌ 查询任务状态失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            self.log_message.emit(f"❌ 查询任务状态异常: {str(e)}")
            return None
    
    def wait_for_avatar_completion(self, task_id: str, title: str, max_wait_minutes: int = 30) -> bool:
        """
        等待数字人创建完成
        
        Args:
            task_id: 任务ID
            title: 数字人名称（用于日志显示）
            max_wait_minutes: 最大等待时间（分钟）
            
        Returns:
            是否创建成功
        """
        try:
            start_time = time.time()
            max_wait_seconds = max_wait_minutes * 60
            check_interval = 10  # 每10秒检查一次
            
            self.log_message.emit(f"⏳ 等待数字人创建完成: {title}")
            
            while True:
                # 检查是否超时
                elapsed_time = time.time() - start_time
                if elapsed_time > max_wait_seconds:
                    self.log_message.emit(f"⏰ 等待超时（{max_wait_minutes}分钟），停止检查: {title}")
                    return False
                
                # 查询任务状态
                status_result = self.check_avatar_task_status(task_id)
                if status_result is None:
                    self.log_message.emit(f"❌ 无法查询任务状态: {title}")
                    return False
                
                status = status_result.get("status")
                avatar_id = status_result.get("avatar")
                
                if status == 3:  # 完成
                    self.log_message.emit(f"✅ 数字人创建完成: {title}")
                    self.log_message.emit(f"🎭 数字人ID: {avatar_id}")
                    return True
                elif status == 4:  # 失败
                    error_msg = status_result.get("message", "创建失败")
                    self.log_message.emit(f"❌ 数字人创建失败: {title} - {error_msg}")
                    return False
                elif status in [1, 2]:  # 等待中或处理中
                    status_text = "等待中" if status == 1 else "处理中"
                    elapsed_minutes = int(elapsed_time // 60)
                    self.log_message.emit(f"⏳ {status_text}: {title} (已等待 {elapsed_minutes} 分钟)")
                    
                    # 等待下次检查
                    time.sleep(check_interval)
                else:
                    self.log_message.emit(f"❓ 未知状态 {status}: {title}")
                    time.sleep(check_interval)
                    
        except Exception as e:
            self.log_message.emit(f"❌ 等待数字人创建异常: {title} - {str(e)}")
            return False
    
    def upload_avatar_from_video_data(self, video_data: Dict[str, Any]) -> bool:
        """
        从视频数据上传数字人
        
        Args:
            video_data: 包含视频信息的字典，需要包含：
                - ID: 素材ID
                - 视频URL: 视频链接
                - 拍摄演员名称: 演员名称
                
        Returns:
            是否上传成功
        """
        try:
            # 提取必要信息
            video_id = video_data.get("ID")
            video_url = video_data.get("视频URL")
            actor_name = video_data.get("拍摄演员名称")
            
            if not all([video_id, video_url, actor_name]):
                self.log_message.emit(f"❌ 视频数据不完整: ID={video_id}, URL={video_url}, 演员={actor_name}")
                return False
            
            # 构建数字人名称：演员名称-ID-演员
            title = f"{actor_name}-{video_id}-演员"
            
            # 创建数字人
            result = self.create_avatar_by_video(title, video_url)
            if result is None:
                return False
            
            task_id = result.get("task_id")
            if not task_id:
                self.log_message.emit(f"❌ 未获取到任务ID: {title}")
                return False
            
            # 等待创建完成
            success = self.wait_for_avatar_completion(task_id, title)
            
            if success:
                self.log_message.emit(f"🎉 数字人上传成功: {title}")
            else:
                self.log_message.emit(f"💔 数字人上传失败: {title}")
            
            return success
            
        except Exception as e:
            self.log_message.emit(f"❌ 上传数字人异常: {str(e)}")
            return False
    
    def test_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            连接是否成功
        """
        try:
            url = f"{self.base_url}/api/v2/hifly/account/credit"
            
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    credit = result.get("left", 0)
                    self.log_message.emit(f"✅ 飞影API连接成功，剩余积分: {credit}")
                    return True
                else:
                    error_msg = result.get("message", "未知错误")
                    self.log_message.emit(f"❌ 飞影API认证失败: {error_msg}")
                    return False
            elif response.status_code == 401:
                self.log_message.emit("❌ 飞影API Token无效，请检查Token是否正确")
                return False
            else:
                self.log_message.emit(f"❌ 飞影API连接失败，状态码: {response.status_code}")
                return False
                
        except requests.exceptions.Timeout:
            self.log_message.emit("❌ 飞影API连接超时，请检查网络连接")
            return False
        except requests.exceptions.RequestException as e:
            self.log_message.emit(f"❌ 飞影API网络异常: {str(e)}")
            return False
        except Exception as e:
            self.log_message.emit(f"❌ 飞影API测试异常: {str(e)}")
            return False
