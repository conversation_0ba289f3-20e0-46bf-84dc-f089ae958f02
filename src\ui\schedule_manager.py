"""
定时任务管理器
支持程序内定时和Windows系统定时的创建、编辑、删除和管理
"""

import os
import sys
import json
import subprocess
from datetime import datetime, time
from PySide6.QtCore import Qt, QTimer, Signal, QObject, QTime
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QTableWidget, QTableWidgetItem, QPushButton, QLabel,
    QTimeEdit, QSpinBox, QCheckBox, QLineEdit, QTextEdit,
    QMessageBox, QHeaderView, QComboBox, QGroupBox,
    QFormLayout, QDialogButtonBox
)
from PySide6.QtGui import QIcon, QTextCursor


class ScheduleTask:
    """定时任务数据类"""
    def __init__(self, task_id, name, task_type, start_time=None, interval_minutes=0,
                 enabled=True, description="", days_of_week=None, duration_hours=0):
        self.task_id = task_id
        self.name = name
        self.task_type = task_type  # "internal" 或 "system"
        self.start_time = start_time  # QTime对象
        self.interval_minutes = interval_minutes
        self.duration_hours = duration_hours  # 持续时间（小时），0表示无限制
        self.enabled = enabled
        self.description = description
        self.days_of_week = days_of_week or [True] * 7  # 周一到周日
        self.created_time = datetime.now()
        self.last_run = None
        
    def to_dict(self):
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'name': self.name,
            'task_type': self.task_type,
            'start_time': self.start_time.toString() if self.start_time else None,
            'interval_minutes': self.interval_minutes,
            'duration_hours': self.duration_hours,
            'enabled': self.enabled,
            'description': self.description,
            'days_of_week': self.days_of_week,
            'created_time': self.created_time.isoformat(),
            'last_run': self.last_run.isoformat() if self.last_run else None
        }
    
    @classmethod
    def from_dict(cls, data):
        """从字典创建"""
        from PySide6.QtCore import QTime
        task = cls(
            task_id=data['task_id'],
            name=data['name'],
            task_type=data['task_type'],
            interval_minutes=data.get('interval_minutes', 0),
            duration_hours=data.get('duration_hours', 0),
            enabled=data.get('enabled', True),
            description=data.get('description', ''),
            days_of_week=data.get('days_of_week', [True] * 7)
        )
        
        if data.get('start_time'):
            task.start_time = QTime.fromString(data['start_time'])
        
        if data.get('created_time'):
            task.created_time = datetime.fromisoformat(data['created_time'])
            
        if data.get('last_run'):
            task.last_run = datetime.fromisoformat(data['last_run'])
            
        return task


class ScheduleManager(QObject):
    """定时任务管理器"""
    
    # 信号定义
    task_triggered = Signal(str)  # 任务触发信号
    log_message = Signal(str)     # 日志消息信号
    
    def __init__(self):
        super().__init__()
        self.tasks = {}  # 存储所有任务
        self.internal_timers = {}  # 程序内定时器
        self.stop_timers = {}  # 停止定时器（用于持续时间控制）
        self.config_file = "schedule_config.json"

        # 支持的任务类型
        self.supported_tasks = {
            'digital_human': '数字人自动上传',
            'video_management': '视频管理自动化',
            'material_update': '素材更新',
            'hifly_upload': '飞影上传',
            'auto_rename': '自动重命名'
        }

        self.load_tasks()
        
    def add_task(self, task):
        """添加任务"""
        self.tasks[task.task_id] = task
        self.save_tasks()
        
        if task.task_type == "internal" and task.enabled:
            self.start_internal_timer(task)
        elif task.task_type == "system" and task.enabled:
            self.create_system_task(task)
            
    def remove_task(self, task_id):
        """删除任务"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            
            # 停止程序内定时器
            if task_id in self.internal_timers:
                self.internal_timers[task_id].stop()
                del self.internal_timers[task_id]
            
            # 删除系统任务
            if task.task_type == "system":
                self.delete_system_task(task)
            
            del self.tasks[task_id]
            self.save_tasks()
            
    def update_task(self, task):
        """更新任务"""
        old_task = self.tasks.get(task.task_id)
        if old_task:
            # 如果是程序内任务，先停止旧的定时器
            if old_task.task_type == "internal" and task.task_id in self.internal_timers:
                self.internal_timers[task.task_id].stop()
                del self.internal_timers[task.task_id]
            
            # 如果是系统任务，先删除旧的系统任务
            if old_task.task_type == "system":
                self.delete_system_task(old_task)
        
        # 更新任务
        self.tasks[task.task_id] = task
        self.save_tasks()
        
        # 启动新的任务
        if task.task_type == "internal" and task.enabled:
            self.start_internal_timer(task)
        elif task.task_type == "system" and task.enabled:
            self.create_system_task(task)
            
    def start_internal_timer(self, task):
        """启动程序内定时器"""
        if task.task_id in self.internal_timers:
            self.internal_timers[task.task_id].stop()

        if task.task_id in self.stop_timers:
            self.stop_timers[task.task_id].stop()

        timer = QTimer()
        timer.timeout.connect(lambda: self.on_timer_triggered(task.task_id))

        if task.interval_minutes > 0:
            timer.start(task.interval_minutes * 60 * 1000)  # 转换为毫秒
            self.internal_timers[task.task_id] = timer

            # 如果设置了持续时间，创建停止定时器
            duration_hours = getattr(task, 'duration_hours', 0)
            if duration_hours > 0:
                stop_timer = QTimer()
                stop_timer.timeout.connect(lambda: self.stop_internal_timer(task.task_id))
                stop_timer.start(duration_hours * 60 * 60 * 1000)  # 转换为毫秒
                self.stop_timers[task.task_id] = stop_timer
                self.log_message.emit(f"程序内定时任务已启动：{task.name}，间隔 {task.interval_minutes} 分钟，持续 {duration_hours} 小时")
            else:
                self.log_message.emit(f"程序内定时任务已启动：{task.name}，间隔 {task.interval_minutes} 分钟，无限制运行")
            
    def stop_internal_timer(self, task_id):
        """停止程序内定时器（持续时间到达）"""
        if task_id in self.internal_timers:
            self.internal_timers[task_id].stop()
            del self.internal_timers[task_id]

        if task_id in self.stop_timers:
            self.stop_timers[task_id].stop()
            del self.stop_timers[task_id]

        if task_id in self.tasks:
            task = self.tasks[task_id]
            self.log_message.emit(f"定时任务已停止（持续时间结束）：{task.name}")

    def on_timer_triggered(self, task_id):
        """定时器触发处理"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.last_run = datetime.now()
            self.save_tasks()
            self.task_triggered.emit(task_id)
            self.log_message.emit(f"定时任务触发：{task.name}")
            
    def create_system_task(self, task):
        """创建系统定时任务"""
        try:
            current_dir = os.getcwd()
            python_exe = sys.executable

            # 根据任务类型选择不同的脚本和VBS文件
            if hasattr(task, 'script_type') and task.script_type == 'video_management':
                # 视频管理任务
                script_path = os.path.join(current_dir, "run_video_management_auto.bat")
                vbs_path = os.path.join(current_dir, "run_video_management_scheduled.vbs")
                # 创建或更新VBS文件
                self.create_video_management_vbs_file(vbs_path, script_path)
            else:
                # 默认数字人任务
                script_path = os.path.join(current_dir, "digital_human_runner.py")
                vbs_path = os.path.join(current_dir, "run_digital_human_scheduled.vbs")
                # 创建或更新VBS文件
                self.create_vbs_file(vbs_path, python_exe, script_path)

            # 创建系统任务 - 使用用户设置的名称，但添加前缀避免冲突
            task_name = f"FishWin_{task.name}_{task.task_id[:8]}"
            
            # 删除已存在的任务
            try:
                subprocess.run([
                    "schtasks", "/delete", "/tn", task_name, "/f"
                ], capture_output=True, check=False)
            except:
                pass
            
            # 构建创建命令
            cmd = [
                "schtasks", "/create",
                "/tn", task_name,
                "/tr", f'wscript.exe "{vbs_path}"',
                "/sc", "daily",
                "/f"
            ]
            
            # 添加开始时间
            if task.start_time:
                cmd.extend(["/st", task.start_time.toString("HH:mm")])
            
            # 添加重复间隔和持续时间
            if task.interval_minutes > 0:
                duration_hours = getattr(task, 'duration_hours', 0)
                if duration_hours > 0:
                    # 格式化持续时间为 HH:MM 格式
                    duration_str = f"{duration_hours:02d}:00"
                    cmd.extend(["/ri", str(task.interval_minutes), "/du", duration_str])
                else:
                    # 无限制运行
                    cmd.extend(["/ri", str(task.interval_minutes), "/du", "24:00"])
            
            # 添加调试信息
            self.log_message.emit(f"正在创建系统任务：{task_name}")
            self.log_message.emit(f"执行命令：{' '.join(cmd)}")

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                # 尝试启用唤醒功能
                self.enable_system_task_wake(task_name)
                self.log_message.emit(f"系统定时任务已创建：{task.name}")
                self.log_message.emit(f"任务名称：{task_name}")
                return True
            else:
                self.log_message.emit(f"系统任务创建失败：{result.stderr}")
                self.log_message.emit(f"返回码：{result.returncode}")
                if result.stdout:
                    self.log_message.emit(f"输出：{result.stdout}")
                return False
                
        except Exception as e:
            self.log_message.emit(f"创建系统任务时出错：{e}")
            return False
            
    def delete_system_task(self, task):
        """删除系统定时任务"""
        try:
            task_name = f"FishWin_{task.name}_{task.task_id[:8]}"
            result = subprocess.run([
                "schtasks", "/delete", "/tn", task_name, "/f"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_message.emit(f"系统定时任务已删除：{task.name}")

            # 注意：不删除共用的VBS文件，因为其他任务可能还在使用

        except Exception as e:
            self.log_message.emit(f"删除系统任务时出错：{e}")

    def create_vbs_file(self, vbs_path, python_exe, script_path):
        """创建VBS文件 - 完全隐藏运行，不显示任何窗口"""
        # 将python.exe替换为pythonw.exe以实现后台运行
        pythonw_exe = python_exe.replace('python.exe', 'pythonw.exe')

        # 确保路径格式正确，使用Windows路径分隔符
        pythonw_exe = pythonw_exe.replace('/', '\\')
        script_path = script_path.replace('/', '\\')

        # 先创建一个简单的批处理文件
        bat_path = vbs_path.replace('.vbs', '_helper.bat')
        script_dir = os.path.dirname(script_path)
        script_name = os.path.basename(script_path)

        bat_content = f'''@echo off
cd /d "{script_dir}"
"{pythonw_exe}" "{script_name}"
'''
        with open(bat_path, 'w', encoding='gbk') as f:
            f.write(bat_content)

        # VBS调用批处理文件
        vbs_content = f'''Set WshShell = CreateObject("WScript.Shell")
WshShell.Run """{bat_path}""", 0, False
'''
        with open(vbs_path, 'w', encoding='utf-8') as f:
            f.write(vbs_content)

    def create_video_management_vbs_file(self, vbs_path, bat_path):
        """创建视频管理VBS文件 - 完全隐藏运行批处理文件"""
        # 确保路径格式正确，使用Windows路径分隔符
        bat_path = bat_path.replace('/', '\\')

        # VBS调用批处理文件，传递scheduled参数
        vbs_content = f'''Set WshShell = CreateObject("WScript.Shell")
WshShell.Run """{bat_path} scheduled""", 0, False
'''
        with open(vbs_path, 'w', encoding='utf-8') as f:
            f.write(vbs_content)

    def create_batch_file_simple(self, bat_path, python_exe, script_path):
        """创建简单的批处理文件"""
        # 将python.exe替换为pythonw.exe以实现后台运行
        pythonw_exe = python_exe.replace('python.exe', 'pythonw.exe')

        # 使用绝对路径，避免中文路径问题
        bat_content = f'''@echo off
"{pythonw_exe}" "{script_path}"
'''
        with open(bat_path, 'w', encoding='gbk') as f:
            f.write(bat_content)

    def create_batch_file(self, bat_path, python_exe, script_path):
        """创建VBScript包装器 - 完全隐藏窗口运行"""
        # 将python.exe替换为pythonw.exe以实现后台运行
        pythonw_exe = python_exe.replace('python.exe', 'pythonw.exe')

        # 创建VBScript文件来隐藏运行
        vbs_path = bat_path.replace('.bat', '.vbs')
        vbs_content = f'''Set WshShell = CreateObject("WScript.Shell")
WshShell.CurrentDirectory = "{os.getcwd()}"
WshShell.Run """{pythonw_exe}"" ""{script_path}""", 0, False
'''
        with open(vbs_path, 'w', encoding='utf-8') as f:
            f.write(vbs_content)

        # 创建批处理文件调用VBScript
        bat_content = f'''@echo off
cscript //nologo "{vbs_path}"
'''
        with open(bat_path, 'w', encoding='gbk') as f:
            f.write(bat_content)
            
    def enable_system_task_wake(self, task_name):
        """启用系统任务的唤醒功能"""
        try:
            powershell_script = f'''
$task = Get-ScheduledTask -TaskName "{task_name}" -ErrorAction SilentlyContinue
if ($task) {{
    $task.Settings.WakeToRun = $true
    $task.Settings.DisallowStartIfOnBatteries = $false
    $task.Settings.StopIfGoingOnBatteries = $false
    Set-ScheduledTask -InputObject $task
}}
'''
            subprocess.run([
                "powershell", "-Command", powershell_script
            ], capture_output=True, text=True, timeout=30)
        except:
            pass
            
    def save_tasks(self):
        """保存任务配置"""
        try:
            data = {task_id: task.to_dict() for task_id, task in self.tasks.items()}
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log_message.emit(f"保存任务配置失败：{e}")
            
    def load_tasks(self):
        """加载任务配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for task_id, task_data in data.items():
                    task = ScheduleTask.from_dict(task_data)
                    self.tasks[task_id] = task
                    
                    # 重启程序内定时器
                    if task.task_type == "internal" and task.enabled:
                        self.start_internal_timer(task)
                        
        except Exception as e:
            self.log_message.emit(f"加载任务配置失败：{e}")
            
    def get_tasks_by_type(self, task_type):
        """根据类型获取任务"""
        return [task for task in self.tasks.values() if task.task_type == task_type]
        
    def open_task_scheduler(self):
        """打开Windows任务计划程序"""
        try:
            os.system("taskschd.msc")
        except Exception as e:
            self.log_message.emit(f"打开任务计划程序失败：{e}")


class TaskEditDialog(QDialog):
    """任务编辑对话框"""

    def __init__(self, parent=None, task=None, task_type="internal"):
        super().__init__(parent)
        self.task = task
        self.task_type = task_type
        self.setup_ui()

        if task:
            self.load_task_data()

    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("编辑定时任务" if self.task else "新建定时任务")
        self.setModal(True)
        self.resize(400, 500)

        layout = QVBoxLayout(self)

        # 基本信息
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)

        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("任务名称")
        basic_layout.addRow("任务名称:", self.name_edit)

        self.desc_edit = QTextEdit()
        self.desc_edit.setMaximumHeight(60)
        self.desc_edit.setPlaceholderText("任务描述（可选）")
        basic_layout.addRow("描述:", self.desc_edit)

        self.enabled_check = QCheckBox("启用任务")
        self.enabled_check.setChecked(True)
        basic_layout.addRow("", self.enabled_check)

        layout.addWidget(basic_group)

        # 时间设置
        time_group = QGroupBox("时间设置")
        time_layout = QFormLayout(time_group)

        self.start_time_edit = QTimeEdit()
        # 默认为当前时间
        current_time = QTime.currentTime()
        self.start_time_edit.setTime(current_time)
        time_layout.addRow("开始时间:", self.start_time_edit)

        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(1, 1440)  # 1分钟到24小时
        self.interval_spin.setValue(60)  # 默认1小时
        self.interval_spin.setSuffix(" 分钟")
        time_layout.addRow("重复间隔:", self.interval_spin)

        self.duration_spin = QSpinBox()
        self.duration_spin.setRange(0, 24)  # 0小时到24小时，0表示无限制
        self.duration_spin.setValue(0)  # 默认无限制
        self.duration_spin.setSuffix(" 小时")
        self.duration_spin.setSpecialValueText("无限制")
        time_layout.addRow("持续时间:", self.duration_spin)

        layout.addWidget(time_group)

        # 星期设置
        days_group = QGroupBox("执行日期")
        days_layout = QHBoxLayout(days_group)

        self.day_checks = []
        days = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        for day in days:
            check = QCheckBox(day)
            check.setChecked(True)  # 默认每天
            self.day_checks.append(check)
            days_layout.addWidget(check)

        layout.addWidget(days_group)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def load_task_data(self):
        """加载任务数据"""
        if self.task:
            self.name_edit.setText(self.task.name)
            self.desc_edit.setPlainText(self.task.description)
            self.enabled_check.setChecked(self.task.enabled)

            if self.task.start_time:
                self.start_time_edit.setTime(self.task.start_time)

            self.interval_spin.setValue(self.task.interval_minutes)

            # 加载持续时间，如果任务没有这个属性则默认为0
            duration = getattr(self.task, 'duration_hours', 0)
            self.duration_spin.setValue(duration)

            for i, checked in enumerate(self.task.days_of_week):
                if i < len(self.day_checks):
                    self.day_checks[i].setChecked(checked)

    def get_task_data(self):
        """获取任务数据"""
        import uuid
        from PySide6.QtCore import QTime

        task_id = self.task.task_id if self.task else str(uuid.uuid4())

        return ScheduleTask(
            task_id=task_id,
            name=self.name_edit.text().strip(),
            task_type=self.task_type,
            start_time=self.start_time_edit.time(),
            interval_minutes=self.interval_spin.value(),
            duration_hours=self.duration_spin.value(),
            enabled=self.enabled_check.isChecked(),
            description=self.desc_edit.toPlainText().strip(),
            days_of_week=[check.isChecked() for check in self.day_checks]
        )

    def accept(self):
        """确认"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "输入错误", "请输入任务名称")
            return

        super().accept()


class ScheduleManagerDialog(QDialog):
    """定时任务管理对话框"""

    def __init__(self, parent=None, schedule_manager=None):
        super().__init__(parent)
        self.schedule_manager = schedule_manager
        self.setup_ui()
        self.refresh_tables()

        # 连接信号
        if self.schedule_manager:
            self.schedule_manager.log_message.connect(self.append_log)

    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("定时任务管理")
        self.setModal(True)
        self.resize(800, 600)

        layout = QVBoxLayout(self)

        # 标题
        title = QLabel("定时任务管理")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)

        # 选项卡
        self.tab_widget = QTabWidget()

        # 程序内定时选项卡
        self.internal_tab = self.create_internal_tab()
        self.tab_widget.addTab(self.internal_tab, "程序内定时")

        # 系统定时选项卡
        self.system_tab = self.create_system_tab()
        self.tab_widget.addTab(self.system_tab, "系统定时")

        layout.addWidget(self.tab_widget)

        # 日志区域
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        layout.addWidget(log_group)

        # 底部按钮
        button_layout = QHBoxLayout()

        self.open_scheduler_btn = QPushButton("打开任务计划程序")
        self.open_scheduler_btn.clicked.connect(self.open_task_scheduler)
        button_layout.addWidget(self.open_scheduler_btn)

        button_layout.addStretch()

        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

    def create_internal_tab(self):
        """创建程序内定时选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 工具栏
        toolbar = QHBoxLayout()

        add_btn = QPushButton("新建任务")
        add_btn.clicked.connect(lambda: self.add_task("internal"))
        toolbar.addWidget(add_btn)

        edit_btn = QPushButton("编辑任务")
        edit_btn.clicked.connect(lambda: self.edit_task("internal"))
        toolbar.addWidget(edit_btn)

        delete_btn = QPushButton("删除任务")
        delete_btn.clicked.connect(lambda: self.delete_task("internal"))
        toolbar.addWidget(delete_btn)

        toolbar.addStretch()

        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_tables)
        toolbar.addWidget(refresh_btn)

        layout.addLayout(toolbar)

        # 表格
        self.internal_table = QTableWidget()
        self.internal_table.setColumnCount(8)
        self.internal_table.setHorizontalHeaderLabels([
            "序号", "任务名称", "开始时间", "间隔(分钟)", "持续时间", "状态", "最后运行", "描述"
        ])

        # 设置表格属性
        header = self.internal_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.internal_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.internal_table.setAlternatingRowColors(True)

        layout.addWidget(self.internal_table)

        return widget

    def create_system_tab(self):
        """创建系统定时选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 添加说明标签
        info_label = QLabel("⚠️ 注意：手动修改Windows任务计划程序中的设置不会同步到程序中。\n"
                           "如需修改任务参数，请在程序中编辑任务，这会覆盖任务计划程序中的设置。")
        info_label.setStyleSheet("QLabel { color: #ff6b35; background-color: #fff3e0; "
                                "padding: 8px; border-radius: 4px; margin: 5px; }")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 工具栏
        toolbar = QHBoxLayout()

        add_btn = QPushButton("新建任务")
        add_btn.clicked.connect(lambda: self.add_task("system"))
        toolbar.addWidget(add_btn)

        edit_btn = QPushButton("编辑任务")
        edit_btn.clicked.connect(lambda: self.edit_task("system"))
        toolbar.addWidget(edit_btn)

        delete_btn = QPushButton("删除任务")
        delete_btn.clicked.connect(lambda: self.delete_task("system"))
        toolbar.addWidget(delete_btn)

        toolbar.addStretch()

        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_tables)
        toolbar.addWidget(refresh_btn)

        layout.addLayout(toolbar)

        # 表格
        self.system_table = QTableWidget()
        self.system_table.setColumnCount(8)
        self.system_table.setHorizontalHeaderLabels([
            "序号", "任务名称", "开始时间", "间隔(分钟)", "持续时间", "状态", "创建时间", "描述"
        ])

        # 设置表格属性
        header = self.system_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.system_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.system_table.setAlternatingRowColors(True)

        layout.addWidget(self.system_table)

        return widget

    def refresh_tables(self):
        """刷新表格数据"""
        if not self.schedule_manager:
            return

        # 刷新程序内定时表格
        internal_tasks = self.schedule_manager.get_tasks_by_type("internal")
        self.internal_table.setRowCount(len(internal_tasks))

        for row, task in enumerate(internal_tasks):
            self.internal_table.setItem(row, 0, QTableWidgetItem(str(row + 1)))  # 序号
            self.internal_table.setItem(row, 1, QTableWidgetItem(task.name))
            self.internal_table.setItem(row, 2, QTableWidgetItem(
                task.start_time.toString("HH:mm") if task.start_time else "立即"
            ))
            self.internal_table.setItem(row, 3, QTableWidgetItem(str(task.interval_minutes)))

            # 持续时间列
            duration_hours = getattr(task, 'duration_hours', 0)
            duration_text = f"{duration_hours}小时" if duration_hours > 0 else "无限制"
            self.internal_table.setItem(row, 4, QTableWidgetItem(duration_text))

            self.internal_table.setItem(row, 5, QTableWidgetItem("启用" if task.enabled else "禁用"))
            self.internal_table.setItem(row, 6, QTableWidgetItem(
                task.last_run.strftime("%m-%d %H:%M") if task.last_run else "未运行"
            ))
            self.internal_table.setItem(row, 7, QTableWidgetItem(task.description))

            # 存储任务ID到任务名称列
            self.internal_table.item(row, 1).setData(Qt.UserRole, task.task_id)

        # 刷新系统定时表格
        system_tasks = self.schedule_manager.get_tasks_by_type("system")
        self.system_table.setRowCount(len(system_tasks))

        for row, task in enumerate(system_tasks):
            self.system_table.setItem(row, 0, QTableWidgetItem(str(row + 1)))  # 序号
            self.system_table.setItem(row, 1, QTableWidgetItem(task.name))
            self.system_table.setItem(row, 2, QTableWidgetItem(
                task.start_time.toString("HH:mm") if task.start_time else "立即"
            ))
            self.system_table.setItem(row, 3, QTableWidgetItem(str(task.interval_minutes)))

            # 持续时间列
            duration_hours = getattr(task, 'duration_hours', 0)
            duration_text = f"{duration_hours}小时" if duration_hours > 0 else "无限制"
            self.system_table.setItem(row, 4, QTableWidgetItem(duration_text))

            self.system_table.setItem(row, 5, QTableWidgetItem("启用" if task.enabled else "禁用"))
            self.system_table.setItem(row, 6, QTableWidgetItem(
                task.created_time.strftime("%m-%d %H:%M")
            ))
            self.system_table.setItem(row, 7, QTableWidgetItem(task.description))

            # 存储任务ID到任务名称列
            self.system_table.item(row, 1).setData(Qt.UserRole, task.task_id)

    def add_task(self, task_type):
        """添加任务"""
        dialog = TaskEditDialog(self, task_type=task_type)
        if dialog.exec() == QDialog.Accepted:
            task = dialog.get_task_data()
            self.schedule_manager.add_task(task)
            self.refresh_tables()
            self.append_log(f"已添加{task_type}任务：{task.name}")

    def edit_task(self, task_type):
        """编辑任务"""
        table = self.internal_table if task_type == "internal" else self.system_table
        current_row = table.currentRow()

        if current_row < 0:
            QMessageBox.warning(self, "选择错误", "请先选择要编辑的任务")
            return

        task_id = table.item(current_row, 1).data(Qt.UserRole)
        task = self.schedule_manager.tasks.get(task_id)

        if not task:
            QMessageBox.warning(self, "错误", "找不到选中的任务")
            return

        dialog = TaskEditDialog(self, task=task, task_type=task_type)
        if dialog.exec() == QDialog.Accepted:
            updated_task = dialog.get_task_data()
            self.schedule_manager.update_task(updated_task)
            self.refresh_tables()
            self.append_log(f"已更新{task_type}任务：{updated_task.name}")

    def delete_task(self, task_type):
        """删除任务"""
        table = self.internal_table if task_type == "internal" else self.system_table
        current_row = table.currentRow()

        if current_row < 0:
            QMessageBox.warning(self, "选择错误", "请先选择要删除的任务")
            return

        task_id = table.item(current_row, 1).data(Qt.UserRole)
        task = self.schedule_manager.tasks.get(task_id)

        if not task:
            QMessageBox.warning(self, "错误", "找不到选中的任务")
            return

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除任务 '{task.name}' 吗？\n\n"
            f"任务类型：{'程序内定时' if task_type == 'internal' else '系统定时'}\n"
            f"此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.schedule_manager.remove_task(task_id)
            self.refresh_tables()
            self.append_log(f"已删除{task_type}任务：{task.name}")

    def open_task_scheduler(self):
        """打开任务计划程序"""
        if self.schedule_manager:
            self.schedule_manager.open_task_scheduler()
            self.append_log("已打开Windows任务计划程序")

    def append_log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

        # 滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.log_text.setTextCursor(cursor)


class VideoManagementScheduleManager(ScheduleManager):
    """视频管理专用的定时任务管理器"""

    def __init__(self):
        # 调用父类初始化以继承信号和基本功能
        super().__init__()

        # 重新设置独立的配置文件（使用绝对路径）
        import os
        # 获取项目根目录（main.py所在目录）
        current_dir = os.getcwd()
        self.config_file = os.path.join(current_dir, "video_management_schedule_config.json")

        # 支持的任务类型 - 与数字人模块保持一致
        self.supported_tasks = {
            'video_management_auto': '视频管理自动化',
            'material_update': '素材更新',
            'hifly_upload': '飞影上传',
            'auto_rename': '自动重命名',
            'material_upload': '素材更新+飞影上传',
            'upload_rename': '飞影上传+自动重命名'
        }

        # 设置默认脚本路径
        self.default_script_path = "src/video_management_runner.py"

        # 重新加载任务（使用独立配置文件）
        self.tasks = {}  # 清空父类加载的任务
        self.load_tasks()

    def create_system_task(self, task):
        """创建系统定时任务 - 专门用于视频管理"""
        try:
            current_dir = os.getcwd()

            # 视频管理任务使用专门的批处理文件
            script_path = os.path.join(current_dir, "run_video_management_auto.bat")
            vbs_path = os.path.join(current_dir, "run_video_management_scheduled.vbs")

            # 创建或更新VBS文件
            python_exe = sys.executable
            self.create_vbs_file(vbs_path, python_exe, script_path)

            # 创建系统任务 - 使用VM前缀区分
            task_name = f"VideoManagement_{task.name}_{task.task_id[:8]}"

            # 构建schtasks命令
            time_str = task.start_time.toString("HH:mm") if task.start_time else "02:00"

            # 删除可能存在的同名任务
            delete_cmd = f'schtasks /delete /tn "{task_name}" /f'
            try:
                subprocess.run(delete_cmd, shell=True, capture_output=True, text=True, check=False)
            except:
                pass

            # 创建新任务
            create_cmd = f'''schtasks /create /tn "{task_name}" /tr "{vbs_path}" /sc daily /st {time_str} /f'''

            result = subprocess.run(create_cmd, shell=True, capture_output=True, text=True)

            if result.returncode == 0:
                print(f"✓ 系统定时任务创建成功: {task_name}")
                return True
            else:
                print(f"❌ 系统定时任务创建失败: {result.stderr}")
                return False

        except Exception as e:
            print(f"❌ 创建系统任务异常: {e}")
            return False


# VideoManagementScheduleDialog 已删除，直接使用 ScheduleManagerDialog
# VideoManagementTaskDialog 已删除，直接使用 TaskEditDialog
