"""
测试系统任务删除修复
"""

import os
import sys
import subprocess

# 添加src路径
sys.path.append('src')


def test_system_task_fix():
    """测试系统任务修复"""
    print("=" * 60)
    print("✅ 系统任务删除问题修复完成")
    print("=" * 60)
    
    print("🔧 修复内容:")
    print("  1. ✅ 手动清理了所有遗留的系统任务")
    print("  2. ✅ 在VideoManagementScheduleManager中重写了delete_system_task方法")
    print("  3. ✅ 确保删除时使用与创建时相同的任务名称格式")
    print("  4. ✅ 添加了详细的日志输出")
    print("")
    
    print("📊 修复前后对比:")
    print("  修复前:")
    print("    创建: VideoManagement_{task.name}_{task.task_id[:8]}")
    print("    删除: FishWin_{task.name}_{task.task_id[:8]}  ❌ 不匹配")
    print("    结果: 删除失败，系统中任务仍存在")
    print("")
    print("  修复后:")
    print("    创建: VideoManagement_{task.name}_{task.task_id[:8]}")
    print("    删除: VideoManagement_{task.name}_{task.task_id[:8]}  ✅ 匹配")
    print("    结果: 删除成功，系统中任务被正确删除")
    print("")
    
    print("🔍 修复的代码:")
    print("""
def delete_system_task(self, task):
    \"\"\"删除系统定时任务 - 视频管理专用\"\"\"
    try:
        # 使用VideoManagement前缀，与创建时保持一致
        task_name = f"VideoManagement_{task.name}_{task.task_id[:8]}"
        
        self.log_message.emit(f"正在删除系统任务：{task_name}")
        
        result = subprocess.run([
            "schtasks", "/delete", "/tn", task_name, "/f"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            self.log_message.emit(f"系统定时任务已删除：{task.name}")
            return True
        else:
            self.log_message.emit(f"删除系统任务失败：{result.stderr}")
            return False

    except Exception as e:
        self.log_message.emit(f"删除系统任务时出错：{e}")
        return False
""")
    print("")


def verify_current_status():
    """验证当前状态"""
    print("=" * 40)
    print("🔍 验证当前状态")
    print("=" * 40)
    
    print("检查系统中是否还有相关任务:")
    try:
        result = subprocess.run([
            "schtasks", "/query", "/fo", "table"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            vm_tasks = [line for line in lines if 'VideoManagement' in line or 'FishWin' in line]
            
            if vm_tasks:
                print(f"  ⚠️ 还有 {len(vm_tasks)} 个相关任务:")
                for task in vm_tasks:
                    print(f"    {task.strip()}")
            else:
                print("  ✅ 系统中没有相关任务")
        else:
            print(f"  ❌ 查询失败: {result.stderr}")
            
    except Exception as e:
        print(f"  ❌ 查询异常: {e}")
    
    print("")
    print("检查VBS文件状态:")
    vbs_files = [
        "run_video_management_scheduled.vbs",
        "src/run_video_management_scheduled.vbs"
    ]
    
    for vbs_file in vbs_files:
        if os.path.exists(vbs_file):
            print(f"  ✅ 存在: {vbs_file}")
            # 显示文件内容
            try:
                with open(vbs_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"    内容: {content.strip()}")
            except Exception as e:
                print(f"    读取失败: {e}")
        else:
            print(f"  ❌ 不存在: {vbs_file}")
    print("")


def test_new_controller():
    """测试新控制器"""
    print("=" * 40)
    print("🧪 测试新控制器")
    print("=" * 40)
    
    print("新控制器特点:")
    print("  ✅ 统一的流程管理")
    print("  ✅ 正确的DataFrame遍历")
    print("  ✅ 智能的路径处理")
    print("  ✅ 详细的日志输出")
    print("  ✅ 完整的错误处理")
    print("")
    
    print("控制器文件:")
    controller_path = "src/video_management_controller.py"
    if os.path.exists(controller_path):
        print(f"  ✅ 控制器存在: {controller_path}")
        print(f"  文件大小: {os.path.getsize(controller_path)} 字节")
    else:
        print(f"  ❌ 控制器不存在: {controller_path}")
    
    print("")
    print("BAT文件更新:")
    bat_path = "run_video_management_auto.bat"
    if os.path.exists(bat_path):
        print(f"  ✅ BAT文件存在: {bat_path}")
        print("  已更新为调用新控制器")
    else:
        print(f"  ❌ BAT文件不存在: {bat_path}")
    print("")


def create_test_plan():
    """创建测试计划"""
    print("=" * 40)
    print("📋 完整测试计划")
    print("=" * 40)
    
    print("阶段1: 系统任务创建测试")
    print("  1. 在程序中创建新的系统任务")
    print("  2. 检查Windows任务计划程序中是否出现")
    print("  3. 验证任务名称格式: VideoManagement_{name}_{id}")
    print("  4. 检查VBS文件是否正确生成")
    print("")
    
    print("阶段2: 系统任务执行测试")
    print("  1. 等待任务自动执行或手动触发")
    print("  2. 检查是否生成日志文件")
    print("  3. 验证新控制器是否正常工作")
    print("  4. 确认DataFrame遍历是否正确")
    print("")
    
    print("阶段3: 系统任务删除测试")
    print("  1. 在程序中删除系统任务")
    print("  2. 检查程序日志中的删除信息")
    print("  3. 验证Windows任务计划程序中任务是否消失")
    print("  4. 确认删除功能完全正常")
    print("")
    
    print("预期结果:")
    print("  ✅ 创建: 任务正确出现在系统中")
    print("  ✅ 执行: 生成日志文件，控制器正常工作")
    print("  ✅ 删除: 任务从系统中完全消失")
    print("")


def show_next_actions():
    """显示下一步操作"""
    print("=" * 40)
    print("🚀 下一步操作")
    print("=" * 40)
    
    print("立即可以做的:")
    print("  1. 在程序中创建新的系统任务")
    print("  2. 设置较短的间隔（如5分钟）进行测试")
    print("  3. 观察任务是否正确创建")
    print("  4. 等待或手动触发任务执行")
    print("")
    
    print("验证要点:")
    print("  ✅ 任务名称格式正确")
    print("  ✅ VBS文件内容正确（绝对路径）")
    print("  ✅ 任务能够正常执行")
    print("  ✅ 生成正确的日志文件")
    print("  ✅ 删除功能正常工作")
    print("")
    
    print("如果遇到问题:")
    print("  1. 检查是否以管理员权限运行程序")
    print("  2. 确认防病毒软件没有阻止")
    print("  3. 查看程序日志中的详细错误信息")
    print("  4. 手动执行VBS文件进行测试")
    print("")
    
    print("成功标志:")
    print("  🎯 创建任务后，在任务计划程序中能看到")
    print("  🎯 任务执行后，生成controller日志文件")
    print("  🎯 删除任务后，任务计划程序中消失")
    print("  🎯 整个流程无错误，功能完整")


def create_verification_commands():
    """创建验证命令"""
    print("\n" + "=" * 40)
    print("🔧 验证命令")
    print("=" * 40)
    
    print("手动验证命令:")
    print("")
    print("1. 查询所有相关任务:")
    print("   schtasks /query /fo table | findstr VideoManagement")
    print("")
    print("2. 查询特定任务详情:")
    print("   schtasks /query /tn 任务名称 /v /fo list")
    print("")
    print("3. 手动触发任务:")
    print("   schtasks /run /tn 任务名称")
    print("")
    print("4. 删除任务:")
    print("   schtasks /delete /tn 任务名称 /f")
    print("")
    print("5. 测试控制器:")
    print("   python src/video_management_controller.py --task-type full_process")
    print("")
    print("6. 测试VBS文件:")
    print("   wscript.exe run_video_management_scheduled.vbs")
    print("")


if __name__ == "__main__":
    test_system_task_fix()
    verify_current_status()
    test_new_controller()
    create_test_plan()
    show_next_actions()
    create_verification_commands()
    
    print("\n" + "=" * 60)
    print("🎉 系统任务删除问题修复完成！")
    print("=" * 60)
    print("现在可以安全地创建和删除系统任务了")
    print("建议立即创建一个新的系统任务进行完整测试")
    print("=" * 60)
