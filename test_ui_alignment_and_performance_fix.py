#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试UI对齐和性能优化修复
验证文件完整性检查优化、表格对齐、日志区域对齐
"""

import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_file_integrity_optimization():
    """测试文件完整性检查优化"""
    print("=== 测试文件完整性检查优化 ===")
    
    try:
        with open("src/core/video_material_manager.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查优化功能
        optimization_features = [
            "_get_cached_data\\(self, force_check=False\\)",  # 新增force_check参数
            "_get_cached_data\\(force_check=False\\)",  # 保存操作优化
            "_get_cached_data\\(force_check=True\\)",  # 读取操作保持检查
            "只在强制检查或首次读取时进行文件完整性检查",  # 优化说明
        ]
        
        import re
        for feature in optimization_features:
            if re.search(feature, content):
                print(f"✅ 文件完整性检查优化存在: {feature}")
            else:
                print(f"❌ 文件完整性检查优化缺失: {feature}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 文件完整性检查优化测试失败: {str(e)}")
        return False

def test_table_alignment():
    """测试表格对齐修复"""
    print("\n=== 测试表格对齐修复 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查表格对齐
        table_alignment_features = [
            "setContentsMargins\\(24, 16, 24, 16\\).*与数字人模块保持一致",  # 表格边距
        ]
        
        import re
        for feature in table_alignment_features:
            if re.search(feature, content):
                print(f"✅ 表格对齐修复存在: {feature}")
            else:
                print(f"❌ 表格对齐修复缺失: {feature}")
                return False
        
        # 检查是否移除了错误的边距设置
        wrong_margins = [
            "table_layout.setContentsMargins\\(0, 0, 0, 0\\)",  # 错误的表格边距
        ]
        
        for wrong in wrong_margins:
            if re.search(wrong, content):
                print(f"❌ 仍存在错误的边距设置: {wrong}")
                return False
            else:
                print(f"✅ 已移除错误的边距设置: {wrong}")
        
        return True
        
    except Exception as e:
        print(f"❌ 表格对齐修复测试失败: {str(e)}")
        return False

def test_log_area_alignment():
    """测试日志区域对齐修复"""
    print("\n=== 测试日志区域对齐修复 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查日志区域对齐
        log_alignment_features = [
            "log_layout.setContentsMargins\\(24, 0, 24, 16\\).*与数字人模块保持一致",  # 日志边距
            "log_header_layout.addStretch\\(\\)",  # 弹性空间
            "setFixedSize\\(50, 24\\).*与数字人模块保持一致",  # 按钮尺寸
            "setObjectName\\(\"logToggleButton\"\\)",  # 按钮对象名
        ]
        
        import re
        for feature in log_alignment_features:
            if re.search(feature, content):
                print(f"✅ 日志区域对齐修复存在: {feature}")
            else:
                print(f"❌ 日志区域对齐修复缺失: {feature}")
                return False
        
        # 检查按钮位置是否正确（在addStretch之后）
        log_section = re.search(
            r'log_header_layout\.addStretch\(\).*?log_header_layout\.addWidget\(self\.vm_log_toggle_btn\)',
            content, re.DOTALL
        )
        
        if log_section:
            print("✅ 展开/收起按钮位置正确（在右侧）")
        else:
            print("❌ 展开/收起按钮位置错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 日志区域对齐修复测试失败: {str(e)}")
        return False

def test_ui_consistency():
    """测试UI一致性"""
    print("\n=== 测试UI一致性 ===")

    try:
        import re
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()

        # 提取数字人模块的边距设置
        dh_table_margins = re.search(r'def create_digital_human_table.*?setContentsMargins\((\d+, \d+, \d+, \d+)\)', content, re.DOTALL)
        dh_log_margins = re.search(r'def create_digital_human_log.*?setContentsMargins\((\d+, \d+, \d+, \d+)\)', content, re.DOTALL)
        
        # 提取视频管理模块的边距设置
        vm_table_margins = re.search(r'def create_video_management_table.*?setContentsMargins\((\d+, \d+, \d+, \d+)\)', content, re.DOTALL)
        vm_log_margins = re.search(r'def create_video_management_log.*?setContentsMargins\((\d+, \d+, \d+, \d+)\)', content, re.DOTALL)
        
        if dh_table_margins and vm_table_margins:
            dh_table_margin_values = dh_table_margins.group(1)
            vm_table_margin_values = vm_table_margins.group(1)
            
            if dh_table_margin_values == vm_table_margin_values:
                print(f"✅ 表格边距一致: {dh_table_margin_values}")
            else:
                print(f"❌ 表格边距不一致: 数字人({dh_table_margin_values}) vs 视频管理({vm_table_margin_values})")
                return False
        
        if dh_log_margins and vm_log_margins:
            dh_log_margin_values = dh_log_margins.group(1)
            vm_log_margin_values = vm_log_margins.group(1)
            
            if dh_log_margin_values == vm_log_margin_values:
                print(f"✅ 日志边距一致: {dh_log_margin_values}")
            else:
                print(f"❌ 日志边距不一致: 数字人({dh_log_margin_values}) vs 视频管理({vm_log_margin_values})")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ UI一致性测试失败: {str(e)}")
        return False

def test_performance_impact():
    """测试性能影响"""
    print("\n=== 测试性能影响 ===")
    
    try:
        with open("src/core/video_material_manager.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查性能优化
        performance_features = [
            "def update_record_field.*?force_check=False",  # 保存操作不检查
            "def delete_record.*?force_check=False",  # 删除操作不检查
            "def get_recent_week_data.*?force_check=True",  # 读取操作检查
        ]
        
        import re
        for feature in performance_features:
            if re.search(feature, content, re.DOTALL):
                print(f"✅ 性能优化存在: {feature}")
            else:
                print(f"❌ 性能优化缺失: {feature}")
                return False
        
        # 统计文件完整性检查的调用次数
        check_calls = len(re.findall(r'_check_and_repair_file\(\)', content))
        print(f"📊 文件完整性检查调用次数: {check_calls}")
        
        if check_calls <= 2:  # 应该只在必要时调用
            print("✅ 文件完整性检查调用次数合理")
        else:
            print("⚠️ 文件完整性检查调用次数较多，可能影响性能")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能影响测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 UI对齐和性能优化修复测试")
    print("=" * 60)
    
    # 测试文件完整性检查优化
    if not test_file_integrity_optimization():
        print("❌ 文件完整性检查优化测试失败")
        return False
    
    # 测试表格对齐修复
    if not test_table_alignment():
        print("❌ 表格对齐修复测试失败")
        return False
    
    # 测试日志区域对齐修复
    if not test_log_area_alignment():
        print("❌ 日志区域对齐修复测试失败")
        return False
    
    # 测试UI一致性
    if not test_ui_consistency():
        print("❌ UI一致性测试失败")
        return False
    
    # 测试性能影响
    if not test_performance_impact():
        print("❌ 性能影响测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！")
    print("\n📋 修复总结:")
    print("✅ 1. 文件完整性检查优化:")
    print("  - 添加force_check参数控制检查时机")
    print("  - 保存操作不进行文件完整性检查")
    print("  - 读取操作保持文件完整性检查")
    print("  - 显著提升保存操作性能")
    
    print("\n✅ 2. 表格区域对齐修复:")
    print("  - 修改表格容器边距为(24, 16, 24, 16)")
    print("  - 与数字人模块保持完全一致")
    print("  - 表格左右边距与工具栏对齐")
    
    print("\n✅ 3. 日志区域对齐修复:")
    print("  - 修改日志容器边距为(24, 0, 24, 16)")
    print("  - 展开/收起按钮移至右侧")
    print("  - 按钮尺寸调整为50x24")
    print("  - 与数字人模块布局完全一致")
    
    print("\n✅ 4. UI一致性保证:")
    print("  - 所有模块使用相同的边距设置")
    print("  - 按钮样式和位置统一")
    print("  - 布局结构完全对齐")
    
    print("\n✅ 5. 性能优化效果:")
    print("  - 保存操作性能显著提升")
    print("  - 减少不必要的文件读取")
    print("  - 保持数据安全性检查")
    
    print("\n🎯 解决的问题:")
    print("  - 每次保存都检查文件完整性 → 仅在必要时检查")
    print("  - 表格区域与工具栏不对齐 → 统一边距设置")
    print("  - 日志区域与其他模块不一致 → 统一布局和样式")
    print("  - 展开/收起按钮位置和显示问题 → 移至右侧并调整尺寸")
    
    print("\n🚀 预期效果:")
    print("  - 保存操作响应更快，无不必要的完整性检查日志")
    print("  - 表格和日志区域与工具栏完美对齐")
    print("  - 展开/收起按钮在右侧正常显示")
    print("  - 整体UI更加美观和一致")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
