"""
测试VBS文件生成逻辑
"""

import os
import sys

# 添加src路径
sys.path.append('src')

# 导入修复后的类
from ui.schedule_manager import VideoManagementScheduleManager


def test_vbs_generation():
    """测试VBS文件生成"""
    print("=" * 60)
    print("🧪 测试VBS文件生成逻辑")
    print("=" * 60)
    
    # 创建管理器实例
    manager = VideoManagementScheduleManager()
    
    # 设置测试路径
    current_dir = os.getcwd()
    bat_path = os.path.join(current_dir, "run_video_management_auto.bat")
    vbs_path = os.path.join(current_dir, "test_generated.vbs")
    
    print(f"BAT文件路径: {bat_path}")
    print(f"VBS文件路径: {vbs_path}")
    print("")
    
    # 生成VBS文件
    try:
        print("🔧 生成VBS文件...")
        manager.create_video_management_vbs_file(vbs_path, bat_path)
        print("✅ VBS文件生成成功")
        
        # 读取生成的内容
        with open(vbs_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("\n📄 生成的VBS文件内容:")
        print("  " + content.replace('\n', '\n  '))
        
        # 验证内容
        print("\n🔍 内容验证:")
        if "python.exe" in content:
            print("  ✅ 包含Python可执行文件路径")
        else:
            print("  ❌ 缺少Python可执行文件路径")
            
        if "video_management_controller.py" in content:
            print("  ✅ 包含控制器路径")
        else:
            print("  ❌ 缺少控制器路径")
            
        if "scheduled" in content:
            print("  ✅ 包含scheduled参数")
        else:
            print("  ❌ 缺少scheduled参数")
            
        if ".bat" not in content:
            print("  ✅ 不调用BAT文件")
        else:
            print("  ⚠️ 仍在调用BAT文件")
        
        # 测试执行
        print("\n🧪 测试VBS文件执行:")
        import subprocess
        try:
            result = subprocess.run([
                "wscript.exe", vbs_path
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("  ✅ VBS文件执行成功")
            else:
                print("  ❌ VBS文件执行失败")
                if result.stderr:
                    print(f"  错误: {result.stderr}")
        except Exception as e:
            print(f"  ❌ 执行测试异常: {e}")
        
        # 清理测试文件
        try:
            os.remove(vbs_path)
            print("\n🧹 测试文件已清理")
        except:
            pass
            
    except Exception as e:
        print(f"❌ 生成VBS文件失败: {e}")
        import traceback
        traceback.print_exc()


def test_original_vbs_update():
    """更新原始VBS文件"""
    print("\n" + "=" * 40)
    print("🔧 更新原始VBS文件")
    print("=" * 40)
    
    # 创建管理器实例
    manager = VideoManagementScheduleManager()
    
    # 更新原始VBS文件
    current_dir = os.getcwd()
    bat_path = os.path.join(current_dir, "run_video_management_auto.bat")
    vbs_path = os.path.join(current_dir, "run_video_management_scheduled.vbs")
    
    try:
        print("🔧 更新原始VBS文件...")
        manager.create_video_management_vbs_file(vbs_path, bat_path)
        print("✅ 原始VBS文件已更新")
        
        # 读取更新后的内容
        with open(vbs_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("\n📄 更新后的VBS文件内容:")
        print("  " + content.replace('\n', '\n  '))
        
        # 测试执行
        print("\n🧪 测试更新后的VBS文件:")
        import subprocess
        try:
            result = subprocess.run([
                "wscript.exe", vbs_path
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("  ✅ 更新后的VBS文件执行成功")
                
                # 检查日志文件
                log_dir = "feiyingshuziren/log"
                if os.path.exists(log_dir):
                    import datetime
                    today = datetime.datetime.now().strftime("%Y-%m-%d")
                    today_log_dir = os.path.join(log_dir, today)
                    
                    if os.path.exists(today_log_dir):
                        log_files = os.listdir(today_log_dir)
                        controller_logs = [f for f in log_files if 'controller' in f]
                        
                        if controller_logs:
                            latest_log = max(controller_logs)
                            print(f"  ✅ 生成了新的日志文件: {latest_log}")
                        else:
                            print("  ⚠️ 没有找到新的日志文件")
                    else:
                        print(f"  ⚠️ 日志目录不存在: {today_log_dir}")
                else:
                    print(f"  ⚠️ 日志根目录不存在: {log_dir}")
            else:
                print("  ❌ 更新后的VBS文件执行失败")
                if result.stderr:
                    print(f"  错误: {result.stderr}")
        except Exception as e:
            print(f"  ❌ 执行测试异常: {e}")
            
    except Exception as e:
        print(f"❌ 更新VBS文件失败: {e}")
        import traceback
        traceback.print_exc()


def show_solution_summary():
    """显示解决方案总结"""
    print("\n" + "=" * 40)
    print("🎯 问题根本原因和解决方案")
    print("=" * 40)
    
    print("🔍 根本原因:")
    print("  1. 程序中的create_video_management_vbs_file方法")
    print("  2. 每次创建系统任务时都会重新生成VBS文件")
    print("  3. 覆盖了我们手动修改的内容")
    print("  4. 旧的方法调用BAT文件，BAT文件有编码问题")
    print("")
    
    print("✅ 解决方案:")
    print("  1. 修改create_video_management_vbs_file方法")
    print("  2. 让VBS直接调用Python控制器")
    print("  3. 使用完整的Python可执行文件路径")
    print("  4. 避免BAT文件的编码问题")
    print("")
    
    print("🎉 修复效果:")
    print("  1. VBS文件不再调用BAT文件")
    print("  2. 直接调用Python控制器")
    print("  3. 避免了编码问题")
    print("  4. 系统任务创建时自动生成正确的VBS文件")
    print("")


if __name__ == "__main__":
    test_vbs_generation()
    test_original_vbs_update()
    show_solution_summary()
    
    print("=" * 60)
    print("🎉 VBS文件生成逻辑修复完成！")
    print("=" * 60)
    print("现在程序创建系统任务时会自动生成正确的VBS文件")
    print("不再需要手动修改VBS文件内容")
    print("系统任务应该可以正常工作了！")
    print("=" * 60)
