"""
视频管理自动化控制器
统一管理整个视频管理自动化流程
"""

import os
import sys
import asyncio
import argparse
import traceback
from datetime import datetime
from pathlib import Path

# 确保正确的路径设置
current_dir = Path(__file__).parent.parent.absolute()
src_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(src_dir))

# 日志文件句柄
log_file = None


def setup_logging():
    """设置日志系统"""
    global log_file
    
    try:
        # 创建日志目录
        log_dir = current_dir / "feiyingshuziren" / "log" / datetime.now().strftime("%Y-%m-%d")
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建日志文件
        timestamp = datetime.now().strftime("%H-%M-%S")
        log_filename = f"video_management_controller_{timestamp}.log"
        log_filepath = log_dir / log_filename
        
        # 打开日志文件
        log_file = open(log_filepath, 'w', encoding='utf-8')
        
        return str(log_filepath)
        
    except Exception as e:
        print(f"设置日志失败: {e}")
        return None


def log_message(message):
    """记录日志消息"""
    global log_file
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_line = f"[{timestamp}] {message}"
    
    # 输出到控制台
    print(log_line)
    
    # 写入日志文件
    if log_file:
        try:
            log_file.write(log_line + "\n")
            log_file.flush()
        except:
            pass


class VideoManagementController:
    """视频管理自动化控制器"""
    
    def __init__(self, task_type="full_process", headless=False):
        self.task_type = task_type
        self.headless = headless
        self.start_time = datetime.now()
        
        # 任务配置
        self.task_config = {
            'full_process': ['material', 'upload', 'rename'],
            'material_only': ['material'],
            'upload_only': ['upload'],
            'rename_only': ['rename'],
            'material_upload': ['material', 'upload'],
            'upload_rename': ['upload', 'rename']
        }
        
        log_message(f"控制器初始化完成")
        log_message(f"任务类型: {self.task_type}")
        log_message(f"无头模式: {self.headless}")
        log_message(f"工作目录: {current_dir}")
        log_message(f"源码目录: {src_dir}")
    
    async def run_material_update(self):
        """执行素材更新任务"""
        try:
            log_message("=" * 60)
            log_message("[MATERIAL] 开始执行素材更新任务")
            log_message("=" * 60)
            
            # 导入视频素材管理器
            from core.video_material_manager import VideoMaterialManager
            from core.config_manager import ConfigManager
            
            # 创建管理器实例
            config_manager = ConfigManager()
            video_manager = VideoMaterialManager(config_manager)
            
            # 连接日志信号
            class LogAdapter:
                def emit(self, message):
                    log_message(f"[MATERIAL] {message}")
            
            log_adapter = LogAdapter()
            video_manager.log_message.connect(log_adapter.emit)
            
            # 执行素材更新
            log_message("[MATERIAL] 开始下载素材数据...")
            success = await video_manager.download_material_data()
            
            if success:
                log_message("[MATERIAL] 素材更新任务完成")
                return True
            else:
                log_message("[MATERIAL] 素材更新任务失败")
                return False
                
        except Exception as e:
            log_message(f"[MATERIAL] 素材更新任务异常: {str(e)}")
            log_message(f"[MATERIAL] 详细错误: {traceback.format_exc()}")
            return False
    
    async def run_hifly_upload(self):
        """执行飞影上传任务"""
        try:
            log_message("=" * 60)
            log_message("[UPLOAD] 开始执行飞影上传任务")
            log_message("=" * 60)
            
            # 导入相关模块
            from core.hifly_upload_manager import HiflyUploadManager
            from core.config_manager import ConfigManager
            from core.video_material_manager import VideoMaterialManager
            
            # 创建管理器实例
            config_manager = ConfigManager()
            upload_manager = HiflyUploadManager(config_manager)
            video_manager = VideoMaterialManager(config_manager)
            
            # 连接日志信号
            class LogAdapter:
                def emit(self, message):
                    log_message(f"[UPLOAD] {message}")
            
            log_adapter = LogAdapter()
            upload_manager.log_message.connect(log_adapter.emit)
            
            # 获取待上传的视频列表
            log_message("[UPLOAD] 获取待上传的视频列表...")
            recent_data = video_manager.get_recent_week_data()
            
            upload_list = []
            log_message(f"[UPLOAD] 获取到数据: {type(recent_data)}, 行数: {len(recent_data)}")
            
            if len(recent_data) > 0:
                for index, row in recent_data.iterrows():
                    if row.get('是否上传飞影', '否') == '否':
                        upload_list.append({
                            'video_path': row.get('视频路径', ''),
                            'actor_name': row.get('演员', ''),
                            'video_id': row.get('ID', ''),
                            'title': f"{row.get('演员', '')}-{row.get('ID', '')}"
                        })
                        log_message(f"[UPLOAD] 添加上传任务: {row.get('演员', '')}-{row.get('ID', '')}")
            
            if not upload_list:
                log_message("[UPLOAD] 没有需要上传的视频")
                return True
            
            log_message(f"[UPLOAD] 找到 {len(upload_list)} 个待上传视频")
            
            # 设置上传队列并执行
            upload_manager.upload_queue = upload_list
            success = upload_manager.start_upload()
            
            if success:
                log_message("[UPLOAD] 飞影上传任务完成")
                return True
            else:
                log_message("[UPLOAD] 飞影上传任务失败")
                return False
                
        except Exception as e:
            log_message(f"[UPLOAD] 飞影上传任务异常: {str(e)}")
            log_message(f"[UPLOAD] 详细错误: {traceback.format_exc()}")
            return False
    
    async def run_auto_rename(self):
        """执行自动重命名任务"""
        try:
            log_message("=" * 60)
            log_message("[RENAME] 开始执行自动重命名任务")
            log_message("=" * 60)
            
            # 导入重命名自动化
            from core.hifly_rename_automation import batch_rename_videos
            from core.video_material_manager import VideoMaterialManager
            from core.config_manager import ConfigManager
            
            # 创建管理器实例
            config_manager = ConfigManager()
            video_manager = VideoMaterialManager(config_manager)
            
            # 获取需要重命名的视频列表
            log_message("[RENAME] 获取需要重命名的视频列表...")
            recent_data = video_manager.get_recent_week_data()
            
            rename_list = []
            log_message(f"[RENAME] 获取到数据: {type(recent_data)}, 行数: {len(recent_data)}")
            
            if len(recent_data) > 0:
                for index, row in recent_data.iterrows():
                    if row.get('是否重命名', '否') == '否':
                        rename_list.append({
                            'actor_name': row.get('演员', ''),
                            'video_id': row.get('ID', '')
                        })
                        log_message(f"[RENAME] 添加重命名任务: {row.get('演员', '')}-{row.get('ID', '')}")
            
            if not rename_list:
                log_message("[RENAME] 没有需要重命名的视频")
                return True
            
            log_message(f"[RENAME] 找到 {len(rename_list)} 个待重命名视频")
            
            # 执行批量重命名
            results = await batch_rename_videos(rename_list)
            
            # 统计结果
            success_count = sum(1 for r in results if "成功" in r.get('result', ''))
            total_count = len(results)
            
            log_message(f"[RENAME] 重命名完成: {success_count}/{total_count} 成功")
            
            # 更新数据库状态
            for result in results:
                if "成功" in result.get('result', ''):
                    log_message(f"[RENAME] {result['actor_name']}-{result['video_id']} 重命名成功")
            
            log_message("[RENAME] 自动重命名任务完成")
            return True
            
        except Exception as e:
            log_message(f"[RENAME] 自动重命名任务异常: {str(e)}")
            log_message(f"[RENAME] 详细错误: {traceback.format_exc()}")
            return False
    
    async def run_all_tasks(self):
        """运行所有任务"""
        try:
            # 获取要执行的任务列表
            tasks_to_run = self.task_config.get(self.task_type, ['material', 'upload', 'rename'])
            
            log_message(f"[CONTROLLER] 将执行 {len(tasks_to_run)} 个任务: {', '.join(tasks_to_run)}")
            
            # 任务执行统计
            all_success = True
            completed_count = 0
            
            # 执行任务
            for i, task_type in enumerate(tasks_to_run, 1):
                if task_type == 'material':
                    log_message(f"\n[CONTROLLER] 第{i}步：素材更新")
                    if await self.run_material_update():
                        completed_count += 1
                    else:
                        all_success = False
                
                elif task_type == 'upload':
                    log_message(f"\n[CONTROLLER] 第{i}步：飞影上传")
                    if await self.run_hifly_upload():
                        completed_count += 1
                    else:
                        all_success = False
                
                elif task_type == 'rename':
                    log_message(f"\n[CONTROLLER] 第{i}步：自动重命名")
                    if await self.run_auto_rename():
                        completed_count += 1
                    else:
                        all_success = False
            
            # 任务完成总结
            log_message("\n" + "=" * 60)
            log_message("[CONTROLLER] 任务执行总结")
            log_message("=" * 60)
            log_message(f"任务类型: {self.task_type}")
            log_message(f"总任务数: {len(tasks_to_run)}")
            log_message(f"完成任务: {completed_count}")
            log_message(f"失败任务: {len(tasks_to_run) - completed_count}")
            
            if len(tasks_to_run) > 0:
                success_rate = (completed_count / len(tasks_to_run)) * 100
                log_message(f"成功率: {success_rate:.1f}%")
            
            if all_success:
                log_message("[CONTROLLER] 所有任务执行成功！")
            else:
                log_message("[CONTROLLER] 部分任务执行失败，请检查日志")
            
            end_time = datetime.now()
            duration = end_time - self.start_time
            log_message(f"开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            log_message(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            log_message(f"总耗时: {duration.total_seconds():.1f}秒")
            log_message("[CONTROLLER] 视频管理自动化任务结束")
            
            return 0 if all_success else 1
            
        except Exception as e:
            log_message(f"[CONTROLLER] 主程序异常: {str(e)}")
            log_message(f"[CONTROLLER] 详细错误: {traceback.format_exc()}")
            return 1


async def main():
    """主函数"""
    try:
        # 解析命令行参数
        parser = argparse.ArgumentParser(description="视频管理自动化控制器")
        parser.add_argument("--task-type", default="full_process", 
                          choices=['full_process', 'material_only', 'upload_only', 'rename_only', 
                                 'material_upload', 'upload_rename'],
                          help="任务类型")
        parser.add_argument("--headless", action="store_true", help="无头模式运行")
        
        args = parser.parse_args()
        
        # 设置日志
        log_filepath = setup_logging()
        log_message(f"日志文件: {log_filepath}")
        log_message("视频管理自动化控制器启动")
        log_message(f"任务类型: {args.task_type}")
        log_message(f"无头模式: {args.headless}")
        log_message(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 创建控制器并运行
        controller = VideoManagementController(args.task_type, args.headless)
        exit_code = await controller.run_all_tasks()
        
        return exit_code
        
    except Exception as e:
        log_message(f"[MAIN] 主程序异常: {str(e)}")
        log_message(f"[MAIN] 详细错误: {traceback.format_exc()}")
        return 1
    
    finally:
        # 关闭日志文件
        if log_file:
            try:
                log_file.close()
            except:
                pass


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
