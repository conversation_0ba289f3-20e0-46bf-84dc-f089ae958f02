#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试ID列编辑功能
验证视频管理模块表格中的ID列是否可以编辑
"""

import os
import sys
import re

def test_id_column_editability():
    """测试ID列的可编辑性设置"""
    print("=== 测试ID列编辑功能 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 查找ID列相关的设置
        print("🔍 检查ID列的编辑设置:")
        
        # 检查ID列的setFlags设置
        id_patterns = [
            # 应该移除的不可编辑设置
            (r'ID.*setFlags.*~.*ItemIsEditable', "ID列不可编辑设置", False),
            
            # 应该存在的可编辑设置
            (r'ID.*setFlags.*\|.*ItemIsEditable', "ID列可编辑设置", True),
            
            # 检查注释说明
            (r'ID列.*可编辑', "ID列可编辑注释", True),
        ]
        
        results = {}
        for pattern, description, should_exist in id_patterns:
            matches = re.findall(pattern, content)
            count = len(matches)
            
            if should_exist:
                success = count > 0
                status = f"✅ 找到 {count} 处" if success else "❌ 未找到"
            else:
                success = count == 0
                status = f"✅ 已移除" if success else f"❌ 仍有 {count} 处"
            
            results[description] = success
            print(f"  {description}: {status}")
        
        # 检查具体的ID列设置代码
        print(f"\n📍 ID列具体设置:")
        
        # 查找ID列的处理代码
        id_code_pattern = r'if original_col_name == "ID":(.*?)else:'
        id_matches = re.findall(id_code_pattern, content, re.DOTALL)
        
        for i, match in enumerate(id_matches):
            print(f"  ID列设置 {i+1}:")
            lines = match.strip().split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    print(f"    {line}")
        
        # 检查其他列的设置作为对比
        print(f"\n📍 其他列设置对比:")
        other_code_pattern = r'else:(.*?)self\.vm_table\.setItem'
        other_matches = re.findall(other_code_pattern, content, re.DOTALL)
        
        if other_matches:
            print(f"  其他列设置:")
            lines = other_matches[0].strip().split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and 'setItem' not in line:
                    print(f"    {line}")
        
        # 总体结果
        all_passed = all(results.values())
        
        print(f"\n🎯 测试结果: {'✅ 全部通过' if all_passed else '❌ 部分失败'}")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_table_edit_triggers():
    """测试表格的编辑触发器设置"""
    print("\n=== 测试表格编辑触发器 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查表格的编辑触发器设置
        edit_trigger_patterns = [
            r'setEditTriggers.*DoubleClicked',
            r'setEditTriggers.*EditKeyPressed',
            r'setEditTriggers.*SelectedClicked',
        ]
        
        print("🔍 检查表格编辑触发器:")
        for pattern in edit_trigger_patterns:
            matches = re.findall(pattern, content)
            if matches:
                print(f"  ✅ 找到编辑触发器: {pattern}")
            else:
                print(f"  ⚠️ 未找到编辑触发器: {pattern}")
        
        # 查找完整的编辑触发器设置
        full_trigger_pattern = r'setEditTriggers\((.*?)\)'
        trigger_matches = re.findall(full_trigger_pattern, content, re.DOTALL)
        
        print(f"\n📍 完整的编辑触发器设置:")
        for match in trigger_matches:
            clean_match = re.sub(r'\s+', ' ', match.strip())
            print(f"  {clean_match}")
        
        return len(trigger_matches) > 0
        
    except Exception as e:
        print(f"❌ 编辑触发器测试失败: {str(e)}")
        return False

def check_numeric_table_widget_item():
    """检查NumericTableWidgetItem类的定义"""
    print("\n=== 检查NumericTableWidgetItem类 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查NumericTableWidgetItem类是否存在
        class_pattern = r'class NumericTableWidgetItem'
        class_matches = re.findall(class_pattern, content)
        
        if class_matches:
            print("✅ NumericTableWidgetItem类存在")
            
            # 查找类的定义
            class_def_pattern = r'class NumericTableWidgetItem.*?(?=class|\Z)'
            class_def_matches = re.findall(class_def_pattern, content, re.DOTALL)
            
            if class_def_matches:
                print("📍 NumericTableWidgetItem类定义:")
                lines = class_def_matches[0].split('\n')[:10]  # 只显示前10行
                for line in lines:
                    if line.strip():
                        print(f"  {line}")
        else:
            print("❌ NumericTableWidgetItem类不存在")
            print("💡 这可能影响ID列的数值排序功能")
        
        return len(class_matches) > 0
        
    except Exception as e:
        print(f"❌ NumericTableWidgetItem检查失败: {str(e)}")
        return False

def generate_edit_instructions():
    """生成编辑操作说明"""
    print("\n=== ID列编辑操作说明 ===")
    
    print("📋 现在ID列应该可以编辑了，用户可以:")
    print("  1. 双击ID单元格进入编辑模式")
    print("  2. 选中ID单元格后按F2键编辑")
    print("  3. 选中ID单元格后直接输入数字")
    print("  4. 使用Tab键在单元格间切换并编辑")
    
    print(f"\n⚠️ 注意事项:")
    print("  - ID列使用NumericTableWidgetItem，支持数值排序")
    print("  - 编辑ID时请确保输入有效的数字")
    print("  - 修改ID后可能影响数据的唯一性")
    print("  - 建议在编辑前备份重要数据")
    
    print(f"\n🔧 如果仍然无法编辑:")
    print("  1. 检查表格是否处于只读模式")
    print("  2. 确认单元格获得了焦点")
    print("  3. 尝试重新加载数据")
    print("  4. 检查是否有其他代码阻止编辑")

def main():
    """主测试函数"""
    print("🧪 ID列编辑功能测试")
    print("=" * 60)
    
    # 测试ID列编辑设置
    editability_ok = test_id_column_editability()
    
    # 测试表格编辑触发器
    triggers_ok = test_table_edit_triggers()
    
    # 检查NumericTableWidgetItem类
    numeric_class_ok = check_numeric_table_widget_item()
    
    # 生成操作说明
    generate_edit_instructions()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    print(f"🎯 测试结果:")
    print(f"  - ID列编辑设置: {'✅ 通过' if editability_ok else '❌ 失败'}")
    print(f"  - 表格编辑触发器: {'✅ 通过' if triggers_ok else '❌ 失败'}")
    print(f"  - NumericTableWidgetItem: {'✅ 存在' if numeric_class_ok else '❌ 缺失'}")
    
    overall_success = editability_ok and triggers_ok
    
    if overall_success:
        print(f"\n🎉 ID列编辑功能修复成功！")
        print(f"\n📋 修改总结:")
        print(f"  ✅ 移除了ID列的不可编辑限制")
        print(f"  ✅ ID列现在设置为可编辑")
        print(f"  ✅ 保持了NumericTableWidgetItem的数值排序功能")
        print(f"  ✅ 表格编辑触发器正常工作")
        
        print(f"\n🚀 用户现在可以:")
        print(f"  - 双击ID单元格进行编辑")
        print(f"  - 修改ID值并保存")
        print(f"  - 享受数值排序功能")
    else:
        print(f"\n❌ 部分功能可能仍有问题，需要进一步检查")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
