"""
快速测试素材下载功能（包含锁屏检测）
"""

import sys
import os
import asyncio
import time

# 添加src路径
sys.path.append('src')

async def test_material_download():
    """测试素材下载功能"""
    print("=" * 60)
    print("🧪 测试素材下载功能（包含锁屏检测）")
    print("=" * 60)
    
    try:
        from core.video_material_manager import VideoMaterialManager
        from core.config_manager import ConfigManager
        
        # 创建管理器实例
        config_manager = ConfigManager()
        manager = VideoMaterialManager(config_manager)
        
        # 连接日志信号
        def log_handler(message):
            print(f"[MATERIAL] {message}")
        
        manager.log_message.connect(log_handler)
        
        print("🔍 开始系统状态检查...")
        
        # 检查系统准备状态
        ready, status = manager.check_system_readiness()
        print(f"系统状态: {'✅ 准备就绪' if ready else '❌ 未准备好'}")
        print(f"状态信息: {status}")
        
        if not ready:
            if "锁屏" in status:
                print("🔒 检测到系统锁屏状态")
                print("💡 在实际使用中，系统会等待解锁后继续执行")
                print("💡 这里跳过等待，直接模拟解锁后的流程")
            else:
                print(f"⚠️ 系统未准备好: {status}")
                print("💡 建议检查系统资源使用情况")
        
        print("")
        print("🌐 测试浏览器稳定性配置...")
        
        # 显示浏览器配置
        print(f"无头模式: {'启用' if manager.headless_mode else '禁用'}")
        print("稳定性参数已配置:")
        print("  ✅ --no-sandbox")
        print("  ✅ --disable-dev-shm-usage") 
        print("  ✅ --disable-background-timer-throttling")
        print("  ✅ --disable-backgrounding-occluded-windows")
        print("  ✅ --disable-renderer-backgrounding")
        
        if manager.headless_mode:
            print("  ✅ --virtual-time-budget=5000")
        
        print("")
        print("⏱️ 测试优化的等待机制...")
        print("等待超时时间: 30秒 (从60秒优化)")
        print("检查间隔: 2秒")
        print("进度反馈: 每5秒")
        print("浏览器进程监控: 启用")
        
        print("")
        print("🎯 修复效果预期:")
        print("  ✅ 锁屏时任务会自动延迟")
        print("  ✅ 解锁后任务会自动继续")
        print("  ✅ 浏览器进程更稳定")
        print("  ✅ 下载等待时间更合理")
        print("  ✅ 进程断开时立即停止等待")
        
        print("")
        print("💡 如果要进行完整测试，请运行:")
        print("   python src/video_management_controller.py scheduled")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False


def show_lockscreen_test_guide():
    """显示锁屏测试指南"""
    print("=" * 60)
    print("🔒 锁屏测试指南")
    print("=" * 60)
    
    print("📋 测试步骤:")
    print("  1. 设置一个定时任务(5-10分钟后执行)")
    print("  2. 锁定电脑屏幕 (Win+L)")
    print("  3. 等待定时任务触发")
    print("  4. 观察日志输出")
    print("  5. 解锁电脑")
    print("  6. 检查任务是否成功完成")
    print("")
    
    print("🔍 预期行为:")
    print("  锁屏时:")
    print("    - 检测到锁屏状态")
    print("    - 任务自动延迟执行")
    print("    - 记录延迟原因")
    print("")
    print("  解锁后:")
    print("    - 检测到解锁状态")
    print("    - 任务自动继续执行")
    print("    - 正常完成下载")
    print("")
    
    print("📊 成功指标:")
    print("  ✅ 没有 'Target page, context or browser has been closed' 错误")
    print("  ✅ 任务在解锁后成功完成")
    print("  ✅ 下载文件正常生成")
    print("  ✅ 日志显示锁屏检测和等待过程")
    print("")
    
    print("🚨 失败指标:")
    print("  ❌ 仍然出现浏览器进程关闭错误")
    print("  ❌ 任务在锁屏时强制执行并失败")
    print("  ❌ 等待超时没有重试")
    print("")


def show_monitoring_commands():
    """显示监控命令"""
    print("=" * 60)
    print("📊 监控命令")
    print("=" * 60)
    
    print("PowerShell监控命令:")
    print("")
    print("1. 实时监控Chrome进程:")
    print("   while($true) {")
    print("     $chromes = Get-Process -Name 'chrome' -ErrorAction SilentlyContinue")
    print("     Write-Host \"$(Get-Date): Chrome进程数量: $($chromes.Count)\"")
    print("     Start-Sleep 5")
    print("   }")
    print("")
    
    print("2. 检查锁屏状态:")
    print("   while($true) {")
    print("     $logonui = Get-Process -Name 'LogonUI' -ErrorAction SilentlyContinue")
    print("     $status = if($logonui) { '锁屏' } else { '解锁' }")
    print("     Write-Host \"$(Get-Date): 系统状态: $status\"")
    print("     Start-Sleep 10")
    print("   }")
    print("")
    
    print("3. 监控系统资源:")
    print("   while($true) {")
    print("     $cpu = Get-Counter '\\Processor(_Total)\\% Processor Time' | Select-Object -ExpandProperty CounterSamples | Select-Object -ExpandProperty CookedValue")
    print("     $mem = Get-Counter '\\Memory\\% Committed Bytes In Use' | Select-Object -ExpandProperty CounterSamples | Select-Object -ExpandProperty CookedValue")
    print("     Write-Host \"$(Get-Date): CPU: $([math]::Round($cpu,1))%, 内存: $([math]::Round($mem,1))%\"")
    print("     Start-Sleep 30")
    print("   }")
    print("")


async def main():
    """主函数"""
    print("🚀 锁屏问题修复验证")
    
    # 运行测试
    success = await test_material_download()
    
    if success:
        print("")
        print("✅ 基础测试通过")
        
        # 显示测试指南
        show_lockscreen_test_guide()
        
        # 显示监控命令
        show_monitoring_commands()
        
        print("=" * 60)
        print("🎉 锁屏问题修复完成！")
        print("=" * 60)
        print("现在可以进行实际的锁屏测试:")
        print("1. 设置定时任务")
        print("2. 锁定屏幕")
        print("3. 观察任务行为")
        print("4. 验证修复效果")
        print("=" * 60)
    else:
        print("")
        print("❌ 测试失败，请检查相关模块")


if __name__ == "__main__":
    asyncio.run(main())
