#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证ID列可编辑修改
简单验证ID列的编辑设置
"""

import os
import re

def verify_id_editable():
    """验证ID列编辑设置"""
    print("🔍 验证ID列编辑功能修改")
    print("=" * 50)
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 查找ID列的具体设置
        print("📍 查找ID列设置代码:")
        
        # 查找ID列处理的代码块
        lines = content.split('\n')
        id_section_found = False
        
        for i, line in enumerate(lines):
            if 'if original_col_name == "ID":' in line:
                print(f"  第{i+1}行: {line.strip()}")
                
                # 显示接下来的几行
                for j in range(1, 6):
                    if i+j < len(lines):
                        next_line = lines[i+j].strip()
                        if next_line:
                            print(f"  第{i+j+1}行: {next_line}")
                            if 'else:' in next_line:
                                break
                
                id_section_found = True
                break
        
        if not id_section_found:
            print("❌ 未找到ID列设置代码")
            return False
        
        # 检查关键设置
        checks = [
            ("ID列不可编辑", r'ID.*~.*ItemIsEditable', False),
            ("ID列可编辑", r'ID.*\|.*ItemIsEditable', True),
            ("NumericTableWidgetItem", r'NumericTableWidgetItem', True),
        ]
        
        print(f"\n🔍 关键设置检查:")
        all_passed = True
        
        for name, pattern, should_exist in checks:
            matches = re.findall(pattern, content)
            count = len(matches)
            
            if should_exist:
                success = count > 0
                status = f"✅ 找到 {count} 处" if success else "❌ 未找到"
            else:
                success = count == 0
                status = f"✅ 已移除" if success else f"❌ 仍有 {count} 处"
            
            print(f"  {name}: {status}")
            if not success:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        return False

def show_edit_instructions():
    """显示编辑操作说明"""
    print(f"\n📋 ID列编辑操作指南:")
    print(f"┌─────────────────────────────────────┐")
    print(f"│  🖱️  双击ID单元格进入编辑模式       │")
    print(f"│  ⌨️  选中后按F2键开始编辑           │")
    print(f"│  📝  选中后直接输入数字             │")
    print(f"│  ↹   使用Tab键在单元格间切换        │")
    print(f"│  ✅  按Enter确认编辑                │")
    print(f"│  ❌  按Esc取消编辑                  │")
    print(f"└─────────────────────────────────────┘")
    
    print(f"\n⚠️ 编辑注意事项:")
    print(f"  - ID应该是唯一的数字")
    print(f"  - 修改ID可能影响数据关联")
    print(f"  - 建议编辑前备份数据")
    print(f"  - 编辑后会自动保存到Excel文件")
    
    print(f"\n🔧 如果仍无法编辑:")
    print(f"  1. 确认表格获得了焦点")
    print(f"  2. 检查是否在编辑模式")
    print(f"  3. 尝试重新加载数据")
    print(f"  4. 重启应用程序")

def main():
    """主函数"""
    success = verify_id_editable()
    show_edit_instructions()
    
    print(f"\n🎯 修改结果:")
    if success:
        print(f"✅ ID列编辑功能已成功启用！")
        print(f"\n📋 修改总结:")
        print(f"  ✅ 移除了ID列的不可编辑限制")
        print(f"  ✅ ID列现在使用 ItemIsEditable 标志")
        print(f"  ✅ 保持了NumericTableWidgetItem的数值排序")
        print(f"  ✅ 用户现在可以双击编辑ID值")
        
        print(f"\n🚀 现在用户可以:")
        print(f"  - 修改视频记录的ID")
        print(f"  - 纠正错误的ID值")
        print(f"  - 重新组织数据编号")
    else:
        print(f"❌ 修改可能不完整，需要进一步检查")
    
    return success

if __name__ == "__main__":
    main()
