"""
数字人上传管理器
基于feiyingshuziren代码实现HiFly平台的数字人视频生成功能
"""

import os
import re
import asyncio
import time
import shutil
import json
import pandas as pd
from datetime import datetime
from PySide6.QtCore import QObject, Signal, QThread, QTimer
from PySide6.QtWidgets import QMessageBox

# 尝试导入playwright，如果失败则设置标志
try:
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    async_playwright = None


class DigitalHumanManager(QObject):
    """数字人上传管理器"""
    
    # 信号定义
    log_message = Signal(str)  # 日志消息
    progress_updated = Signal(int)  # 进度更新
    upload_completed = Signal(str, str, str)  # 上传完成 (文件名, 状态, 消息)
    table_updated = Signal()  # 表格更新
    
    def __init__(self):
        super().__init__()

        # 获取项目根目录（相对于src目录的上级目录）
        current_dir = os.path.dirname(os.path.abspath(__file__))  # core目录
        src_dir = os.path.dirname(current_dir)  # src目录
        self.project_root = os.path.dirname(src_dir)  # 项目根目录

        # 添加停止标志
        self.stop_requested = False

        # 配置项 - 使用绝对路径
        self.ENABLE_SCREENSHOTS = False
        self.AUTH_FILE = os.path.join(self.project_root, "feiyingshuziren", "essential_auth_data.json")
        self.POINTS_FOLDER = "积分"
        self.DARK_FOLDER = "暗黑"
        self.AUDIO_BASE_DIR = os.path.join(self.project_root, "feiyingshuziren", "音频文件")
        
        # 状态变量
        self.is_uploading = False
        self.current_task = None
        
        # 定时器
        self.schedule_timer = QTimer()
        self.schedule_timer.timeout.connect(self.check_and_upload)
        
    def load_auth_data(self):
        """加载认证数据"""
        if not os.path.exists(self.AUTH_FILE):
            self.log_message.emit(f"错误: 认证数据文件 {self.AUTH_FILE} 不存在!")
            return None
        
        try:
            with open(self.AUTH_FILE, "r", encoding="utf-8") as f:
                auth_data = json.load(f)
            
            cookies = auth_data.get("cookies", [])
            local_storage = auth_data.get("localStorage", {})
            
            self.log_message.emit(f"已加载 {len(cookies)} 个cookie, {len(local_storage)} 个localStorage项")
            return auth_data
        except Exception as e:
            self.log_message.emit(f"加载认证数据出错: {e}")
            return None
    
    def get_today_folder(self):
        """获取今日任务文件夹路径"""
        date_today = datetime.now().strftime("%Y%m%d")
        return os.path.join(self.project_root, "feiyingshuziren", f"创作任务_{date_today}")

    def ensure_today_folder_and_table(self):
        """确保今日任务文件夹和表格存在"""
        try:
            # 获取今日任务文件夹
            main_folder = self.get_today_folder()

            # 创建文件夹（如果不存在）
            if not os.path.exists(main_folder):
                os.makedirs(main_folder, exist_ok=True)
                self.log_message.emit(f"已创建今日任务文件夹: {main_folder}")

            # 初始化结果表格（如果不存在会自动创建）
            table_path = self.init_result_table(main_folder)

            return main_folder, table_path
        except Exception as e:
            self.log_message.emit(f"创建今日任务环境时出错: {str(e)}")
            return None, None

    def init_result_table(self, main_folder):
        """初始化结果记录表格"""
        # 使用带日期的表格文件名
        date_today = datetime.now().strftime("%Y%m%d")
        table_path = os.path.join(main_folder, f"生成结果记录_{date_today}.xlsx")

        if not os.path.exists(table_path):
            # 创建表格并写入表头
            headers = ["名称", "素材ID", "生成模式", "上传情况", "上传时间", "文件夹", "工单号", "完成状态", "完成日期"]
            try:
                df = pd.DataFrame(columns=headers)
                df.to_excel(table_path, index=False, engine='openpyxl')
                self.log_message.emit(f"✓ 已创建结果记录表格: {table_path}")
            except Exception as e:
                self.log_message.emit(f"创建表格文件失败: {e}")
        return table_path
    
    def ensure_audio_directories(self):
        """确保音频文件夹存在，如果不存在则创建"""
        try:
            # 创建基础音频目录
            if not os.path.exists(self.AUDIO_BASE_DIR):
                os.makedirs(self.AUDIO_BASE_DIR, exist_ok=True)
                self.log_message.emit(f"已创建音频基础目录: {self.AUDIO_BASE_DIR}")

            # 创建积分模式文件夹
            points_dir = os.path.join(self.AUDIO_BASE_DIR, self.POINTS_FOLDER)
            if not os.path.exists(points_dir):
                os.makedirs(points_dir, exist_ok=True)
                self.log_message.emit(f"已创建积分模式文件夹: {points_dir}")

            # 创建暗黑模式文件夹
            dark_dir = os.path.join(self.AUDIO_BASE_DIR, self.DARK_FOLDER)
            if not os.path.exists(dark_dir):
                os.makedirs(dark_dir, exist_ok=True)
                self.log_message.emit(f"已创建暗黑模式文件夹: {dark_dir}")

            return True
        except Exception as e:
            self.log_message.emit(f"创建音频文件夹时出错: {str(e)}")
            return False

    def scan_audio_files(self, auto_create_dirs=False):
        """
        扫描音频文件

        Args:
            auto_create_dirs: 是否自动创建缺失的目录
        """
        # 如果启用自动创建目录，先确保目录存在
        if auto_create_dirs:
            if not self.ensure_audio_directories():
                return []

        # 检查基础目录是否存在
        if not os.path.exists(self.AUDIO_BASE_DIR):
            self.log_message.emit(f"错误: 音频基础目录 '{self.AUDIO_BASE_DIR}' 不存在")
            return []

        folders_to_process = []

        # 检查积分模式文件夹
        points_dir = os.path.join(self.AUDIO_BASE_DIR, self.POINTS_FOLDER)
        if os.path.exists(points_dir) and os.path.isdir(points_dir):
            points_files = [f for f in os.listdir(points_dir)
                           if f.lower().endswith(('.mp3', '.wav', '.m4a', '.ogg'))]
            if points_files:
                self.log_message.emit(f"积分模式文件夹包含 {len(points_files)} 个音频文件")
                folders_to_process.append({
                    "folder": points_dir,
                    "mode": "jf",
                    "files": points_files
                })

        # 检查暗黑模式文件夹
        dark_dir = os.path.join(self.AUDIO_BASE_DIR, self.DARK_FOLDER)
        if os.path.exists(dark_dir) and os.path.isdir(dark_dir):
            dark_files = [f for f in os.listdir(dark_dir)
                          if f.lower().endswith(('.mp3', '.wav', '.m4a', '.ogg'))]
            if dark_files:
                self.log_message.emit(f"暗黑模式文件夹包含 {len(dark_files)} 个音频文件")
                folders_to_process.append({
                    "folder": dark_dir,
                    "mode": "ah",
                    "files": dark_files
                })

        return folders_to_process
    
    def extract_ticket_number(self, filename):
        """从文件名提取工单号"""
        # 匹配6位数字的工单号
        match = re.search(r'\b(\d{6})\b', filename)
        if match:
            return match.group(1)
        return ""
    
    def extract_name_from_filename(self, filename):
        """从文件名提取姓名"""
        # 移除扩展名
        name_without_ext = os.path.splitext(filename)[0]
        
        # 按照常见分隔符分割
        parts = re.split(r'[-_\s]+', name_without_ext)
        
        # 查找单独的字母（通常是姓名）
        for part in parts:
            if len(part) == 1 and part.isalpha():
                return part
        
        return ""
    
    def start_upload(self, headless=True):
        """开始上传任务"""
        if self.is_uploading:
            self.log_message.emit("上传任务正在进行中...")
            return

        self.is_uploading = True
        self.stop_requested = False  # 重置停止标志
        self.log_message.emit("开始数字人上传任务...")

        # 在新线程中运行上传任务
        self.upload_thread = UploadThread(self, headless)
        self.upload_thread.finished.connect(self.on_upload_finished)
        self.upload_thread.start()

    def stop_upload(self):
        """停止上传任务"""
        if not self.is_uploading:
            self.log_message.emit("没有正在进行的上传任务")
            return

        self.stop_requested = True
        self.log_message.emit("正在停止上传任务...")

        # 如果有上传线程，等待其结束
        if hasattr(self, 'upload_thread') and self.upload_thread.isRunning():
            self.upload_thread.quit()
            self.upload_thread.wait(3000)  # 等待最多3秒

        self.is_uploading = False
        self.log_message.emit("上传任务已停止")

    def on_upload_finished(self):
        """上传任务完成"""
        self.is_uploading = False
        self.log_message.emit("上传任务完成")
        self.progress_updated.emit(0)
    
    def set_schedule(self, interval_minutes):
        """设置定时任务"""
        if interval_minutes > 0:
            self.schedule_timer.start(interval_minutes * 60 * 1000)  # 转换为毫秒
            self.log_message.emit(f"定时任务已设置，每 {interval_minutes} 分钟检查一次")
        else:
            self.schedule_timer.stop()
            self.log_message.emit("定时任务已停止")
    
    def check_and_upload(self):
        """定时检查并上传"""
        self.log_message.emit("定时检查音频文件...")
        folders = self.scan_audio_files()
        if folders:
            total_files = sum(len(folder["files"]) for folder in folders)
            self.log_message.emit(f"发现 {total_files} 个待上传音频文件，开始上传...")
            self.start_upload(headless=True)
        else:
            self.log_message.emit("未发现待上传的音频文件")


class UploadThread(QThread):
    """上传线程"""
    
    def __init__(self, manager, headless=True):
        super().__init__()
        self.manager = manager
        self.headless = headless
    
    def run(self):
        """运行上传任务"""
        try:
            # 运行异步上传任务
            asyncio.run(self.upload_task())
        except Exception as e:
            self.manager.log_message.emit(f"上传任务出错: {e}")
    
    async def upload_task(self):
        """异步上传任务"""
        # 创建结果文件夹
        main_folder = self.manager.get_today_folder()
        os.makedirs(main_folder, exist_ok=True)

        # 初始化结果表格
        table_path = self.manager.init_result_table(main_folder)

        # 扫描音频文件
        folders_to_process = self.manager.scan_audio_files()
        if not folders_to_process:
            self.manager.log_message.emit("没有找到待上传的音频文件")
            return

        # 加载认证数据
        auth_data = self.manager.load_auth_data()
        if not auth_data:
            self.manager.log_message.emit("无法加载认证数据，上传终止")
            return

        # 启动浏览器并执行真实上传
        await self.run_browser_upload(auth_data, folders_to_process, table_path, main_folder)

    async def run_browser_upload(self, auth_data, folders_to_process, table_path, main_folder):
        """运行浏览器上传任务"""
        self.manager.log_message.emit("🔥 重新尝试浏览器自动化上传")

        if not PLAYWRIGHT_AVAILABLE:
            self.manager.log_message.emit("错误: playwright未安装，无法执行真实上传")
            raise Exception("playwright未安装，无法执行上传任务")

        self.manager.log_message.emit("🔥 playwright可用，开始真实上传")

        try:
            async with async_playwright() as p:
                # 按照原始代码的简单方式启动浏览器
                mode_text = "无头模式" if self.headless else "有头模式"
                self.manager.log_message.emit(f"启动浏览器（{mode_text}）...")

                # 尝试使用系统 Chrome 浏览器
                browser = None
                chrome_paths = [
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                    r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', ''))
                ]

                # 首先尝试使用系统 Chrome
                for chrome_path in chrome_paths:
                    if os.path.exists(chrome_path):
                        try:
                            self.manager.log_message.emit(f"使用系统 Chrome: {chrome_path}")
                            browser = await p.chromium.launch(
                                executable_path=chrome_path,
                                headless=self.headless,
                                slow_mo=2000 if not self.headless else 0
                            )
                            break
                        except Exception as e:
                            self.manager.log_message.emit(f"系统 Chrome 启动失败: {str(e)}")
                            continue

                # 如果系统 Chrome 都失败了，尝试 Playwright 内置浏览器
                if browser is None:
                    try:
                        self.manager.log_message.emit("尝试使用 Playwright 内置浏览器...")
                        browser = await p.chromium.launch(
                            headless=self.headless,
                            slow_mo=2000 if not self.headless else 0
                        )
                    except Exception as browser_error:
                        if "Executable doesn't exist" in str(browser_error):
                            raise Exception("未找到可用的浏览器。请确保系统已安装 Chrome 浏览器，或手动运行: playwright install chromium")
                        else:
                            raise browser_error

                # 创建新的上下文
                self.manager.log_message.emit("创建浏览器上下文...")
                context = await browser.new_context(
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36"
                )

                # 应用cookie（在创建页面之前）
                cookies = auth_data.get("cookies", [])
                if cookies:
                    self.manager.log_message.emit(f"应用 {len(cookies)} 个cookie...")
                    await context.add_cookies(cookies)
                    self.manager.log_message.emit("✓ cookies应用成功")

                # 创建新页面
                self.manager.log_message.emit("创建新页面...")
                page = await context.new_page()
                self.manager.log_message.emit("✓ 页面创建成功")

                # 设置超时时间
                page.set_default_timeout(30000)

                # 导航到主页面
                self.manager.log_message.emit("导航到HiFly平台...")
                await page.goto("https://light.hifly.cc/", timeout=30000, wait_until="domcontentloaded")
                self.manager.log_message.emit("✓ 页面导航成功")

                # 等待页面稳定
                self.manager.log_message.emit("等待页面稳定...")
                await page.wait_for_timeout(5000)

                # 应用localStorage - 使用最简单的方式
                local_storage = auth_data.get("localStorage", {})
                if local_storage:
                    self.manager.log_message.emit(f"准备应用 {len(local_storage)} 个localStorage项")

                    # 一次性设置所有localStorage，避免多次调用
                    localStorage_script = "() => {"
                    for key, value in local_storage.items():
                        # 确保值是字符串
                        safe_value = json.dumps(value) if not isinstance(value, str) else value
                        localStorage_script += f'localStorage.setItem("{key}", {json.dumps(safe_value)});'
                    localStorage_script += "}"

                    try:
                        await page.evaluate(localStorage_script)
                        self.manager.log_message.emit(f"✓ 成功应用所有localStorage项")
                    except Exception as ls_error:
                        self.manager.log_message.emit(f"应用localStorage失败: {ls_error}")
                        # 继续执行，不因为localStorage失败而终止

                # 刷新页面使登录生效
                self.manager.log_message.emit("刷新页面使登录生效...")
                await page.reload(wait_until="domcontentloaded")
                await page.wait_for_timeout(3000)
                self.manager.log_message.emit("✓ 页面刷新完成")

                # 开始处理音频文件
                self.manager.log_message.emit("准备调用真实上传函数...")
                await self.process_audio_folders_real(page, folders_to_process, table_path, main_folder)
                self.manager.log_message.emit("真实上传函数执行完成")

                # 保持浏览器打开一段时间，便于观察
                self.manager.log_message.emit("保持浏览器打开10秒，便于观察...")
                await page.wait_for_timeout(10000)

                await browser.close()

        except Exception as outer_e:
            self.manager.log_message.emit(f"🔥 真实上传失败: {outer_e}")
            import traceback
            self.manager.log_message.emit(f"错误详情: {traceback.format_exc()}")
            raise Exception(f"真实上传失败: {outer_e}")

    async def process_audio_folders_real(self, page, folders_to_process, table_path, main_folder):
        """真实处理音频文件夹 - 按照参考代码逻辑"""
        self.manager.log_message.emit("🚀 开始执行真实上传函数 process_audio_folders_real")
        total_files = sum(len(folder["files"]) for folder in folders_to_process)
        processed_files = 0

        self.manager.log_message.emit(f"总共需要处理 {total_files} 个文件")

        # 模式状态跟踪（用于暗黑模式上限检查）
        mode_status = {
            "jf": {"limit_reached": False},
            "ah": {"limit_reached": False}
        }

        for folder_info in folders_to_process:
            folder_path = folder_info["folder"]
            mode = folder_info["mode"]
            files = folder_info["files"]

            mode_name = "积分模式" if mode == "jf" else "暗黑模式"
            self.manager.log_message.emit(f"开始处理 {mode_name} 文件夹，共 {len(files)} 个文件")

            for file_name in files:
                try:
                    # 检查是否请求停止
                    if self.manager.stop_requested:
                        self.manager.log_message.emit("检测到停止请求，终止上传任务")
                        return

                    # 检查当前模式是否已达到上限
                    if mode_status[mode]["limit_reached"]:
                        self.manager.log_message.emit(f"跳过音频文件: {file_name} - {mode_name}已达到提交上限")
                        continue

                    file_path = os.path.join(folder_path, file_name)
                    self.manager.log_message.emit(f"正在处理: {file_name}")

                    # 1. 提取素材ID (倒数第一个5位数字)
                    import re
                    id_match = re.findall(r'(?<!\d)\d{5}(?!\d)', file_name)
                    if not id_match:
                        self.manager.log_message.emit(f"无法从文件名中提取素材ID: {file_name}")
                        self.manager.upload_completed.emit(file_name, "失败", "无法提取素材ID")
                        continue

                    material_id = id_match[-1]  # 取最后一个5位数字
                    self.manager.log_message.emit(f"提取到素材ID: {material_id}")

                    # 2. 导航到主页并等待素材加载
                    # 检查停止请求
                    if self.manager.stop_requested:
                        self.manager.log_message.emit("检测到停止请求，终止上传任务")
                        return

                    self.manager.log_message.emit("导航到主页并等待素材加载...")
                    await page.goto("https://light.hifly.cc/", timeout=30000, wait_until="domcontentloaded")
                    await page.wait_for_timeout(3000)

                    # 3. 搜索匹配的素材
                    # 检查停止请求
                    if self.manager.stop_requested:
                        self.manager.log_message.emit("检测到停止请求，终止上传任务")
                        return

                    material_container = await self.search_material_by_id(page, material_id)
                    if not material_container:
                        self.manager.log_message.emit(f"⚠️ 未找到ID为 {material_id} 的素材，跳过当前音频")
                        self.manager.upload_completed.emit(file_name, "失败", "未找到素材")
                        continue

                    # 4. 点击素材进入创建页面
                    # 检查停止请求
                    if self.manager.stop_requested:
                        self.manager.log_message.emit("检测到停止请求，终止上传任务")
                        return

                    success = await self.click_material_to_create(page, material_container, material_id)
                    if not success:
                        self.manager.log_message.emit(f"点击素材失败: {material_id}")
                        self.manager.upload_completed.emit(file_name, "失败", "点击素材失败")
                        continue

                    # 5. 在创建页面进行重命名和上传
                    # 检查停止请求
                    if self.manager.stop_requested:
                        self.manager.log_message.emit("检测到停止请求，终止上传任务")
                        return

                    upload_success = await self.upload_audio_in_create_page(page, file_path, file_name, mode)

                    if upload_success:
                        # 更新进度
                        processed_files += 1
                        progress = int((processed_files / total_files) * 100)
                        self.manager.progress_updated.emit(progress)

                        # 记录结果
                        ticket_number = self.manager.extract_ticket_number(file_name)
                        result_data = {
                            "名称": file_name,
                            "素材ID": int(material_id),  # 转换为整数
                            "生成模式": mode_name,
                            "上传情况": "成功",
                            "上传时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            "文件夹": mode_name,
                            "工单号": int(ticket_number) if ticket_number else "",  # 转换为整数
                            "完成状态": "",
                            "完成日期": ""
                        }

                        # 更新表格
                        await self.update_result_table(table_path, result_data)

                        # 发送完成信号
                        self.manager.upload_completed.emit(file_name, "成功", f"素材ID: {material_id}")

                        # 移动文件到已完成文件夹 - 按照参考代码的正确路径
                        # 确定目标文件夹（积分或暗黑）
                        if mode == "jf":
                            target_folder = os.path.join(main_folder, "已完成", "积分")
                        else:
                            target_folder = os.path.join(main_folder, "已完成", "暗黑")

                        # 确保目标文件夹存在
                        os.makedirs(target_folder, exist_ok=True)

                        # 移动文件
                        import shutil
                        dest_path = os.path.join(target_folder, file_name)
                        shutil.move(file_path, dest_path)
                        self.manager.log_message.emit(f"✓ 已将音频文件 {file_name} 剪切到 {target_folder}")
                    else:
                        # 检查是否是暗黑模式达到上限
                        if mode == "ah" and "达到上限" in str(upload_success):
                            mode_status[mode]["limit_reached"] = True
                            self.manager.log_message.emit(f"{mode_name}已达到提交上限，后续文件将被跳过")

                        self.manager.upload_completed.emit(file_name, "失败", "上传失败")

                except Exception as e:
                    self.manager.log_message.emit(f"处理文件 {file_name} 失败: {e}")
                    self.manager.upload_completed.emit(file_name, "失败", str(e))

        self.manager.log_message.emit("真实上传任务完成！")

    async def search_material_by_id(self, page, material_id):
        """搜索匹配素材ID的数字人"""
        try:
            self.manager.log_message.emit(f"搜索匹配素材ID: {material_id}...")

            # 先检查当前素材加载情况
            current_count = await page.evaluate('''
                () => {
                    const boxes = document.querySelectorAll('.list-box');
                    return boxes.length;
                }
            ''')
            self.manager.log_message.emit(f"当前已加载素材数量: {current_count}")

            # 完整的素材加载逻辑 - 按照参考代码
            min_materials = 500
            normal_stable_threshold = 3
            extended_stable_threshold = 5
            max_wait_time = 60

            if current_count < min_materials:
                self.manager.log_message.emit(f"素材数量较少 ({current_count} < {min_materials})，尝试加载更多...")

                initial_count = current_count
                stable_count = 0
                start_time = time.time()

                while True:
                    # 获取当前素材数量
                    current_count = await page.evaluate('''
                        () => {
                            const boxes = document.querySelectorAll('.list-box');
                            return boxes.length;
                        }
                    ''')

                    # 打印当前加载状态
                    if current_count > initial_count:
                        self.manager.log_message.emit(f"素材加载中: 已加载 {current_count} 个素材")
                        initial_count = current_count
                        stable_count = 0
                    else:
                        stable_count += 1

                    # 检查是否已经稳定或者达到最大等待时间
                    elapsed_time = time.time() - start_time

                    # 如果素材数量少于最小阈值，使用更严格的稳定条件
                    required_stable_count = extended_stable_threshold if current_count < min_materials else normal_stable_threshold

                    # 如果素材数量少于最小阈值，增加等待时间
                    actual_max_wait_time = max_wait_time * 2 if current_count < min_materials else max_wait_time

                    if stable_count >= required_stable_count or elapsed_time > actual_max_wait_time:
                        # 如果素材数量少于最小阈值，但还没达到延长的等待时间，继续等待
                        if current_count < min_materials and elapsed_time <= max_wait_time and stable_count < extended_stable_threshold:
                            self.manager.log_message.emit(f"素材数量较少 ({current_count} < {min_materials})，延长等待时间...")
                            stable_count = 0  # 重置稳定计数，强制继续等待
                            # 额外滚动页面以尝试加载更多素材
                            await page.evaluate('''
                                () => {
                                    window.scrollTo(0, document.body.scrollHeight);
                                    console.log("滚动到页面底部以尝试加载更多素材");
                                }
                            ''')
                            self.manager.log_message.emit("已滚动页面以尝试加载更多素材")
                            await page.wait_for_timeout(3000)  # 等待更长时间让新素材加载
                            continue
                        break

                    # 仅当数量稳定时滚动一次页面以确认
                    if stable_count >= 1 and current_count > 2000:  # 如果已有大量素材且计数稳定，只滚动一次
                        await page.evaluate('''
                            () => {
                                window.scrollTo(0, document.body.scrollHeight);
                                console.log("滚动到页面底部以确认没有更多内容");
                            }
                        ''')
                        self.manager.log_message.emit("已滚动一次页面以确认加载完成")
                    elif stable_count >= 1:  # 如果素材较少，每次稳定都滚动以尝试加载更多
                        await page.evaluate('''
                            () => {
                                window.scrollTo(0, document.body.scrollHeight);
                                console.log("滚动到页面底部以尝试加载更多素材");
                            }
                        ''')
                        self.manager.log_message.emit("已滚动页面以尝试加载更多素材")

                    # 等待一段时间再检查
                    await page.wait_for_timeout(2000)

                self.manager.log_message.emit(f"素材加载完成，共找到 {current_count} 个素材")

            # 检查并关闭广告弹窗
            self.manager.log_message.emit("检查是否存在广告弹窗...")
            try:
                close_button = await page.query_selector('.activity-modal-content .ant-modal-close')
                if close_button:
                    self.manager.log_message.emit("检测到广告弹窗，点击关闭按钮...")
                    await close_button.click()
                    self.manager.log_message.emit("已关闭广告弹窗")
                    await page.wait_for_timeout(1000)
                else:
                    self.manager.log_message.emit("未检测到广告弹窗，继续执行")
            except Exception as popup_error:
                self.manager.log_message.emit(f"处理广告弹窗时出错: {popup_error}")

            # 确认页面上有素材列表
            try:
                list_count = await page.evaluate('() => document.querySelectorAll("div.list-box").length')
                self.manager.log_message.emit(f"页面上找到 {list_count} 个数字人素材")

                if list_count == 0:
                    self.manager.log_message.emit("未找到素材列表，可能页面加载有问题")
                    return None
            except Exception as e:
                self.manager.log_message.emit(f"检查素材列表时出错: {e}")

            # JavaScript精确匹配搜索 - 按照参考代码的方式返回详细信息
            found_material = await page.evaluate(f'''
                (materialId) => {{
                    // 查找所有box-title元素
                    const titleElements = document.querySelectorAll('.box-title');
                    console.log(`在JavaScript中搜索ID: ${{materialId}}, 找到 ${{titleElements.length}} 个potential素材`);

                    // 找到包含目标ID的元素索引
                    for (let i = 0; i < titleElements.length; i++) {{
                        if (titleElements[i].textContent.includes(materialId)) {{
                            // 获取更多信息以便更精确定位
                            const parentBox = titleElements[i].closest('.list-box');
                            const rect = parentBox ? parentBox.getBoundingClientRect() : null;

                            return {{
                                found: true,
                                index: i,
                                text: titleElements[i].textContent,
                                position: rect ? {{
                                    x: rect.x + rect.width/2,
                                    y: rect.y + rect.height/2
                                }} : null
                            }};
                        }}
                    }}

                    // 未找到匹配元素
                    return {{ found: false }};
                }}
            ''', material_id)

            if found_material and found_material.get('found'):
                self.manager.log_message.emit(f"✓ 找到精确匹配素材ID: {material_id}")
                self.manager.log_message.emit(f"素材文本: {found_material.get('text')}")
                return found_material

            self.manager.log_message.emit(f"未找到匹配素材ID: {material_id}")
            return None
        except Exception as e:
            self.manager.log_message.emit(f"搜索素材时出错: {e}")
            return None

    async def click_material_to_create(self, page, found_material, material_id):
        """点击素材进入创建页面 - 完全按照参考代码逻辑"""
        try:
            self.manager.log_message.emit(f"准备点击素材 {material_id} 进入创建页面...")

            # 如果有位置信息，首先滚动到该位置
            position = found_material.get('position')
            if position:
                self.manager.log_message.emit(f"素材位置信息: x={position['x']}, y={position['y']}")

                # 重要步骤: 先滚动到元素位置，确保元素在视野内
                index = found_material.get('index')
                await page.evaluate(f'''
                    () => {{
                        // 查找所有素材元素
                        const boxes = document.querySelectorAll('.list-box');
                        if (boxes.length > {index}) {{
                            // 滚动到目标元素
                            boxes[{index}].scrollIntoView({{behavior: "auto", block: "center"}});
                            console.log('已滚动到目标素材');
                        }}
                    }}
                ''')

                self.manager.log_message.emit("已滚动到目标素材位置")
                await page.wait_for_timeout(500)  # 等待滚动完成和元素稳定

                # 获取滚动后的新位置 (这一步很重要)
                updated_position = await page.evaluate(f'''
                    () => {{
                        const boxes = document.querySelectorAll('.list-box');
                        if (boxes.length > {index}) {{
                            const box = boxes[{index}];
                            const rect = box.getBoundingClientRect();
                            return {{
                                x: rect.x + rect.width/2,
                                y: rect.y + rect.height/2
                            }};
                        }}
                        return null;
                    }}
                ''')

                if updated_position:
                    self.manager.log_message.emit(f"滚动后的新位置: x={updated_position['x']}, y={updated_position['y']}")
                    position = updated_position

                # 等待页面稳定
                self.manager.log_message.emit("等待3秒让页面元素完全加载和响应...")
                await page.wait_for_timeout(3000)

                # ===== 修复：使用更直接的方法触发悬停和点击 =====
                self.manager.log_message.emit("使用更直接的方法查找和点击去创作按钮...")

                # 1. 获取目标list-box元素
                target_box = await page.evaluate(f'''
                    () => {{
                        const boxes = document.querySelectorAll('.list-box');
                        const index = {found_material.get('index')};
                        if (boxes.length > index) {{
                            const rect = boxes[index].getBoundingClientRect();
                            return {{
                                index: index,
                                x: rect.left,
                                y: rect.top,
                                width: rect.width,
                                height: rect.height,
                                hasBottom: !!boxes[index].querySelector('.bottom')
                            }};
                        }}
                        return null;
                    }}
                ''')

                if target_box:
                    self.manager.log_message.emit(f"找到目标素材框，位置: x={target_box['x']}, y={target_box['y']}, 宽={target_box['width']}, 高={target_box['height']}")

                    # 2. 直接使用鼠标操作在目标元素上移动和悬停
                    # 移动到素材中心位置
                    center_x = target_box['x'] + target_box['width'] / 2
                    center_y = target_box['y'] + target_box['height'] / 2
                    await page.mouse.move(center_x, center_y)

                    # 等待一短暂时间
                    await page.wait_for_timeout(500)

                    # 使用JavaScript触发悬停事件，确保按钮出现
                    await page.evaluate(f'''
                        () => {{
                            const boxes = document.querySelectorAll('.list-box');
                            const index = {found_material.get('index')};
                            if (boxes.length > index) {{
                                // 创建并触发鼠标悬停事件
                                const hoverEvent = new MouseEvent('mouseover', {{
                                    bubbles: true,
                                    cancelable: true,
                                    view: window
                                }});
                                boxes[index].dispatchEvent(hoverEvent);
                                console.log('已通过JavaScript模拟触发悬停事件');

                                // 查找底部元素
                                const bottom = boxes[index].querySelector('.bottom');
                                if (bottom) {{
                                    // 确保底部元素可见
                                    bottom.style.opacity = '1';
                                    bottom.style.visibility = 'visible';
                                    bottom.style.display = 'block';
                                    console.log('已强制底部元素可见');

                                    // 查找并强制显示按钮
                                    const btn = bottom.querySelector('.btn');
                                    if (btn) {{
                                        btn.style.opacity = '1';
                                        btn.style.visibility = 'visible';
                                        btn.style.display = 'block';
                                        console.log('已强制按钮元素可见');
                                    }}
                                }}
                            }}
                        }}
                    ''')
                    self.manager.log_message.emit("已使用JavaScript增强悬停效果")

                    # 3. 移动到底部区域（去创作按钮通常位于底部）
                    button_y = target_box['y'] + target_box['height'] - 20
                    await page.mouse.move(center_x, button_y)
                    self.manager.log_message.emit(f"鼠标悬停在素材底部区域: x={center_x}, y={button_y}")

                    # 4. 再次等待按钮出现 - 增加等待时间
                    await page.wait_for_timeout(1500)

                    # 5. 现在尝试查找可见的去创作按钮并点击
                    button_clicked = await page.evaluate(f'''
                        () => {{
                            const boxes = document.querySelectorAll('.list-box');
                            const index = {found_material.get('index')};
                            if (boxes.length > index) {{
                                // 获取所有可见的按钮
                                const buttons = Array.from(boxes[index].querySelectorAll('.bottom .btn, button'))
                                    .filter(btn =>
                                        btn.textContent &&
                                        btn.textContent.includes('去创作') &&
                                        btn.offsetParent !== null &&
                                        window.getComputedStyle(btn).visibility !== 'hidden' &&
                                        window.getComputedStyle(btn).opacity !== '0'
                                    );

                                if (buttons.length > 0) {{
                                    buttons[0].click();
                                    console.log('已点击去创作按钮');
                                    return true;
                                }}
                            }}
                            return false;
                        }}
                    ''')

                    if button_clicked:
                        self.manager.log_message.emit("✓ 成功点击去创作按钮")

                        # 等待页面跳转 - 使用固定等待，避免超时问题
                        self.manager.log_message.emit("等待页面跳转...")
                        await page.wait_for_timeout(3000)

                        # 检查当前URL
                        current_url = page.url
                        self.manager.log_message.emit(f"跳转后的URL: {current_url}")

                        # 如果是light.hifly.cc的创作页面，直接返回成功
                        if "create" in current_url:
                            self.manager.log_message.emit("✓ 已成功跳转到创作页面")
                            return True
                        else:
                            self.manager.log_message.emit("✗ 未能成功跳转到创作页面")
                            return False
                    else:
                        self.manager.log_message.emit("✗ 未找到或无法点击去创作按钮")
                        return False
                else:
                    self.manager.log_message.emit("无法获取目标素材框信息")
                    return False
            else:
                self.manager.log_message.emit("没有位置信息")
                return False

        except Exception as e:
            self.manager.log_message.emit(f"点击素材进入创建页面失败: {e}")
            import traceback
            self.manager.log_message.emit(f"错误详情: {traceback.format_exc()}")
            return False

    async def upload_audio_in_create_page(self, page, file_path, file_name, mode):
        """在创建页面进行重命名和音频上传 - 包含URL替换逻辑"""
        try:
            self.manager.log_message.emit(f"在创建页面处理音频: {file_name}")

            # 等待创建页面加载
            await page.wait_for_timeout(2000)

            # 关键步骤：检查并替换URL从light.hifly.cc到hifly.cc
            current_url = page.url
            if "light.hifly.cc" in current_url and "create" in current_url:
                new_url = current_url.replace("light.hifly.cc", "hifly.cc")
                self.manager.log_message.emit(f"检测到light域名创作页面，正在替换URL...")
                self.manager.log_message.emit(f"原URL: {current_url}")
                self.manager.log_message.emit(f"新URL: {new_url}")

                # 执行URL切换
                await page.goto(new_url, timeout=30000, wait_until="domcontentloaded")
                self.manager.log_message.emit(f"已重定向到hifly.cc创作页面")

                # 重新应用认证信息
                await self.reapply_auth_after_url_change(page)

                # 等待页面加载完成
                await page.wait_for_timeout(3000)

            # 1. 重命名素材
            # 检查停止请求
            if self.manager.stop_requested:
                self.manager.log_message.emit("检测到停止请求，终止上传任务")
                return False

            file_name_without_ext = os.path.splitext(file_name)[0]
            rename_success = await self.rename_material_in_create_page(page, file_name_without_ext)
            if rename_success:
                self.manager.log_message.emit("✓ 素材重命名成功")
            else:
                self.manager.log_message.emit("⚠️ 素材重命名失败，但继续执行")

            # 2. 切换到音频驱动模式
            # 检查停止请求
            if self.manager.stop_requested:
                self.manager.log_message.emit("检测到停止请求，终止上传任务")
                return False

            audio_mode_success = await self.switch_to_audio_mode_in_create_page(page)
            if audio_mode_success:
                self.manager.log_message.emit("✓ 切换到音频驱动模式成功")
            else:
                self.manager.log_message.emit("⚠️ 切换音频模式失败，但继续执行")

            # 3. 上传音频文件
            # 检查停止请求
            if self.manager.stop_requested:
                self.manager.log_message.emit("检测到停止请求，终止上传任务")
                return False

            upload_success = await self.upload_audio_file_in_create_page(page, file_path)
            if not upload_success:
                self.manager.log_message.emit("✗ 音频文件上传失败")
                return False

            # 4. 配置高级设置（包含生成模式和模型选择）
            # 检查停止请求
            if self.manager.stop_requested:
                self.manager.log_message.emit("检测到停止请求，终止上传任务")
                return False

            await self.configure_advanced_settings(page, mode)

            # 5. 提交生成并检测结果
            # 检查停止请求
            if self.manager.stop_requested:
                self.manager.log_message.emit("检测到停止请求，终止上传任务")
                return False

            submit_result = await self.submit_and_check_result(page, mode)
            return submit_result

        except Exception as e:
            self.manager.log_message.emit(f"在创建页面处理音频失败: {e}")
            import traceback
            self.manager.log_message.emit(f"错误详情: {traceback.format_exc()}")
            return False

    async def reapply_auth_after_url_change(self, page):
        """URL切换后重新应用认证信息"""
        try:
            self.manager.log_message.emit("重新应用登录信息...")

            # 获取认证数据
            auth_data = self.manager.load_auth_data()
            if not auth_data:
                self.manager.log_message.emit("无法获取认证数据")
                return

            # 重新应用localStorage
            local_storage = auth_data.get("localStorage", {})
            if local_storage:
                localStorage_script = "() => {"
                for key, value in local_storage.items():
                    safe_value = json.dumps(value) if not isinstance(value, str) else value
                    localStorage_script += f'localStorage.setItem("{key}", {json.dumps(safe_value)});'
                localStorage_script += "}"

                await page.evaluate(localStorage_script)
                self.manager.log_message.emit(f"已重新应用 {len(local_storage)} 个localStorage项")

            # 刷新页面以确保登录状态生效
            self.manager.log_message.emit("刷新页面以确保登录状态生效...")
            await page.reload(wait_until="domcontentloaded")

        except Exception as e:
            self.manager.log_message.emit(f"重新应用认证信息失败: {e}")
            import traceback
            self.manager.log_message.emit(f"错误详情: {traceback.format_exc()}")

    async def configure_advanced_settings(self, page, mode):
        """配置高级设置 - 完全按照参考代码逻辑"""
        try:
            self.manager.log_message.emit("开始配置高级设置...")

            # 1. 点击高级设置按钮
            setting_btn = await page.query_selector('.setting, .ant-dropdown-trigger, [title="高级设置"]')
            if setting_btn:
                await setting_btn.click()
                self.manager.log_message.emit("✓ 已点击高级设置按钮")
                await page.wait_for_timeout(1000)
            else:
                # 使用JavaScript查找高级设置按钮
                setting_clicked = await page.evaluate('''
                    () => {
                        // 查找包含"高级设置"文本的元素
                        const elements = Array.from(document.querySelectorAll('*'));
                        for (const element of elements) {
                            if (element.textContent && element.textContent.includes('高级设置')) {
                                // 查找可点击的父元素
                                let clickable = element;
                                while (clickable && clickable.tagName !== 'BUTTON' && !clickable.onclick) {
                                    clickable = clickable.parentElement;
                                }
                                if (clickable) {
                                    clickable.click();
                                    return true;
                                }
                            }
                        }
                        return false;
                    }
                ''')

                if setting_clicked:
                    self.manager.log_message.emit("✓ 通过JavaScript找到并点击了高级设置按钮")
                    await page.wait_for_timeout(1000)
                else:
                    self.manager.log_message.emit("⚠️ 无法找到高级设置按钮，跳过高级设置")
                    return

            # 2. 选择生成模式
            generation_mode = "暗黑模式" if mode == "ah" else "积分模式"
            self.manager.log_message.emit(f"准备设置生成模式: {generation_mode}")

            gen_mode_clicked = await page.evaluate(f'''
                (targetMode) => {{
                    // 寻找包含"生成模式"的区域
                    const titles = Array.from(document.querySelectorAll('.title, .item-title'));
                    const modeTitle = titles.find(el => el.textContent.includes('生成模式'));

                    if (modeTitle) {{
                        const settingItem = modeTitle.closest('.setting-item');
                        if (settingItem) {{
                            // 查找所有radio选项
                            const radioLabels = settingItem.querySelectorAll('.ant-radio-wrapper span:not(.ant-radio):not(.ant-radio-inner)');

                            for (const label of radioLabels) {{
                                if (label.textContent.trim() === targetMode) {{
                                    // 找到对应的radio input并点击
                                    const wrapper = label.closest('.ant-radio-wrapper');
                                    if (wrapper) {{
                                        const radio = wrapper.querySelector('input[type="radio"]');
                                        if (radio) {{
                                            radio.click();
                                            return true;
                                        }}
                                    }}
                                }}
                            }}
                        }}
                    }}
                    return false;
                }}
            ''', generation_mode)

            if gen_mode_clicked:
                self.manager.log_message.emit(f"✓ 已选择生成模式: {generation_mode}")
            else:
                self.manager.log_message.emit(f"⚠️ 无法找到指定的生成模式: {generation_mode}")

            # 3. 选择V1.5模型
            model_clicked = await page.evaluate('''
                () => {
                    // 寻找选择模型区域
                    const titles = Array.from(document.querySelectorAll('.title'));
                    const modelTitle = titles.find(el => el.textContent.includes('选择模型'));

                    if (modelTitle) {
                        const settingItem = modelTitle.closest('.setting-item');
                        if (settingItem) {
                            // 查找V1.5选项
                            const radioLabels = settingItem.querySelectorAll('.ant-radio-wrapper span:not(.ant-radio):not(.ant-radio-inner)');

                            for (const label of radioLabels) {
                                if (label.textContent.trim() === 'V1.5') {
                                    // 找到对应的radio input并点击
                                    const wrapper = label.closest('.ant-radio-wrapper');
                                    if (wrapper) {
                                        const radio = wrapper.querySelector('input[type="radio"]');
                                        if (radio) {
                                            radio.click();
                                            return true;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    return false;
                }
            ''')

            if model_clicked:
                self.manager.log_message.emit("✓ 已选择V1.5模型")
            else:
                self.manager.log_message.emit("⚠️ 无法找到V1.5模型选项")

            # 4. 选择顺序驱动模式
            self.manager.log_message.emit("尝试设置驱动模式为顺序驱动...")

            drive_mode_clicked = await page.evaluate('''
                () => {
                    // 寻找包含"驱动模式"的区域
                    const titles = Array.from(document.querySelectorAll('.title, .item-title'));
                    const driveTitle = titles.find(el => el.textContent.includes('驱动模式'));

                    if (driveTitle) {
                        const settingItem = driveTitle.closest('.setting-item');
                        if (settingItem) {
                            // 查找所有radio选项
                            const radioLabels = settingItem.querySelectorAll('.ant-radio-wrapper span:not(.ant-radio):not(.ant-radio-inner)');

                            for (const label of radioLabels) {
                                if (label.textContent.trim() === '顺序驱动') {
                                    // 找到对应的radio input并点击
                                    const wrapper = label.closest('.ant-radio-wrapper');
                                    if (wrapper) {
                                        const radio = wrapper.querySelector('input[type="radio"]');
                                        if (radio) {
                                            console.log('找到顺序驱动选项');
                                            radio.click();
                                            return true;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // 备用方法：通过value=1查找
                    const orderDriveRadio = document.querySelector('input[type="radio"][value="1"]');
                    if (orderDriveRadio) {
                        console.log('通过value找到顺序驱动选项');
                        orderDriveRadio.click();
                        return true;
                    }

                    return false;
                }
            ''')

            if drive_mode_clicked:
                self.manager.log_message.emit("✓ 已选择顺序驱动模式")
            else:
                self.manager.log_message.emit("⚠️ 无法找到顺序驱动模式选项")

            # 等待设置确认
            await page.wait_for_timeout(1000)
            self.manager.log_message.emit("高级设置配置完成")

        except Exception as e:
            self.manager.log_message.emit(f"配置高级设置时出错: {e}")
            import traceback
            self.manager.log_message.emit(f"错误详情: {traceback.format_exc()}")

    async def submit_and_check_result(self, page, mode):
        """提交生成并检测结果 - 包含暗黑模式上限处理"""
        try:
            self.manager.log_message.emit("准备点击提交/生成视频按钮...")

            # 增加等待时间确保上传和设置处理完成
            self.manager.log_message.emit("等待额外3秒确保处理完成...")
            await page.wait_for_timeout(3000)

            # 智能检测按钮文案 - 支持积分模式和暗黑模式
            btn_configs = [
                {"text": "提交（不消耗积分）", "class": "btn-vip"},  # 暗黑模式
                {"text": "提 交", "class": "ant-btn-primary"},       # 积分模式-有空格
                {"text": "提交", "class": "ant-btn-primary"},        # 积分模式-无空格
                {"text": "生成视频", "class": "ant-btn-primary"}     # 其他可能的文案
            ]

            # 尝试精确匹配提交按钮
            self.manager.log_message.emit("尝试精确匹配提交按钮...")
            generate_btn = None

            # 使用JavaScript精确定位按钮
            button_info = await page.evaluate('''
                () => {
                    // 定义要查找的按钮文本
                    const buttonTexts = ["提交（不消耗积分）", "提 交", "提交", "生成视频"];

                    // 查找所有按钮元素
                    const buttons = Array.from(document.querySelectorAll('button'));
                    let foundButton = null;
                    let buttonText = "";

                    // 按优先级查找
                    for (const text of buttonTexts) {
                        for (const button of buttons) {
                            // 获取按钮文本及其子元素文本
                            const fullText = button.textContent.trim();

                            if (fullText.includes(text) &&
                                !fullText.includes('更换') &&
                                !fullText.includes('更 换') &&
                                !button.disabled &&
                                button.offsetParent !== null) {

                                const rect = button.getBoundingClientRect();
                                return {
                                    found: true,
                                    text: fullText,
                                    x: rect.left + rect.width/2,
                                    y: rect.top + rect.height/2
                                };
                            }
                        }
                    }

                    return { found: false };
                }
            ''')

            if button_info and button_info.get('found'):
                self.manager.log_message.emit(f"✓ 找到提交按钮: '{button_info.get('text')}'")

                # 点击按钮
                btn_x = button_info.get('x')
                btn_y = button_info.get('y')
                await page.mouse.click(btn_x, btn_y)
                self.manager.log_message.emit("✓ 已点击提交按钮")

                # 等待并检测提交结果
                self.manager.log_message.emit("等待提交结果...")
                success = False
                limit_reached = False

                for i in range(5):  # 尝试5次，每次等待2秒
                    await page.wait_for_timeout(2000)

                    # 检查是否达到提交上限
                    limit_elements = await page.query_selector_all('text="提交的作品数量达到上限"')
                    if limit_elements:
                        self.manager.log_message.emit("⚠️ 检测到提交上限提示")
                        limit_reached = True

                        # 如果是暗黑模式，返回特殊标识
                        if mode == "ah":
                            self.manager.log_message.emit("⚠️ 暗黑模式已达到上限")
                            return "达到上限"
                        break

                    # 更全面的成功检测 - 按照参考代码逻辑
                    success_detected = await self.check_submission_success(page)
                    if success_detected:
                        self.manager.log_message.emit("✓ 检测到成功提示")
                        success = True
                        break

                    # 检查进度条或加载指示
                    progress = await page.query_selector('.ant-progress, .loading, .progress-bar')
                    if progress:
                        self.manager.log_message.emit(f"检测到进度指示 ({i+1}/5)")

                if success:
                    self.manager.log_message.emit("✓ 提交成功")
                    return True
                elif limit_reached:
                    self.manager.log_message.emit("⚠️ 达到提交上限")
                    return "达到上限" if mode == "ah" else False
                else:
                    self.manager.log_message.emit("⚠️ 未检测到明确的成功提示")
                    return False
            else:
                self.manager.log_message.emit("✗ 未找到提交按钮")
                return False

        except Exception as e:
            self.manager.log_message.emit(f"提交生成失败: {e}")
            import traceback
            self.manager.log_message.emit(f"错误详情: {traceback.format_exc()}")
            return False

    async def check_submission_success(self, page):
        """检查提交是否成功 - 按照参考代码的全面检测逻辑"""
        try:
            success = False

            # 检查1: 成功提示消息
            success_tips = await page.query_selector_all('.ant-message-success, .ant-message-info, .success-tip')
            if len(success_tips) > 0:
                self.manager.log_message.emit("✓ 验证: 检测到成功提示消息")
                success = True

            # 检查2: 页面URL是否发生变化（可能跳转到结果页面）
            current_url = page.url
            if "result" in current_url or "success" in current_url:
                self.manager.log_message.emit("✓ 验证: URL显示成功页面")
                success = True

            # 检查3: 检查是否有"生成中"或"处理中"的提示
            processing_elements = await page.query_selector_all('text="生成中", text="处理中", text="排队中"')
            if len(processing_elements) > 0:
                self.manager.log_message.emit("✓ 验证: 检测到处理中状态")
                success = True

            # 检查4: 检查是否有进度条
            progress_bars = await page.query_selector_all('.ant-progress, .progress-bar')
            if len(progress_bars) > 0:
                self.manager.log_message.emit("✓ 验证: 检测到进度条")
                success = True

            # 检查5: 使用JavaScript检查页面内容变化
            js_success = await page.evaluate('''
                () => {
                    // 检查是否有成功相关的文本
                    const bodyText = document.body.textContent || document.body.innerText || '';
                    const successKeywords = ['成功', '提交成功', '生成中', '处理中', '排队中', '已提交'];

                    for (const keyword of successKeywords) {
                        if (bodyText.includes(keyword)) {
                            console.log('检测到成功关键词:', keyword);
                            return true;
                        }
                    }

                    // 检查是否有成功的CSS类
                    const successElements = document.querySelectorAll('.success, .ant-result-success, .ant-message-success');
                    if (successElements.length > 0) {
                        console.log('检测到成功CSS类');
                        return true;
                    }

                    return false;
                }
            ''')

            if js_success:
                self.manager.log_message.emit("✓ 验证: JavaScript检测到成功标识")
                success = True

            return success

        except Exception as e:
            self.manager.log_message.emit(f"检查提交成功状态时出错: {e}")
            return False

    async def rename_material_in_create_page(self, page, new_name):
        """在创建页面重命名素材"""
        try:
            # 查找重命名图标并点击
            rename_icon = await page.query_selector('.rename .anticon-form, [aria-label="form"]')
            if rename_icon:
                await rename_icon.click()
                await page.wait_for_timeout(500)

                # 查找输入框
                input_field = await page.query_selector('input[type="text"]:focus, .rename-input')
                if input_field:
                    # 清除现有文本并输入新名称
                    await input_field.fill("")
                    await input_field.type(new_name)
                    await input_field.press("Enter")
                    return True
        except Exception as e:
            self.manager.log_message.emit(f"重命名失败: {e}")
        return False

    async def switch_to_audio_mode_in_create_page(self, page):
        """在创建页面切换到音频驱动模式"""
        try:
            # 查找并点击音频驱动标签
            audio_tab = await page.query_selector('label:has-text("音频驱动")')
            if audio_tab:
                await audio_tab.click()
                await page.wait_for_timeout(2000)
                return True
            else:
                # 尝试使用JavaScript查找
                clicked = await page.evaluate('''
                    () => {
                        const labels = Array.from(document.querySelectorAll('label, .ant-radio-button-wrapper'));
                        for (const label of labels) {
                            if (label.textContent.includes('音频') ||
                                label.textContent.includes('音频驱动')) {
                                label.click();
                                return true;
                            }
                        }
                        return false;
                    }
                ''')
                if clicked:
                    await page.wait_for_timeout(2000)
                    return True
        except Exception as e:
            self.manager.log_message.emit(f"切换音频模式失败: {e}")
        return False

    async def upload_audio_file_in_create_page(self, page, file_path):
        """在创建页面上传音频文件"""
        try:
            self.manager.log_message.emit(f"准备上传音频文件: {file_path}")

            # 检查文件是否存在
            if not os.path.exists(file_path):
                self.manager.log_message.emit(f"✗ 文件不存在: {file_path}")
                return False

            # 查找上传区域
            upload_selectors = [
                '.ant-upload-drag',
                '.ant-upload-drag-container',
                '.ant-upload-btn',
                '[role="button"]:has-text("上传")',
                '.upload-area',
                '.dragger'
            ]

            upload_area = None
            for selector in upload_selectors:
                element = await page.query_selector(selector)
                if element:
                    is_visible = await element.is_visible()
                    if is_visible:
                        upload_area = element
                        self.manager.log_message.emit(f"✓ 找到上传区域: {selector}")
                        break

            if not upload_area:
                self.manager.log_message.emit("✗ 无法找到上传区域")
                return False

            # 使用file_chooser事件处理文件选择
            try:
                async with page.expect_file_chooser(timeout=10000) as fc_info:
                    await upload_area.click()
                    self.manager.log_message.emit("✓ 已点击上传区域")

                # 设置文件上传
                file_chooser = await fc_info.value
                await file_chooser.set_files(file_path)
                self.manager.log_message.emit("✓ 文件已设置上传")

                # 等待上传完成
                await page.wait_for_timeout(5000)

                # 验证上传成功
                upload_success = await self.verify_audio_upload_success(page)
                if upload_success:
                    self.manager.log_message.emit("✓ 音频文件上传成功")
                    return True
                else:
                    self.manager.log_message.emit("⚠️ 未检测到明确的上传成功迹象")
                    return False

            except Exception as fc_err:
                self.manager.log_message.emit(f"文件选择器处理错误: {fc_err}")
                return False

        except Exception as e:
            self.manager.log_message.emit(f"上传音频文件失败: {e}")
            return False

    async def verify_audio_upload_success(self, page):
        """验证音频上传是否成功"""
        try:
            # 检查是否有成功的指示器
            success_indicators = [
                '.ant-upload-list-item-done',
                '.upload-success',
                '[class*="success"]',
                '.ant-progress-success'
            ]

            for indicator in success_indicators:
                element = await page.query_selector(indicator)
                if element:
                    return True

            # 检查是否有错误指示器
            error_indicators = [
                '.ant-upload-list-item-error',
                '.upload-error',
                '[class*="error"]'
            ]

            for indicator in error_indicators:
                element = await page.query_selector(indicator)
                if element:
                    return False

            return True  # 如果没有明确的错误指示器，假设成功

        except Exception as e:
            self.manager.log_message.emit(f"验证上传状态失败: {e}")
            return False












    

    
    async def update_result_table(self, table_path, result_data):
        """更新结果记录表格"""
        try:
            if os.path.exists(table_path):
                df = pd.read_excel(table_path, engine='openpyxl')
            else:
                df = pd.DataFrame(columns=["名称", "素材ID", "生成模式", "上传情况", "上传时间", "文件夹", "工单号", "完成状态", "完成日期"])
            
            # 添加新记录
            new_row = pd.DataFrame([result_data])
            df = pd.concat([df, new_row], ignore_index=True)

            # 确保数据类型正确
            if "素材ID" in df.columns:
                df["素材ID"] = df["素材ID"].astype('Int64')  # 使用可空整数类型
            if "工单号" in df.columns:
                # 将工单号转换为整数，空值保持为空
                df["工单号"] = df["工单号"].apply(lambda x: int(x) if pd.notna(x) and x != "" else "")

            # 保存表格
            df.to_excel(table_path, index=False, engine='openpyxl')
            
            # 发送表格更新信号
            self.manager.table_updated.emit()
            
        except Exception as e:
            self.manager.log_message.emit(f"更新表格失败: {e}")
