"""
重命名线程 - 简单可靠的线程方案
"""

import asyncio
import sys
import os
from PySide6.QtCore import QThread, Signal


class RenameThread(QThread):
    """重命名线程"""
    
    # 信号定义
    log_signal = Signal(str)  # 日志信号
    progress_signal = Signal(int, int)  # 进度信号 (current, total)
    finished_signal = Signal(list)  # 完成信号
    error_signal = Signal(str)  # 错误信号
    
    def __init__(self, video_list):
        super().__init__()
        self.video_list = video_list
        
    def run(self):
        """线程主函数"""
        try:
            self.log_signal.emit("🔄 重命名线程开始运行...")
            
            # 在线程中运行异步任务
            if sys.platform == "win32":
                # Windows上设置事件循环策略
                asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
            
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # 运行重命名任务
                results = loop.run_until_complete(self._run_rename_task())
                self.log_signal.emit(f"✅ 重命名任务完成，获得 {len(results)} 个结果")
                self.finished_signal.emit(results)
                
            finally:
                # 改进的事件循环清理
                try:
                    # 取消所有挂起的任务
                    pending = asyncio.all_tasks(loop)
                    if pending:
                        self.log_signal.emit(f"🔄 清理 {len(pending)} 个挂起任务...")
                        for task in pending:
                            if not task.done():
                                task.cancel()

                        # 等待任务取消（减少超时时间）
                        try:
                            loop.run_until_complete(
                                asyncio.wait_for(
                                    asyncio.gather(*pending, return_exceptions=True),
                                    timeout=1.5  # 减少超时时间
                                )
                            )
                            self.log_signal.emit("✅ 挂起任务清理完成")
                        except asyncio.TimeoutError:
                            self.log_signal.emit("⚠️ 任务清理超时，强制继续")
                        except Exception as e:
                            self.log_signal.emit(f"⚠️ 任务清理异常: {str(e)}")

                    # 额外等待，让所有资源释放
                    import time
                    time.sleep(0.5)

                    # 关闭事件循环
                    if not loop.is_closed():
                        loop.close()
                        self.log_signal.emit("✅ 事件循环已关闭")

                except Exception as e:
                    self.log_signal.emit(f"⚠️ 清理事件循环时出错: {str(e)}")
                
        except Exception as e:
            self.log_signal.emit(f"❌ 重命名线程异常: {str(e)}")
            self.error_signal.emit(str(e))
    
    async def _run_rename_task(self):
        """运行重命名任务"""
        results = []
        
        try:
            # 导入重命名自动化类
            sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
            from core.hifly_rename_automation import HiflyRenameAutomation
            
            self.log_signal.emit("🔄 初始化重命名自动化...")
            
            # 创建自动化实例（设置正确的认证文件路径）
            # 计算认证文件的绝对路径
            current_dir = os.path.dirname(os.path.abspath(__file__))  # src/ui
            src_dir = os.path.dirname(current_dir)  # src
            project_root = os.path.dirname(src_dir)  # 项目根目录
            auth_file_path = os.path.join(project_root, "feiyingshuziren", "essential_auth_data.json")

            self.log_signal.emit(f"🔍 认证文件路径: {auth_file_path}")
            self.log_signal.emit(f"🔍 文件存在: {os.path.exists(auth_file_path)}")

            automation = HiflyRenameAutomation(auth_file_path=auth_file_path)
            
            # 加载认证数据
            if not automation.load_auth_data():
                self.log_signal.emit("❌ 认证数据加载失败")
                return []
            
            self.log_signal.emit("✅ 认证数据加载成功")
            
            # 初始化浏览器
            self.log_signal.emit("🔄 初始化浏览器...")
            await automation.init_browser(headless=True)
            self.log_signal.emit("✅ 浏览器初始化完成")
            
            try:
                # 处理每个视频
                total = len(self.video_list)
                for i, video in enumerate(self.video_list, 1):
                    actor_name = video.get('actor_name', '')
                    video_id = video.get('video_id', '')
                    
                    self.log_signal.emit(f"🔄 [{i}/{total}] 处理视频: {actor_name} (ID: {video_id})")
                    self.progress_signal.emit(i, total)
                    
                    try:
                        # 执行重命名
                        result = await automation.process_rename_task(actor_name, video_id)
                        
                        if "成功" in result:
                            self.log_signal.emit(f"✅ [{i}/{total}] {actor_name} 重命名成功")
                            results.append({
                                "actor_name": actor_name,
                                "video_id": video_id,
                                "result": "重命名成功"
                            })
                        else:
                            self.log_signal.emit(f"❌ [{i}/{total}] {actor_name} 重命名失败: {result}")
                            results.append({
                                "actor_name": actor_name,
                                "video_id": video_id,
                                "result": f"重命名失败: {result}"
                            })
                            
                    except Exception as e:
                        error_msg = f"处理异常: {str(e)}"
                        self.log_signal.emit(f"❌ [{i}/{total}] {actor_name} {error_msg}")
                        results.append({
                            "actor_name": actor_name,
                            "video_id": video_id,
                            "result": error_msg
                        })
                
            finally:
                # 关闭浏览器（改进的关闭逻辑）
                self.log_signal.emit("🔄 关闭浏览器...")
                try:
                    # 使用更温和的关闭方式
                    await asyncio.wait_for(automation.close_browser(), timeout=8.0)
                    self.log_signal.emit("✅ 浏览器已关闭")

                    # 额外等待，让资源完全释放
                    await asyncio.sleep(1)
                    self.log_signal.emit("✅ 浏览器资源已释放")

                except asyncio.TimeoutError:
                    self.log_signal.emit("⚠️ 关闭浏览器超时，强制清理")
                except Exception as e:
                    self.log_signal.emit(f"⚠️ 关闭浏览器时出错: {str(e)}")
                
        except Exception as e:
            self.log_signal.emit(f"❌ 重命名任务异常: {str(e)}")
            
        return results
