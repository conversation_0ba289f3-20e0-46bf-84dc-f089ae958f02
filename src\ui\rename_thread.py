"""
重命名线程 - 简单可靠的线程方案
"""

import asyncio
import sys
import os
import warnings
import io
from contextlib import redirect_stderr
from PySide6.QtCore import QThread, Signal


class AsyncioErrorFilter:
    """过滤asyncio相关的错误输出"""

    def __init__(self):
        self.original_stderr = sys.stderr
        self.buffer = io.StringIO()

    def write(self, text):
        # 过滤掉asyncio相关的错误
        if any(keyword in text for keyword in [
            "_ProactorBasePipeTransport.__del__",
            "BaseSubprocessTransport.__del__",
            "StreamWriter.__del__",
            "Event loop is closed",
            "I/O operation on closed pipe",
            "unclosed transport"
        ]):
            # 这些错误不输出到终端
            return

        # 其他错误正常输出
        self.original_stderr.write(text)

    def flush(self):
        self.original_stderr.flush()


class RenameThread(QThread):
    """重命名线程"""
    
    # 信号定义
    log_signal = Signal(str)  # 日志信号
    progress_signal = Signal(int, int)  # 进度信号 (current, total)
    finished_signal = Signal(list)  # 完成信号
    error_signal = Signal(str)  # 错误信号
    
    def __init__(self, video_list, headless=True):
        super().__init__()
        self.video_list = video_list
        self.headless = headless
        
    def run(self):
        """线程主函数"""
        # 启用asyncio错误过滤
        error_filter = AsyncioErrorFilter()
        original_stderr = sys.stderr

        try:
            # 替换stderr以过滤asyncio错误
            sys.stderr = error_filter

            self.log_signal.emit("🔄 重命名线程开始运行...")

            # 在线程中运行异步任务
            if sys.platform == "win32":
                # Windows上设置事件循环策略
                asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
            
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # 运行重命名任务
                results = loop.run_until_complete(self._run_rename_task())
                self.log_signal.emit(f"✅ 重命名任务完成，获得 {len(results)} 个结果")
                self.finished_signal.emit(results)
                
            finally:
                # 彻底的事件循环清理（解决Windows asyncio问题）
                try:
                    # 1. 给事件循环一个机会处理待处理的回调
                    try:
                        loop.run_until_complete(asyncio.sleep(0))
                    except:
                        pass

                    # 2. 取消所有挂起的任务
                    pending = asyncio.all_tasks(loop)
                    if pending:
                        self.log_signal.emit(f"🔄 清理 {len(pending)} 个挂起任务...")
                        for task in pending:
                            if not task.done():
                                task.cancel()

                        # 3. 等待任务取消完成
                        try:
                            loop.run_until_complete(
                                asyncio.wait_for(
                                    asyncio.gather(*pending, return_exceptions=True),
                                    timeout=1.0
                                )
                            )
                            self.log_signal.emit("✅ 挂起任务清理完成")
                        except:
                            self.log_signal.emit("⚠️ 强制清理挂起任务")

                    # 4. 再次给事件循环处理机会
                    try:
                        loop.run_until_complete(asyncio.sleep(0))
                    except:
                        pass

                    # 5. 等待所有transport关闭
                    import time
                    time.sleep(0.2)

                    # 6. 安全关闭事件循环
                    if not loop.is_closed():
                        try:
                            loop.close()
                            self.log_signal.emit("✅ 事件循环已关闭")
                        except Exception as e:
                            self.log_signal.emit(f"⚠️ 关闭事件循环异常: {str(e)}")

                    # 7. 最终等待，让垃圾回收完成
                    time.sleep(0.1)

                except Exception as e:
                    self.log_signal.emit(f"⚠️ 清理事件循环时出错: {str(e)}")
                
        except Exception as e:
            self.log_signal.emit(f"❌ 重命名线程异常: {str(e)}")
            self.error_signal.emit(str(e))

        finally:
            # 恢复原始stderr
            sys.stderr = original_stderr

            # 额外等待，让所有垃圾回收完成
            import time
            time.sleep(0.5)
    
    async def _run_rename_task(self):
        """运行重命名任务"""
        results = []
        
        try:
            # 导入重命名自动化类
            sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
            from core.hifly_rename_automation import HiflyRenameAutomation
            
            self.log_signal.emit("🔄 初始化重命名自动化...")
            
            # 创建自动化实例（设置正确的认证文件路径）
            # 计算认证文件的绝对路径
            current_dir = os.path.dirname(os.path.abspath(__file__))  # src/ui
            src_dir = os.path.dirname(current_dir)  # src
            project_root = os.path.dirname(src_dir)  # 项目根目录
            auth_file_path = os.path.join(project_root, "feiyingshuziren", "essential_auth_data.json")

            self.log_signal.emit(f"🔍 认证文件路径: {auth_file_path}")
            self.log_signal.emit(f"🔍 文件存在: {os.path.exists(auth_file_path)}")

            automation = HiflyRenameAutomation(auth_file_path=auth_file_path)
            
            # 加载认证数据
            if not automation.load_auth_data():
                self.log_signal.emit("❌ 认证数据加载失败")
                return []
            
            self.log_signal.emit("✅ 认证数据加载成功")
            
            # 初始化浏览器
            self.log_signal.emit(f"🔄 初始化浏览器（无头模式: {'启用' if self.headless else '禁用'}）...")
            await automation.init_browser(headless=self.headless)
            self.log_signal.emit("✅ 浏览器初始化完成")
            
            try:
                # 处理每个视频
                total = len(self.video_list)
                for i, video in enumerate(self.video_list, 1):
                    actor_name = video.get('actor_name', '')
                    video_id = video.get('video_id', '')
                    
                    self.log_signal.emit(f"🔄 [{i}/{total}] 处理视频: {actor_name} (ID: {video_id})")
                    self.progress_signal.emit(i, total)
                    
                    try:
                        # 执行重命名
                        result = await automation.process_rename_task(actor_name, video_id)
                        
                        if "成功" in result:
                            self.log_signal.emit(f"✅ [{i}/{total}] {actor_name} 重命名成功")
                            results.append({
                                "actor_name": actor_name,
                                "video_id": video_id,
                                "result": "重命名成功"
                            })
                        else:
                            self.log_signal.emit(f"❌ [{i}/{total}] {actor_name} 重命名失败: {result}")
                            results.append({
                                "actor_name": actor_name,
                                "video_id": video_id,
                                "result": f"重命名失败: {result}"
                            })
                            
                    except Exception as e:
                        error_msg = f"处理异常: {str(e)}"
                        self.log_signal.emit(f"❌ [{i}/{total}] {actor_name} {error_msg}")
                        results.append({
                            "actor_name": actor_name,
                            "video_id": video_id,
                            "result": error_msg
                        })
                
            finally:
                # 彻底的浏览器关闭逻辑
                self.log_signal.emit("🔄 关闭浏览器...")
                try:
                    # 1. 先关闭所有页面
                    if hasattr(automation, 'page') and automation.page:
                        try:
                            await automation.page.close()
                            self.log_signal.emit("✅ 页面已关闭")
                        except:
                            pass

                    # 2. 关闭浏览器上下文
                    if hasattr(automation, 'context') and automation.context:
                        try:
                            await automation.context.close()
                            self.log_signal.emit("✅ 浏览器上下文已关闭")
                        except:
                            pass

                    # 3. 关闭浏览器实例
                    if hasattr(automation, 'browser') and automation.browser:
                        try:
                            await automation.browser.close()
                            self.log_signal.emit("✅ 浏览器实例已关闭")
                        except:
                            pass

                    # 4. 关闭playwright
                    if hasattr(automation, 'playwright') and automation.playwright:
                        try:
                            await automation.playwright.stop()
                            self.log_signal.emit("✅ Playwright已停止")
                        except:
                            pass

                    # 5. 额外等待让所有资源释放
                    await asyncio.sleep(0.5)
                    self.log_signal.emit("✅ 浏览器资源完全释放")

                except Exception as e:
                    self.log_signal.emit(f"⚠️ 关闭浏览器时出错: {str(e)}")
                
        except Exception as e:
            self.log_signal.emit(f"❌ 重命名任务异常: {str(e)}")
            
        return results
