/*
   Fish Audio声音克隆工具样式表
   现代化设计，参考提供的界面图片
*/

/* ==== 全局样式 ==== */
QMainWindow, QDialog {
    background-color: #f5f5f5;
    color: #333333;
    font-family: "Microsoft YaHei UI", "PingFang SC", "Segoe UI", sans-serif;
    font-size: 13px;
}

/* 自定义标题栏 */
QWidget#titleBar {
    background-color: #f5f5f5;
    border: none;
    min-height: 36px;
    max-height: 36px;
}

/* 拖动区域 */
QWidget#dragArea {
    background-color: transparent;
    min-height: 48px;
}

/* 窗口控制按钮 */
QPushButton#windowButton {
    border: none;
    background-color: transparent;
    color: #666666;
    font-size: 28px;
    min-width: 32px;
    max-width: 32px;
    min-height: 28px;
    max-height: 28px;
    margin: 0;
    padding: 2px;
}

QPushButton#windowButton:hover {
    background-color: #e0e0e0;
}

QPushButton#windowButton[close="true"]:hover {
    background-color: #e81123;
    color: white;
}

/* ==== 侧边栏样式 ==== */
QWidget#sidebarWidget {
    background-color: #f5f5f5;
    min-width: 64px;
    max-width: 64px;
    border: none;
    padding: 0;
}

/* 侧边栏头部用户头像区域 */
QWidget#userAvatarArea {
    background-color: #f5f5f5;
    padding: 16px 0;
    border-bottom: none;
}

/* 侧边栏图标区域 */
QWidget#sidebarIconsArea {
    background-color: #f5f5f5;
    padding: 8px 0;
}

/* 侧边栏底部设置区域 */
QWidget#sidebarBottomArea {
    background-color: #f5f5f5;
    padding: 16px 0;
    border-top: none;
}

/* 用户头像按钮样式 */
QPushButton#userAvatarButton {
    background-color: transparent;
    border: none;
    border-radius: 20px;
    margin: 4px 0 4px 8px;
    min-width: 40px;
    max-width: 40px;
    min-height: 40px;
    max-height: 40px;
}

QPushButton#userAvatarButton:hover {
    background-color: rgba(0, 0, 0, 0.15);
    border-radius: 20px;
}

/* 侧边栏图标按钮样式 */
QPushButton#iconButton {
    background-color: transparent;
    border: none;
    padding: 8px;
    border-radius: 6px;
    margin: 2px 0 2px 8px;
    min-width: 32px;
    max-width: 32px;
    min-height: 32px;
    max-height: 32px;
    outline: none;  /* 禁用焦点虚线框 */
}

QPushButton#iconButton:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

QPushButton#iconButton:focus {
    outline: none;  /* 禁用焦点虚线框 */
}

QPushButton#iconButton[selected="true"] {
    background-color: #dbeafe;
    color: #3b82f6;
}

QPushButton#iconButton:disabled {
    opacity: 0.4;
}

/* 侧边栏工具提示样式 */
QToolTip {
    background-color: rgba(0, 0, 0, 0.75);
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
}

/* ==== 内容区域 ==== */
QWidget#contentArea {
    background-color: #ffffff;
    border: none;
    border-radius: 16px;
    margin: 12px;
}

/* 内容卡片样式 */
QFrame#contentCard {
    background-color: #ffffff;
    border: 1px solid #e1e4e8;  /* 添加边框替代表格边框 */
    border-radius: 12px;
    padding: 0;
    margin: 0 24px;
}

/* 分割器 - 隐藏分割线 */
QSplitter#contentSplitter::handle {
    background-color: transparent;
    height: 24px;
    border: none;
}

QSplitter#contentSplitter::handle:hover {
    background-color: transparent;
}

/* ==== 工具栏容器样式 ==== */
QWidget#toolbarContainer {
    background-color: #ffffff;
    border: none;
    border-radius: 16px 16px 0 0;
    padding: 8px 0;  /* 减少上下内边距，降低工具栏高度 */
    min-height: 48px;  /* 减少最小高度 */
}

/* ==== 搜索框样式 ==== */
QWidget#searchWidget {
    background-color: #f8f9fa;
    border: 1px solid #d0d7de;
    border-radius: 18px;
    padding: 0 20px;
    min-height: 36px;
    max-height: 36px;
    margin-left: 24px;
}

QWidget#searchWidget:focus-within {
    border-color: #0969da;
}

QLineEdit#searchBox {
    background-color: transparent;
    border: none;
    padding: 8px 16px;  /* 增加左右内边距，让提示文字离边框更远 */
    font-size: 13px;
    color: #24292f;
    margin-right: 4px;
}

QLineEdit#searchBox::placeholder {
    color: #656d76;
}

QPushButton#searchButton {
    background-color: transparent;
    border: none;
    padding: 4px;
    margin: 0 2px;
    border-radius: 4px;
    min-width: 20px;
    max-width: 20px;
    min-height: 20px;
    max-height: 20px;
}

QPushButton#prevSearchButton {
    background-color: transparent;
    border: none;
    padding: 4px;
    margin: 0;
    border-radius: 4px;
    min-width: 20px;
    max-width: 20px;
    min-height: 20px;
    max-height: 20px;
}

QPushButton#nextSearchButton {
    background-color: transparent;
    border: none;
    padding: 4px;
    margin: 0;
    border-radius: 4px;
    min-width: 20px;
    max-width: 20px;
    min-height: 20px;
    max-height: 20px;
}

QPushButton#searchButton:hover,
QPushButton#prevSearchButton:hover,
QPushButton#nextSearchButton:hover {
    background-color: #f3f4f6;
}

/* ==== 按钮样式 ==== */
QPushButton {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 8px 12px;
    color: #374151;
    font-family: "Microsoft YaHei UI", "PingFang SC", "Segoe UI", sans-serif;
    font-size: 13px;
    font-weight: 500;
    outline: none;  /* 禁用焦点虚线框 */
}

QPushButton:hover {
    background-color: #f3f4f6;
    border-color: #d1d5db;
}

QPushButton:pressed {
    background-color: #e5e7eb;
    border-color: #d1d5db;
}

QPushButton:focus {
    outline: none;  /* 禁用焦点虚线框 */
    /* 完全不改变边框颜色，保持原样 */
}

QPushButton:disabled {
    background-color: #f9fafb;
    color: #9ca3af;
    border-color: #e5e7eb;
    opacity: 0.6;
}

/* 主要按钮（蓝色） */
QPushButton[class="primaryButton"] {
    background-color: #3b82f6;
    color: white;
    border: 1px solid #3b82f6;  /* 添加同色边框，与次要按钮高度一致 */
    border-radius: 8px;
    padding: 8px 12px;  /* 与次要按钮相同的padding */
    font-weight: 500;
    font-size: 14px;  /* 与次要按钮相同的字体大小 */
    text-align: center;  /* 居中对齐 */
    outline: none;  /* 禁用焦点虚线框 */
}

QPushButton[class="primaryButton"]:hover {
    background-color: #2563eb;
    border-color: #2563eb;  /* 边框颜色跟随背景色 */
}

QPushButton[class="primaryButton"]:pressed {
    background-color: #1d4ed8;
    border-color: #1d4ed8;  /* 边框颜色跟随背景色 */
}

QPushButton[class="primaryButton"]:focus {
    outline: none;  /* 禁用焦点虚线框 */
}

/* 成功按钮（绿色） */
QPushButton[class="successButton"] {
    background-color: #1f883d;
    color: white;
    border: 1px solid #1f883d;
    border-radius: 6px;
}

QPushButton[class="successButton"]:hover {
    background-color: #1a7f37;
}

QPushButton[class="successButton"]:pressed {
    background-color: #166f2c;
}

/* 危险按钮（红色） */
QPushButton[class="dangerButton"] {
    background-color: #dc2626;
    color: white;
    border: 1px solid #dc2626;
    border-radius: 8px;
    padding: 8px 12px;
    font-weight: 500;
    font-size: 14px;
    text-align: center;
    outline: none;
}

QPushButton[class="dangerButton"]:hover {
    background-color: #b91c1c;
    border-color: #b91c1c;
}

QPushButton[class="dangerButton"]:pressed {
    background-color: #991b1b;
    border-color: #991b1b;
}

QPushButton[class="dangerButton"]:focus {
    outline: none;
}

/* 次要按钮 */
QPushButton#secondaryButton {
    background-color: #f6f8fa;
    border: 1px solid #d0d7de;
    color: #24292f;
    border-radius: 6px;
    padding: 8px 12px;  /* 与主要按钮相同的上下padding */
    text-align: center;
    font-size: 14px;    /* 增大字体 */
    font-weight: 500;   /* 增加字重 */
    outline: none;  /* 禁用焦点虚线框 */
}

QPushButton#secondaryButton:hover {
    background-color: #f3f4f6;
    border-color: #d0d7de;
}

QPushButton#secondaryButton:focus {
    outline: none;  /* 禁用焦点虚线框 */
    /* 完全不改变边框颜色，保持原样 */
}

/* 警告按钮 */
QPushButton#warningButton {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    border-radius: 6px;
    padding: 8px 12px;
    min-height: 20px;
    font-size: 14px;
    font-weight: 500;
    outline: none;
}

QPushButton#warningButton:hover {
    background-color: #ffeaa7;
    border-color: #ffd93d;
}

QPushButton#warningButton:focus {
    outline: none;
}

/* 删除按钮 */
QPushButton#deleteButton {
    background-color: #ff4d4f;
    color: white;
    border: none;
    border-radius: 18px;
    font-size: 11px;
    padding: 2px 4px;
}

QPushButton#deleteButton:hover {
    background-color: #ff7875;
}

/* 剪贴板监听按钮 */
QPushButton#btn_toggle_clipboard {
    font-size: 12px;
    padding: 4px 8px;
    max-height: 20px;
    min-height: 20px;
    margin: 2px 8px 2px 0;
    border-radius: 4px;
}

/* ==== 表格样式 - 现代化设计 ==== */
QTableWidget {
    background-color: #ffffff;
    gridline-color: #e1e4e8;
    border: none;  /* 移除表格边框，使用容器边框 */
    border-radius: 0px;  /* 移除表格圆角，使用容器圆角 */
    selection-background-color: #dbeafe;
    selection-color: #1e40af;
    alternate-background-color: #f8f9fa;
    outline: none;
    show-decoration-selected: 1;
}

QTableWidget::item {
    padding: 8px 12px;
    border: none;  /* 移除单元格边框，使用容器边框 */
    color: #24292f;
}

QTableWidget::item:selected {
    background-color: #dbeafe;
    color: #1e40af;
}

QTableWidget::item:hover:!selected {
    background-color: #f6f8fa;
}

/* 表格视图样式 */
QTableView {
    gridline-color: #e1e4e8;
    border: none;
    border-radius: 12px;
    background-color: #ffffff;
    outline: none;
}

QTableView::item {
    border: none;
    padding: 8px 12px;
}

/* 表格头部样式 */
QHeaderView {
    background-color: #f6f8fa;
    border: none;
    border-radius: 12px 12px 0 0;
}

QHeaderView::section {
    background-color: #f6f8fa;
    color: #24292f;
    padding: 12px;
    border: none;
    border-right: 1px solid #e1e4e8;
    font-weight: 600;
    font-size: 12px;
    text-align: center;
}

QHeaderView::section:last {
    border-right: none;
}

QHeaderView::section:first {
    border-top-left-radius: 12px;
}

QHeaderView::section:last {
    border-top-right-radius: 12px;
}

QHeaderView::section:horizontal {
    min-height: 32px;
}

QHeaderView::section:hover {
    background-color: #f3f4f6;
}

/* ==== 日志区域样式 ==== */
QWidget#logContainer {
    background-color: #ffffff;
    border: none;
    border-radius: 12px;
    margin: 0 24px 24px 24px;
}

QTextEdit {
    background-color: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 8px;
    color: #24292f;
    font-family: "Consolas", "Monaco", "Courier New", monospace;
    font-size: 12px;
    padding: 12px;
    line-height: 1.4;
}

QTextEdit:focus {
    border-color: #0969da;
    outline: none;
}

/* ==== 底部控制区 ==== */
QWidget#bottomControls {
    background-color: #ffffff;
    border-radius: 0 0 16px 16px;
    padding: 0;  /* 内边距由代码控制 */
    margin: 0 24px 24px 24px;  /* 保留外边距，确保与表格对齐 */
}

/* 进度条样式 */
QProgressBar {
    background-color: #f6f8fa;
    border: 1px solid #d0d7de;
    border-radius: 8px;
    color: #24292f;
    text-align: center;
    height: 20px;
    font-size: 12px;
    font-weight: 500;
}

QProgressBar::chunk {
    background-color: #1f883d;
    border-radius: 8px;
}

QProgressBar:disabled {
    background-color: #f6f8fa;
    color: #8c959f;
}

QProgressBar:disabled::chunk {
    background-color: #d0d7de;
}

/* ==== 标签样式 ==== */
QLabel {
    color: #24292f;
    font-family: "Microsoft YaHei UI", "PingFang SC", "Segoe UI", sans-serif;
    font-size: 13px;
}

QLabel#titleLabel {
    font-size: 18px;
    font-weight: 600;
    color: #24292f;
    margin-bottom: 8px;
}

QLabel#sectionLabel {
    font-size: 16px;
    font-weight: 600;
    color: #24292f;
    margin-bottom: 12px;
}

QLabel#sectionTitle {
    font-size: 20px;
    font-weight: 600;
    color: #24292f;
    margin-bottom: 16px;
}

/* ==== 设置区域样式 ==== */
QWidget#settingsArea {
    background-color: #ffffff;
    border: 1px solid #d0d7de;
    border-radius: 8px;
    padding: 20px;
}

QWidget#settingsPage {
    background-color: #ffffff;
}

QGroupBox {
    border: 1px solid #d0d7de;
    border-radius: 8px;
    margin-top: 20px;  /* 增加上边距 */
    margin-bottom: 16px;  /* 增加下边距 */
    padding-top: 28px;  /* 增加顶部内边距，为更大的标题留出空间 */
    font-weight: 600;
    color: #24292f;
    background-color: #ffffff;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 4px 12px;  /* 增加标题内边距 */
    left: 0px;  /* 与边框左对齐 */
    background-color: #ffffff;
    color: #24292f;
    font-size: 16px;  /* 增大字体 */
    font-weight: 600;  /* 加粗 */
}

/* 输入框样式 */
QLineEdit, QComboBox, QSpinBox {
    border: 1px solid #d0d7de;
    border-radius: 6px;
    padding: 8px 12px;
    background-color: #ffffff;
    color: #24292f;
    font-size: 13px;
    min-height: 20px;
}

QLineEdit:focus, QComboBox:focus, QSpinBox:focus {
    border: 1px solid #0969da;
    outline: none;
}

QLineEdit:disabled, QComboBox:disabled, QSpinBox:disabled {
    background-color: #f6f8fa;
    color: #8c959f;
    border-color: #d0d7de;
}

QLineEdit::placeholder {
    color: #8c959f;
}

/* 复选框样式 */
QCheckBox {
    spacing: 8px;
    color: #24292f;
    font-size: 13px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #d0d7de;
    background-color: #ffffff;
    border-radius: 3px;
}

QCheckBox::indicator:hover {
    border-color: #0969da;
}

QCheckBox::indicator:checked {
    border: 1px solid #1f883d;
    background-color: #1f883d;
    background-image: url(ui/icons/check-white.svg);
    background-position: center;
    background-repeat: no-repeat;
}

QCheckBox::indicator:checked:hover {
    background-color: #1a7f37;
    border-color: #1a7f37;
}

QCheckBox::indicator:disabled {
    background-color: #f6f8fa;
    border-color: #d0d7de;
}

/* 滚动条样式 */
QScrollBar:vertical {
    border: none;
    background: #f6f8fa;
    width: 12px;
    margin: 0px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background: #d0d7de;
    min-height: 30px;
    border-radius: 6px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background: #afb8c1;
}

QScrollBar::handle:vertical:pressed {
    background: #8c959f;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
    background: none;
}

/* 水平滚动条 */
QScrollBar:horizontal {
    border: none;
    background: #f6f8fa;
    height: 12px;
    margin: 0px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background: #d0d7de;
    min-width: 30px;
    border-radius: 6px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background: #afb8c1;
}

QScrollBar::handle:horizontal:pressed {
    background: #8c959f;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {
    background: none;
}

/* 状态栏样式 */
QStatusBar {
    background-color: #f5f5f5;
    color: #656d76;
    border: none;
    padding: 4px 8px;
    font-size: 12px;
}

QStatusBar::item {
    border: none;
}

/* 对话框样式 */
QDialog {
    background-color: #f6f8fa;
    border: 1px solid #d0d7de;
    border-radius: 8px;
}

QDialog QWidget#dialogContent {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 20px;
}

/* 消息框样式 */
QMessageBox {
    background-color: #ffffff;
    border: 1px solid #d0d7de;
    border-radius: 8px;
}

QMessageBox QPushButton {
    min-width: 80px;
    padding: 8px 16px;
}

QMessageBox QLabel {
    color: #24292f;
    font-size: 13px;
}

/* 窗口控制按钮样式 */
QPushButton#windowButton {
    border: none;
    font-family: "Segoe UI", sans-serif;
    font-size: 16px;
    color: #666666;
    background-color: transparent;
    border-radius: 4px;
    margin: 2px;
}

QPushButton#windowButton:hover {
    background-color: #e0e0e0;
}

QPushButton#windowButton[close="true"]:hover {
    background-color: #ff4d4f;
    color: white;
}

/* 窗口标题栏样式 */
QWidget#titleBar {
    background-color: #f5f5f5;
    border: none;
}

/* 拖动区域样式 */
QWidget#dragArea {
    background-color: transparent;
}

/* ==== 设置面板样式 ==== */
QScrollArea#settingsScrollArea {
    border: none;
    background-color: transparent;
}

QWidget#settingsPage {
    background-color: #ffffff;
    border: none;
    border-radius: 16px;
    padding: 24px;
}

/* 折叠式日志样式 */
QPushButton#logToggleButton {
    background-color: #ffffff;
    border: none;
    border-radius: 12px 12px 0 0;  /* 只有顶部圆角 */
    text-align: left;
    padding: 0;
}

QPushButton#logToggleButton:hover {
    background-color: #f8f9fa;
}

/* 日志标题样式 */
QLabel#logTitle {
    font-weight: 500;
    color: #24292f;
    font-size: 14px;
}

/* 日志计数样式 */
QLabel#logCount {
    color: #656d76;
    font-size: 12px;
}

/* 日志内容区域样式 */
QWidget#logContent {
    background-color: #ffffff;
    border-top: 1px solid #e1e4e8;
}

/* 日志文本区域样式 */
QTextEdit#logTextArea {
    background-color: #ffffff;  /* 改为白色背景 */
    border: none;
    border-radius: 6px;
    padding: 8px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 12px;
    color: #24292f;
}