"""
测试路径修复
"""

import os
import sys

# 添加src路径
sys.path.append('src')


def test_path_fixes():
    """测试路径修复"""
    print("=" * 60)
    print("🔧 测试路径修复")
    print("=" * 60)
    
    print("🔍 问题分析:")
    print("  1. 系统任务VBS文件路径错误")
    print("  2. 程序内定时任务脚本路径重复")
    print("")
    
    print("📂 当前目录结构:")
    current_dir = os.getcwd()
    print(f"  项目根目录: {current_dir}")
    
    # 检查关键文件
    files_to_check = [
        "run_video_management_scheduled.vbs",
        "run_video_management_auto.bat", 
        "src/video_management_runner.py",
        "src/ui/main_window.py",
        "src/ui/schedule_manager.py"
    ]
    
    print("\n📋 关键文件检查:")
    for file_path in files_to_check:
        full_path = os.path.join(current_dir, file_path)
        if os.path.exists(full_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} - 不存在")
    
    print("\n🔧 修复内容:")
    print("  1. 系统任务VBS路径修复:")
    print("     修复前: 可能使用错误的src子目录路径")
    print("     修复后: 使用项目根目录路径")
    print(f"     正确路径: {os.path.join(current_dir, 'run_video_management_scheduled.vbs')}")
    print("")
    
    print("  2. 程序内定时任务脚本路径修复:")
    print("     修复前: script_path = 'src/video_management_runner.py'")
    print("     问题: 相对路径导致路径重复 (src/src/video_management_runner.py)")
    print("     修复后: 使用绝对路径")
    print(f"     正确路径: {os.path.join(current_dir, 'src', 'video_management_runner.py')}")
    print("")
    
    print("🧪 路径测试:")
    
    # 测试VBS文件路径
    vbs_path = os.path.join(current_dir, "run_video_management_scheduled.vbs")
    if os.path.exists(vbs_path):
        print(f"  ✅ VBS文件路径正确: {vbs_path}")
        
        # 读取VBS文件内容
        with open(vbs_path, 'r', encoding='utf-8') as f:
            vbs_content = f.read()
        print(f"  📝 VBS文件内容:")
        print(f"     {vbs_content.strip()}")
    else:
        print(f"  ❌ VBS文件路径错误: {vbs_path}")
    
    # 测试BAT文件路径
    bat_path = os.path.join(current_dir, "run_video_management_auto.bat")
    if os.path.exists(bat_path):
        print(f"  ✅ BAT文件路径正确: {bat_path}")
    else:
        print(f"  ❌ BAT文件路径错误: {bat_path}")
    
    # 测试Python脚本路径
    script_path = os.path.join(current_dir, "src", "video_management_runner.py")
    if os.path.exists(script_path):
        print(f"  ✅ Python脚本路径正确: {script_path}")
    else:
        print(f"  ❌ Python脚本路径错误: {script_path}")
    
    print("\n🚀 测试建议:")
    print("  1. 重启程序以应用路径修复")
    print("  2. 创建新的定时任务进行测试")
    print("  3. 观察日志中的路径信息")
    print("  4. 检查任务是否正确执行")
    print("")
    
    print("📊 预期结果:")
    print("  系统任务:")
    print("    - VBS文件路径正确")
    print("    - BAT文件能够找到并执行")
    print("    - Python脚本正确运行")
    print("")
    print("  程序内定时任务:")
    print("    - 脚本路径不再重复")
    print("    - 能够找到video_management_runner.py")
    print("    - 任务执行成功，返回码为0")
    print("")


def test_command_construction():
    """测试命令构建"""
    print("=" * 40)
    print("🔨 测试命令构建")
    print("=" * 40)
    
    current_dir = os.getcwd()
    
    print("系统任务命令构建:")
    vbs_path = os.path.join(current_dir, "run_video_management_scheduled.vbs")
    task_name = "VideoManagement_测试_12345678"
    
    cmd = [
        "schtasks", "/create",
        "/tn", task_name,
        "/tr", f'wscript.exe "{vbs_path}"',
        "/sc", "daily",
        "/f",
        "/st", "18:30",
        "/ri", "60",
        "/du", "24:00"
    ]
    
    print("  命令: " + " ".join(cmd))
    print(f"  VBS路径: {vbs_path}")
    print("")
    
    print("程序内定时任务命令构建:")
    script_path = os.path.join(current_dir, "src", "video_management_runner.py")
    python_exe = sys.executable
    
    cmd2 = [python_exe, script_path, "--task-type", "full_process"]
    
    print("  命令: " + " ".join(cmd2))
    print(f"  Python路径: {python_exe}")
    print(f"  脚本路径: {script_path}")
    print("")


def check_working_directory():
    """检查工作目录"""
    print("=" * 40)
    print("📁 检查工作目录")
    print("=" * 40)
    
    print(f"当前工作目录: {os.getcwd()}")
    print(f"脚本所在目录: {os.path.dirname(os.path.abspath(__file__))}")
    print(f"Python可执行文件: {sys.executable}")
    print("")
    
    # 检查相对路径和绝对路径
    relative_script = "src/video_management_runner.py"
    absolute_script = os.path.join(os.getcwd(), "src", "video_management_runner.py")
    
    print("路径对比:")
    print(f"  相对路径: {relative_script}")
    print(f"  绝对路径: {absolute_script}")
    print(f"  相对路径存在: {os.path.exists(relative_script)}")
    print(f"  绝对路径存在: {os.path.exists(absolute_script)}")
    print("")
    
    # 模拟从src目录运行时的情况
    src_dir = os.path.join(os.getcwd(), "src")
    if os.path.exists(src_dir):
        print("模拟从src目录运行:")
        old_cwd = os.getcwd()
        os.chdir(src_dir)
        
        print(f"  切换到: {os.getcwd()}")
        print(f"  相对路径存在: {os.path.exists(relative_script)}")
        print(f"  绝对路径存在: {os.path.exists(absolute_script)}")
        
        # 切换回原目录
        os.chdir(old_cwd)
        print(f"  切换回: {os.getcwd()}")


if __name__ == "__main__":
    test_path_fixes()
    test_command_construction()
    check_working_directory()
