"""
飞影重命名功能测试脚本
用于测试重命名自动化功能
"""

import asyncio
import os
import sys
import pandas as pd
from datetime import datetime

# 添加项目路径到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from feiyingshuziren.hifly_rename_automation import HiflyRenameAutomation


def load_test_data():
    """加载测试数据"""
    # 用户提供的测试数据
    test_data = [
        {
            "ID": "38050",
            "拍摄演员名称": "张卓铭",
            "正确命名": "张卓铭-38050-演员"
        },
        {
            "ID": "48074", 
            "拍摄演员名称": "韩卫军",
            "正确命名": "韩卫军-48074-演员"
        }
    ]
    
    print("📋 测试数据:")
    for i, data in enumerate(test_data, 1):
        print(f"  {i}. {data['正确命名']}")
    
    return test_data


def update_avatar_list_with_rename_column():
    """在avatar_list.xlsx中添加是否重命名列"""
    try:
        avatar_list_path = "data/avatar_list.xlsx"
        
        if not os.path.exists(avatar_list_path):
            print(f"⚠️ 文件不存在: {avatar_list_path}")
            return False
        
        # 读取Excel文件
        df = pd.read_excel(avatar_list_path)
        
        # 检查是否已有"是否重命名"列
        if "是否重命名" not in df.columns:
            df["是否重命名"] = ""
            print("✓ 已添加'是否重命名'列")
        else:
            print("✓ '是否重命名'列已存在")
        
        # 保存文件
        df.to_excel(avatar_list_path, index=False)
        print(f"✓ 已更新文件: {avatar_list_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 更新avatar_list.xlsx失败: {e}")
        return False


def update_rename_status(video_id, status):
    """更新重命名状态到Excel文件"""
    try:
        avatar_list_path = "data/avatar_list.xlsx"
        
        if not os.path.exists(avatar_list_path):
            print(f"⚠️ 文件不存在: {avatar_list_path}")
            return False
        
        # 读取Excel文件
        df = pd.read_excel(avatar_list_path)
        
        # 查找对应的行
        mask = df['ID'].astype(str) == str(video_id)
        if mask.any():
            df.loc[mask, '是否重命名'] = status
            
            # 保存文件
            df.to_excel(avatar_list_path, index=False)
            print(f"✓ 已更新ID {video_id} 的重命名状态为: {status}")
            return True
        else:
            print(f"⚠️ 未找到ID为 {video_id} 的记录")
            return False
            
    except Exception as e:
        print(f"❌ 更新重命名状态失败: {e}")
        return False


async def run_rename_test():
    """运行重命名测试"""
    print("=" * 60)
    print("🚀 飞影重命名功能测试")
    print("=" * 60)
    
    # 检查认证文件
    auth_file = "feiyingshuziren/essential_auth_data.json"
    if not os.path.exists(auth_file):
        print(f"❌ 认证文件不存在: {auth_file}")
        print("请确保认证文件存在并包含有效的登录信息")
        return
    
    # 更新avatar_list.xlsx添加重命名列
    if not update_avatar_list_with_rename_column():
        print("❌ 无法更新avatar_list.xlsx文件")
        return
    
    # 加载测试数据
    test_data = load_test_data()
    
    # 创建重命名自动化实例
    automation = HiflyRenameAutomation()
    
    try:
        print("\n🔧 初始化浏览器自动化...")
        
        # 加载认证数据
        if not automation.load_auth_data():
            print("❌ 加载认证数据失败")
            return
        
        # 初始化浏览器（非无头模式，便于观察）
        if not await automation.init_browser(headless=False):
            print("❌ 初始化浏览器失败")
            return
        
        print("\n🎯 开始执行重命名任务...")
        
        # 处理每个测试数据
        results = []
        for i, data in enumerate(test_data, 1):
            print(f"\n--- 任务 {i}/{len(test_data)} ---")
            
            result = await automation.process_rename_task(
                data["拍摄演员名称"],
                data["ID"]
            )
            
            # 记录结果
            results.append({
                "ID": data["ID"],
                "演员名称": data["拍摄演员名称"],
                "目标命名": data["正确命名"],
                "结果": result
            })
            
            # 更新Excel文件中的状态
            if "成功" in result:
                update_rename_status(data["ID"], "是")
            else:
                update_rename_status(data["ID"], "重命名失败")
            
            # 等待一段时间再处理下一个，并确保页面状态清理
            if i < len(test_data):
                print("⏳ 等待5秒后处理下一个，确保页面状态清理...")
                await asyncio.sleep(5)

                # 额外清理：确保没有残留的弹窗或遮罩
                try:
                    remaining_modals = await automation.page.query_selector_all('.ant-modal')
                    mask_elements = await automation.page.query_selector_all('.ant-modal-mask')
                    if remaining_modals or mask_elements:
                        print(f"🔧 清理残留元素：{len(remaining_modals)}个弹窗，{len(mask_elements)}个遮罩")
                        await automation.page.keyboard.press('Escape')
                        await asyncio.sleep(2)
                except:
                    pass
        
        # 显示测试结果
        print("\n" + "=" * 60)
        print("📊 测试结果汇总")
        print("=" * 60)
        
        for result in results:
            status_icon = "✅" if "成功" in result["结果"] else "❌"
            print(f"{status_icon} {result['目标命名']}: {result['结果']}")
        
        # 统计
        success_count = sum(1 for r in results if "成功" in r["结果"])
        total_count = len(results)
        
        print(f"\n📈 成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        if success_count == total_count:
            print("🎉 所有重命名任务都成功完成！")
        else:
            print("⚠️ 部分任务失败，请检查日志信息")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n🔧 清理资源...")
        await automation.close_browser()
        print("✓ 测试完成")


def main():
    """主函数"""
    try:
        # 检查依赖
        try:
            import playwright
        except ImportError:
            print("❌ 缺少playwright依赖，请运行: pip install playwright")
            print("然后运行: playwright install chromium")
            return
        
        # 运行测试
        asyncio.run(run_rename_test())
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
