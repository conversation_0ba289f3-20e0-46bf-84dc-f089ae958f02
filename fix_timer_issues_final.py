"""
最终修复定时任务问题
"""

import os
import sys
import json
import shutil

# 添加src路径
sys.path.append('src')


def fix_timer_issues():
    """修复定时任务问题"""
    print("=" * 60)
    print("🔧 最终修复定时任务问题")
    print("=" * 60)
    
    print("🔍 问题分析:")
    print("  1. ✅ 系统任务创建成功 - 找到 VideoManagement_2_5c826596 任务")
    print("  2. ❌ 程序内定时任务显示'未运行' - 配置文件路径问题")
    print("  3. ❌ 配置文件在错误位置 - 在src目录而不是项目根目录")
    print("")
    
    print("🔧 修复步骤:")
    
    # 步骤1：移动配置文件到正确位置
    print("  1. 移动配置文件到正确位置...")
    src_config = "src/video_management_schedule_config.json"
    dst_config = "video_management_schedule_config.json"
    
    if os.path.exists(src_config):
        if os.path.exists(dst_config):
            print(f"    ⚠️ 目标配置文件已存在，备份原文件...")
            shutil.copy2(dst_config, f"{dst_config}.backup")
        
        shutil.move(src_config, dst_config)
        print(f"    ✅ 配置文件已移动: {src_config} -> {dst_config}")
    else:
        print(f"    ⚠️ 源配置文件不存在: {src_config}")
    
    # 步骤2：检查配置文件内容
    print("  2. 检查配置文件内容...")
    if os.path.exists(dst_config):
        with open(dst_config, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"    ✅ 配置文件加载成功，包含 {len(config)} 个任务")
        
        for task_id, task_data in config.items():
            task_name = task_data.get('name', 'N/A')
            task_type = task_data.get('task_type', 'N/A')
            enabled = task_data.get('enabled', False)
            interval = task_data.get('interval_minutes', 0)
            
            print(f"    📝 任务: {task_name}")
            print(f"       类型: {task_type}")
            print(f"       启用: {enabled}")
            print(f"       间隔: {interval} 分钟")
            
            if task_type == "internal" and enabled and interval > 0:
                print(f"       ✅ 程序内定时任务配置正确")
            elif task_type == "internal":
                print(f"       ⚠️ 程序内定时任务配置有问题")
    else:
        print(f"    ❌ 配置文件不存在: {dst_config}")
    
    print("")
    print("🚀 修复完成建议:")
    print("  1. 重启程序以应用配置文件路径修复")
    print("  2. 检查程序内定时任务是否正常启动")
    print("  3. 观察任务是否按时触发")
    print("  4. 检查日志输出")
    print("")


def create_test_instructions():
    """创建测试说明"""
    print("=" * 40)
    print("📋 测试说明")
    print("=" * 40)
    
    print("重启程序后的测试步骤:")
    print("")
    print("1. 检查程序内定时任务:")
    print("   - 打开视频管理定时任务界面")
    print("   - 查看'程序内定时'标签页")
    print("   - 检查任务状态是否从'未运行'变为正常")
    print("   - 观察'最后运行'列是否会更新")
    print("")
    print("2. 创建新的测试任务:")
    print("   - 任务名称: 测试任务")
    print("   - 任务类型: 程序内定时")
    print("   - 重复间隔: 1分钟（快速测试）")
    print("   - 持续时间: 5分钟（限制测试时间）")
    print("   - 启用任务: 勾选")
    print("")
    print("3. 观察日志输出:")
    print("   - 切换到数字人页面")
    print("   - 查看右侧日志区域")
    print("   - 寻找 [视频管理] 开头的日志")
    print("   - 应该看到:")
    print("     [视频管理] 程序内定时任务已启动：测试任务，间隔 1 分钟，持续 5 小时")
    print("     [视频管理] 定时任务触发: 测试任务")
    print("     [视频管理] 视频管理脚本已启动，进程ID: XXXX")
    print("")
    print("4. 检查脚本执行:")
    print("   - 查看 feiyingshuziren/log/今天日期/ 目录")
    print("   - 寻找最新的 video_management_auto_*.log 文件")
    print("   - 检查脚本是否正确执行")
    print("")
    print("5. 系统任务测试:")
    print("   - 创建系统定时任务")
    print("   - 打开Windows任务计划程序")
    print("   - 查找 VideoManagement_ 开头的任务")
    print("   - 检查任务状态和下次运行时间")
    print("")


def show_current_status():
    """显示当前状态"""
    print("=" * 40)
    print("📊 当前状态")
    print("=" * 40)
    
    print("✅ 已修复的问题:")
    print("  1. 系统任务创建失败 - create_vbs_file参数问题")
    print("  2. 程序内定时任务不继承父类功能 - 初始化问题")
    print("  3. 配置文件路径错误 - 相对路径问题")
    print("  4. 任务触发信号参数不匹配")
    print("  5. 脚本执行逻辑缺失")
    print("")
    
    print("✅ 验证成功的功能:")
    print("  1. 系统任务创建 - VideoManagement_2_5c826596 任务存在")
    print("  2. schtasks命令正常工作")
    print("  3. VBS和BAT文件存在")
    print("  4. 脚本参数支持正常")
    print("")
    
    print("🔄 待验证的功能:")
    print("  1. 程序内定时任务正常启动")
    print("  2. 定时器按时触发")
    print("  3. 脚本正确执行")
    print("  4. 日志正确记录")
    print("")
    
    print("⚠️ 注意事项:")
    print("  1. 需要重启程序以应用配置文件路径修复")
    print("  2. 程序内定时任务需要程序保持运行")
    print("  3. 系统任务不依赖程序运行状态")
    print("  4. 测试时建议使用较短的时间间隔")
    print("")


def check_files_status():
    """检查文件状态"""
    print("=" * 40)
    print("📂 文件状态检查")
    print("=" * 40)
    
    files_to_check = [
        "video_management_schedule_config.json",
        "src/video_management_runner.py",
        "run_video_management_auto.bat",
        "run_video_management_scheduled.vbs"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} - {size} 字节")
        else:
            print(f"❌ {file_path} - 不存在")
    
    print("")
    print("📁 目录状态:")
    log_dir = "feiyingshuziren/log"
    if os.path.exists(log_dir):
        print(f"✅ {log_dir} - 存在")
        
        # 检查今天的日志目录
        from datetime import datetime
        today = datetime.now().strftime("%Y-%m-%d")
        today_log_dir = os.path.join(log_dir, today)
        
        if os.path.exists(today_log_dir):
            log_files = [f for f in os.listdir(today_log_dir) if f.endswith('.log')]
            print(f"✅ {today_log_dir} - 包含 {len(log_files)} 个日志文件")
        else:
            print(f"⚠️ {today_log_dir} - 不存在（首次运行时会自动创建）")
    else:
        print(f"⚠️ {log_dir} - 不存在（首次运行时会自动创建）")


if __name__ == "__main__":
    fix_timer_issues()
    create_test_instructions()
    show_current_status()
    check_files_status()
