"""
系统工具模块 - 提供系统状态检测功能
"""

import os
import subprocess
import psutil
import time
from typing import Optional, Dict, Any


class SystemUtils:
    """系统工具类"""
    
    @staticmethod
    def is_system_locked() -> bool:
        """
        检测系统是否处于锁屏状态
        
        Returns:
            bool: True表示系统已锁屏，False表示系统未锁屏
        """
        try:
            # 方法1: 检查LogonUI进程（Windows锁屏界面进程）
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if proc.info['name'].lower() == 'logonui.exe':
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # 方法2: 检查winlogon进程的状态
            try:
                result = subprocess.run([
                    'powershell', '-Command',
                    'Get-Process -Name "winlogon" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty SessionId'
                ], capture_output=True, text=True, timeout=5)
                
                if result.returncode == 0 and result.stdout.strip():
                    # 如果有多个winlogon进程，可能表示有锁屏会话
                    session_ids = result.stdout.strip().split('\n')
                    if len(session_ids) > 1:
                        return True
            except Exception:
                pass
            
            # 方法3: 检查当前会话状态
            try:
                result = subprocess.run([
                    'powershell', '-Command',
                    '(Get-WmiObject -Class Win32_ComputerSystem).UserName'
                ], capture_output=True, text=True, timeout=5)
                
                # 如果没有用户登录或返回空，可能是锁屏状态
                if result.returncode == 0 and not result.stdout.strip():
                    return True
            except Exception:
                pass
            
            return False
            
        except Exception as e:
            # 如果检测失败，为了安全起见，假设没有锁屏
            print(f"锁屏检测失败: {e}")
            return False
    
    @staticmethod
    def wait_for_unlock(max_wait_minutes: int = 60) -> bool:
        """
        等待系统解锁
        
        Args:
            max_wait_minutes: 最大等待时间（分钟）
            
        Returns:
            bool: True表示系统已解锁，False表示等待超时
        """
        start_time = time.time()
        max_wait_seconds = max_wait_minutes * 60
        
        print(f"🔒 检测到系统锁屏，等待解锁... (最大等待{max_wait_minutes}分钟)")
        
        while time.time() - start_time < max_wait_seconds:
            if not SystemUtils.is_system_locked():
                elapsed_minutes = (time.time() - start_time) / 60
                print(f"🔓 系统已解锁，等待时间: {elapsed_minutes:.1f}分钟")
                return True
            
            # 每分钟检查一次
            time.sleep(60)
            elapsed_minutes = (time.time() - start_time) / 60
            remaining_minutes = max_wait_minutes - elapsed_minutes
            print(f"⏳ 继续等待解锁... (已等待{elapsed_minutes:.1f}分钟，剩余{remaining_minutes:.1f}分钟)")
        
        print(f"⏰ 等待解锁超时 ({max_wait_minutes}分钟)")
        return False
    
    @staticmethod
    def get_chrome_processes() -> list:
        """
        获取所有Chrome进程信息
        
        Returns:
            list: Chrome进程信息列表
        """
        chrome_processes = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'create_time', 'status']):
                try:
                    if 'chrome' in proc.info['name'].lower():
                        chrome_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'create_time': proc.info['create_time'],
                            'status': proc.info['status']
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            print(f"获取Chrome进程失败: {e}")
        
        return chrome_processes
    
    @staticmethod
    def is_process_alive(pid: int) -> bool:
        """
        检查指定PID的进程是否存活
        
        Args:
            pid: 进程ID
            
        Returns:
            bool: True表示进程存活，False表示进程不存在
        """
        try:
            return psutil.pid_exists(pid)
        except Exception:
            return False
    
    @staticmethod
    def get_system_info() -> Dict[str, Any]:
        """
        获取系统信息
        
        Returns:
            dict: 系统信息字典
        """
        try:
            return {
                'locked': SystemUtils.is_system_locked(),
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'chrome_processes': len(SystemUtils.get_chrome_processes()),
                'uptime_hours': (time.time() - psutil.boot_time()) / 3600
            }
        except Exception as e:
            return {'error': str(e)}
    
    @staticmethod
    def should_delay_task() -> tuple[bool, str]:
        """
        判断是否应该延迟执行任务
        
        Returns:
            tuple: (是否延迟, 延迟原因)
        """
        # 检查锁屏状态
        if SystemUtils.is_system_locked():
            return True, "系统处于锁屏状态"
        
        # 检查系统资源
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent
            
            if cpu_percent > 90:
                return True, f"CPU使用率过高: {cpu_percent:.1f}%"
            
            if memory_percent > 90:
                return True, f"内存使用率过高: {memory_percent:.1f}%"
        except Exception:
            pass
        
        return False, ""


def test_system_utils():
    """测试系统工具功能"""
    print("=" * 50)
    print("🧪 系统工具测试")
    print("=" * 50)
    
    # 测试锁屏检测
    is_locked = SystemUtils.is_system_locked()
    print(f"🔒 系统锁屏状态: {'已锁屏' if is_locked else '未锁屏'}")
    
    # 测试Chrome进程检测
    chrome_procs = SystemUtils.get_chrome_processes()
    print(f"🌐 Chrome进程数量: {len(chrome_procs)}")
    
    # 测试系统信息
    sys_info = SystemUtils.get_system_info()
    print(f"💻 系统信息: {sys_info}")
    
    # 测试任务延迟判断
    should_delay, reason = SystemUtils.should_delay_task()
    print(f"⏸️ 是否延迟任务: {'是' if should_delay else '否'}")
    if should_delay:
        print(f"   延迟原因: {reason}")


if __name__ == "__main__":
    test_system_utils()
