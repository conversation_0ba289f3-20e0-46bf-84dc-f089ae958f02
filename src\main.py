"""  """#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Fish Audio声音克隆工具 - GUI版本
"""

import sys
import os
import traceback
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QCoreApplication, Qt

# 确保资源文件路径正确
def setup_environment():
    """设置应用环境，确保路径正确"""
    # 获取应用程序所在目录
    if getattr(sys, 'frozen', False):
        # 如果是打包后的可执行文件
        application_path = os.path.dirname(sys.executable)
        print(f"运行模式: 打包应用，路径: {application_path}")
        
        # 将图标目录添加到QT_PLUGIN_PATH环境变量
        icons_path = os.path.join(application_path, "_internal", "ui", "icons")
        if os.path.exists(icons_path):
            print(f"添加图标路径到环境变量: {icons_path}")
            # 创建_internal/ui/icons的软链接到ui/icons
            ui_icons_dir = os.path.join(application_path, "ui", "icons")
            if not os.path.exists(ui_icons_dir):
                os.makedirs(os.path.dirname(ui_icons_dir), exist_ok=True)
                try:
                    # 在Windows上创建目录符号链接
                    if not os.path.exists(ui_icons_dir):
                        os.system(f'mklink /D "{ui_icons_dir}" "{icons_path}"')
                        print(f"创建图标目录符号链接: {ui_icons_dir} -> {icons_path}")
                except Exception as e:
                    print(f"创建符号链接失败: {str(e)}")
                    # 如果符号链接失败，尝试直接复制
                    try:
                        import shutil
                        if os.path.exists(icons_path) and not os.path.exists(ui_icons_dir):
                            os.makedirs(os.path.dirname(ui_icons_dir), exist_ok=True)
                            shutil.copytree(icons_path, ui_icons_dir)
                            print(f"已复制图标目录: {icons_path} -> {ui_icons_dir}")
                    except Exception as e2:
                        print(f"复制图标目录失败: {str(e2)}")
    else:
        # 如果是源码运行
        application_path = os.path.dirname(os.path.abspath(__file__))
        print(f"运行模式: 源码开发，路径: {application_path}")

        # 添加src目录到Python路径，确保能正确导入模块
        if application_path not in sys.path:
            sys.path.insert(0, application_path)
            print(f"添加Python路径: {application_path}")
    
    # 设置工作目录
    os.chdir(application_path)
    print(f"当前工作目录: {os.getcwd()}")
    
    # 创建必要的目录
    output_path = "../output" if not getattr(sys, 'frozen', False) else "output"
    if not os.path.exists(output_path):
        os.makedirs(output_path)
        print(f"创建输出目录: {output_path}")

    # 创建配置目录
    config_path = "../config" if not getattr(sys, 'frozen', False) else "config"
    if not os.path.exists(config_path):
        os.makedirs(config_path)
        print(f"创建配置目录: {config_path}")
    
    # 尝试加载各种依赖
    try:
        import lxml
        print(f"LXML版本: {lxml.__version__}")
    except ImportError:
        print("警告: 未能加载LXML，HTML解析可能不可用")
    
    print(f"Python版本: {sys.version}")
    print(f"PySide6 Qt库路径: {os.environ.get('QT_PLUGIN_PATH', '未设置')}")

def load_styles(app):
    """加载样式表，确保UI界面正确展示"""
    # 尝试多个可能的样式文件路径
    style_paths = [
        os.path.join("src", "ui", "styles.qss"),
        os.path.join("ui", "styles.qss"),
        os.path.join("_internal", "ui", "styles.qss"),
        os.path.join(os.path.dirname(sys.executable), "src", "ui", "styles.qss"),
        os.path.join(os.path.dirname(sys.executable), "ui", "styles.qss"),
        os.path.join(os.path.dirname(sys.executable), "_internal", "ui", "styles.qss"),
    ]
    
    # 逐一尝试加载样式文件
    for style_file in style_paths:
        if os.path.exists(style_file):
            try:
                with open(style_file, "r", encoding="utf-8") as f:
                    style_content = f.read()
                    app.setStyleSheet(style_content)
                    print(f"成功加载样式文件: {style_file}")
                    return True
            except Exception as e:
                print(f"加载样式文件 {style_file} 时出错: {str(e)}")
    
    print("警告: 未能加载任何样式文件")
    return False

class AsyncioErrorSuppressor:
    """精确抑制特定的asyncio垃圾回收错误"""

    def __init__(self):
        self.original_stderr = sys.stderr
        self.buffer = []
        self.in_ignored_exception = False

    def write(self, text):
        # 检查是否是"Exception ignored in:"开头的错误块
        if "Exception ignored in:" in text:
            # 检查是否是我们要过滤的特定错误
            if any(pattern in text for pattern in [
                "_ProactorBasePipeTransport.__del__",
                "BaseSubprocessTransport.__del__",
                "StreamWriter.__del__"
            ]):
                self.in_ignored_exception = True
                self.buffer = [text]  # 开始缓存这个错误块
                return
            else:
                # 不是我们要过滤的错误，正常输出
                self.in_ignored_exception = False
                self.original_stderr.write(text)
                return

        # 如果在被忽略的异常块中
        if self.in_ignored_exception:
            self.buffer.append(text)

            # 检查是否包含我们要过滤的特定错误信息
            full_text = ''.join(self.buffer)
            if any(error in full_text for error in [
                "Event loop is closed",
                "I/O operation on closed pipe"
            ]):
                # 确认是我们要过滤的错误，丢弃整个错误块
                if text.strip() == "" or "Exception ignored in:" in text:
                    # 错误块结束，清空缓存
                    self.buffer = []
                    self.in_ignored_exception = False
                return
            else:
                # 不是我们要过滤的错误，输出缓存的内容
                for buffered_text in self.buffer:
                    self.original_stderr.write(buffered_text)
                self.buffer = []
                self.in_ignored_exception = False
                return

        # 正常的错误信息，直接输出
        self.original_stderr.write(text)

    def flush(self):
        # 如果有缓存的内容且不是要过滤的错误，输出它们
        if self.buffer and not self.in_ignored_exception:
            for text in self.buffer:
                self.original_stderr.write(text)
            self.buffer = []
        self.original_stderr.flush()


def main():
    """主函数"""
    try:
        # 确保sys和os已导入
        import sys
        import os

        # 启用asyncio错误抑制
        if sys.platform == "win32":
            sys.stderr = AsyncioErrorSuppressor()
            print("✅ 已启用asyncio错误抑制")

        # 设置环境
        setup_environment()

        # 创建应用
        QCoreApplication.setAttribute(Qt.AA_ShareOpenGLContexts)
        app = QApplication(sys.argv)
        app.setApplicationName("光流一站式口播助手")

        # 设置应用程序图标（用于任务栏）
        from PySide6.QtGui import QIcon

        # 根据运行环境确定图标路径
        if getattr(sys, 'frozen', False):
            # 打包后的环境
            base_path = os.path.dirname(sys.executable)
            icon_paths = [
                os.path.join(base_path, "_internal", "ui", "icons", "guangliu.ico"),
                os.path.join(base_path, "_internal", "ui", "icons", "app.ico"),
                os.path.join(base_path, "ui", "icons", "guangliu.ico"),
                os.path.join(base_path, "ui", "icons", "app.ico")
            ]
        else:
            # 开发环境
            icon_paths = [
                "src/ui/icons/guangliu.ico",
                "src/ui/icons/app.ico",
                "ui/icons/guangliu.ico",
                "ui/icons/app.ico"
            ]

        for icon_path in icon_paths:
            if os.path.exists(icon_path):
                app.setWindowIcon(QIcon(icon_path))
                print(f"设置应用程序图标: {icon_path}")
                break
        else:
            print("未找到应用程序图标文件")
        
        # 导入MainWindow
        from ui.main_window import MainWindow
        print("成功导入 MainWindow")
        
        # 加载样式表
        load_styles(app)
        
        # 打印依赖库信息
        print("环境信息:")
        try:
            import pandas
            print(f"Pandas 版本: {pandas.__version__}")
        except:
            print("Pandas 导入失败")
        
        try:
            import openpyxl
            print(f"Openpyxl 版本: {openpyxl.__version__}")
        except:
            print("Openpyxl 导入失败")
        
        # 创建主窗口
        window = MainWindow()
        window.show()

        # 强制显示窗口
        window.raise_()
        window.activateWindow()

        # 运行应用
        sys.exit(app.exec())
    except Exception as e:
        # 捕获所有异常
        import sys
        error_message = f"程序启动时发生错误:\n{str(e)}\n\n详细信息:\n{traceback.format_exc()}"
        print(error_message)

        # 如果GUI已经初始化，则显示错误对话框
        try:
            if 'app' in locals():
                QMessageBox.critical(None, "启动错误", error_message)
        except:
            pass

        sys.exit(1)

if __name__ == "__main__":
    main() 