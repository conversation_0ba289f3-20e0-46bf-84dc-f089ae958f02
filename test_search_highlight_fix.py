#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试搜索高亮功能修复
验证视频管理和音频管理模块的搜索高亮显示
"""

import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_video_management_search_highlight():
    """测试视频管理模块搜索高亮功能"""
    print("=== 测试视频管理模块搜索高亮功能 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查视频管理搜索高亮功能
        vm_highlight_features = [
            "def highlight_vm_search_result\\(self\\):",  # 高亮方法
            "item.setBackground\\(QColor\\(\"#ffeb3b\"\\)\\)",  # 黄色高亮
            "def clear_vm_search_highlights\\(self\\):",  # 清除高亮方法
            "self.vm_table.scrollToItem\\(item\\)",  # 滚动到项目
            "self.highlight_vm_search_result\\(\\)",  # 调用高亮
        ]
        
        import re
        for feature in vm_highlight_features:
            if re.search(feature, content):
                print(f"✅ 视频管理高亮功能存在: {feature}")
            else:
                print(f"❌ 视频管理高亮功能缺失: {feature}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 视频管理搜索高亮测试失败: {str(e)}")
        return False

def test_voice_management_search_highlight():
    """测试音频管理模块搜索高亮功能"""
    print("\n=== 测试音频管理模块搜索高亮功能 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查音频管理搜索高亮功能
        voice_highlight_features = [
            "def highlight_voice_search_result\\(self\\):",  # 高亮方法
            "def clear_voice_search_highlights\\(self\\):",  # 清除高亮方法
            "def voice_search_table_async\\(self\\):",  # 异步搜索方法
            "def voice_search_in_table\\(self\\):",  # 表格搜索方法
            "def voice_search_completed\\(self\\):",  # 搜索完成方法
            "def voice_search_table\\(self, next=False, prev=False\\):",  # 导航方法
        ]
        
        import re
        for feature in voice_highlight_features:
            if re.search(feature, content):
                print(f"✅ 音频管理高亮功能存在: {feature}")
            else:
                print(f"❌ 音频管理高亮功能缺失: {feature}")
                return False
        
        # 检查音频管理是否使用相同的高亮颜色
        if re.search(r'item\.setBackground\(QColor\("#ffeb3b"\)\).*voice', content, re.DOTALL):
            print("✅ 音频管理使用相同的高亮颜色")
        else:
            print("❌ 音频管理未使用相同的高亮颜色")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 音频管理搜索高亮测试失败: {str(e)}")
        return False

def test_search_navigation_buttons():
    """测试搜索导航按钮连接"""
    print("\n=== 测试搜索导航按钮连接 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查视频管理导航按钮
        vm_navigation = [
            "lambda: self.vm_search_table\\(prev=True\\)",  # 视频管理上一个
            "lambda: self.vm_search_table\\(next=True\\)",  # 视频管理下一个
        ]
        
        # 检查音频管理导航按钮
        voice_navigation = [
            "lambda: self.voice_search_table\\(prev=True\\)",  # 音频管理上一个
            "lambda: self.voice_search_table\\(next=True\\)",  # 音频管理下一个
        ]
        
        import re
        
        # 验证视频管理导航
        for nav in vm_navigation:
            if re.search(nav, content):
                print(f"✅ 视频管理导航按钮连接正确: {nav}")
            else:
                print(f"❌ 视频管理导航按钮连接错误: {nav}")
                return False
        
        # 验证音频管理导航
        for nav in voice_navigation:
            if re.search(nav, content):
                print(f"✅ 音频管理导航按钮连接正确: {nav}")
            else:
                print(f"❌ 音频管理导航按钮连接错误: {nav}")
                return False
        
        # 检查是否还有错误的连接（音频管理按钮连接到视频管理方法）
        wrong_connections = [
            "lambda: self.vm_search_table.*# 音频管理",  # 错误连接
        ]
        
        for wrong in wrong_connections:
            if re.search(wrong, content):
                print(f"❌ 仍存在错误的按钮连接: {wrong}")
                return False
            else:
                print(f"✅ 已修复错误的按钮连接: {wrong}")
        
        return True
        
    except Exception as e:
        print(f"❌ 搜索导航按钮测试失败: {str(e)}")
        return False

def test_search_signal_blocking():
    """测试搜索过程中的信号阻止"""
    print("\n=== 测试搜索过程中的信号阻止 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查信号阻止功能
        signal_blocking_features = [
            "self.voice_table.blockSignals\\(True\\)",  # 阻止信号
            "self.voice_table.blockSignals\\(False\\)",  # 恢复信号
            "was_blocked = self.voice_table.signalsBlocked\\(\\)",  # 保存信号状态
            "finally:",  # 确保信号恢复
        ]
        
        import re
        for feature in signal_blocking_features:
            if re.search(feature, content):
                print(f"✅ 音频管理信号阻止功能存在: {feature}")
            else:
                print(f"❌ 音频管理信号阻止功能缺失: {feature}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 信号阻止测试失败: {str(e)}")
        return False

def test_search_consistency():
    """测试搜索功能一致性"""
    print("\n=== 测试搜索功能一致性 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查两个模块是否使用相同的搜索模式
        consistency_features = [
            # 高亮颜色一致性
            ("#ffeb3b", "黄色高亮颜色"),
            # 搜索结果显示一致性
            ("搜索结果.*?/", "搜索结果位置显示"),
            # 清除背景色方式一致性
            ("setBackground\\(QColor\\(\\)\\)", "清除背景色方式"),
            # 滚动到项目一致性
            ("scrollToItem\\(item\\)", "滚动到项目"),
        ]
        
        import re
        for pattern, description in consistency_features:
            # 检查视频管理模块
            vm_matches = len(re.findall(pattern, content))
            # 检查音频管理模块
            voice_matches = len(re.findall(pattern, content))
            
            if vm_matches > 0 and voice_matches > 0:
                print(f"✅ {description}一致性检查通过: VM({vm_matches}) Voice({voice_matches})")
            else:
                print(f"❌ {description}一致性检查失败: VM({vm_matches}) Voice({voice_matches})")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 搜索功能一致性测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 搜索高亮功能修复测试")
    print("=" * 60)
    
    # 测试视频管理模块搜索高亮
    if not test_video_management_search_highlight():
        print("❌ 视频管理模块搜索高亮测试失败")
        return False
    
    # 测试音频管理模块搜索高亮
    if not test_voice_management_search_highlight():
        print("❌ 音频管理模块搜索高亮测试失败")
        return False
    
    # 测试搜索导航按钮连接
    if not test_search_navigation_buttons():
        print("❌ 搜索导航按钮连接测试失败")
        return False
    
    # 测试搜索过程中的信号阻止
    if not test_search_signal_blocking():
        print("❌ 搜索信号阻止测试失败")
        return False
    
    # 测试搜索功能一致性
    if not test_search_consistency():
        print("❌ 搜索功能一致性测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！")
    print("\n📋 修复总结:")
    print("✅ 1. 音频管理模块搜索高亮功能:")
    print("  - 添加了完整的搜索高亮显示功能")
    print("  - 使用与视频管理模块相同的黄色高亮")
    print("  - 支持搜索结果导航（上一个/下一个）")
    print("  - 添加了搜索进度和结果统计")
    
    print("\n✅ 2. 搜索导航按钮修复:")
    print("  - 修复了音频管理模块导航按钮的错误连接")
    print("  - 确保每个模块使用自己的搜索方法")
    print("  - 添加了完整的搜索导航功能")
    
    print("\n✅ 3. 搜索功能一致性:")
    print("  - 两个模块使用相同的高亮颜色和样式")
    print("  - 相同的搜索结果显示格式")
    print("  - 一致的用户交互体验")
    
    print("\n✅ 4. 信号阻止机制:")
    print("  - 搜索过程中阻止表格信号")
    print("  - 防止高亮操作触发其他事件")
    print("  - 使用finally块确保信号恢复")
    
    print("\n🎯 解决的问题:")
    print("  - 音频管理模块搜索无高亮显示 → 添加完整高亮功能")
    print("  - 导航按钮连接错误 → 修复按钮连接到正确方法")
    print("  - 搜索体验不一致 → 统一搜索功能和样式")
    print("  - 缺少搜索导航 → 添加上一个/下一个结果导航")
    
    print("\n🚀 预期效果:")
    print("  - 音频管理模块搜索后有黄色高亮显示")
    print("  - 可以使用导航按钮在搜索结果间跳转")
    print("  - 搜索体验与视频管理模块完全一致")
    print("  - 搜索过程流畅，无意外触发其他操作")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
