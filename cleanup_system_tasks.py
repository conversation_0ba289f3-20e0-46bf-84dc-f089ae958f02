"""
清理系统任务并修复删除逻辑
"""

import os
import sys
import subprocess

# 添加src路径
sys.path.append('src')


def list_all_video_management_tasks():
    """列出所有视频管理相关的系统任务"""
    print("=" * 60)
    print("🔍 查找所有视频管理相关的系统任务")
    print("=" * 60)
    
    try:
        # 查询所有任务
        result = subprocess.run([
            "schtasks", "/query", "/fo", "table"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            vm_tasks = []
            
            for line in lines:
                if 'VideoManagement' in line or 'FishWin' in line:
                    # 解析任务信息
                    parts = line.split()
                    if len(parts) >= 3:
                        task_name = parts[0]
                        next_run = ' '.join(parts[1:3]) if len(parts) >= 3 else 'N/A'
                        status = parts[3] if len(parts) >= 4 else 'N/A'
                        
                        vm_tasks.append({
                            'name': task_name,
                            'next_run': next_run,
                            'status': status,
                            'line': line.strip()
                        })
            
            if vm_tasks:
                print(f"✅ 找到 {len(vm_tasks)} 个相关任务:")
                for i, task in enumerate(vm_tasks, 1):
                    print(f"  {i}. {task['name']}")
                    print(f"     下次运行: {task['next_run']}")
                    print(f"     状态: {task['status']}")
                    print("")
                
                return vm_tasks
            else:
                print("✅ 没有找到相关任务")
                return []
        else:
            print(f"❌ 查询任务失败: {result.stderr}")
            return []
            
    except Exception as e:
        print(f"❌ 查询任务异常: {e}")
        return []


def delete_system_task(task_name):
    """删除指定的系统任务"""
    try:
        print(f"🗑️ 删除任务: {task_name}")
        
        result = subprocess.run([
            "schtasks", "/delete", "/tn", task_name, "/f"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"  ✅ 删除成功")
            return True
        else:
            print(f"  ❌ 删除失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"  ❌ 删除异常: {e}")
        return False


def batch_delete_tasks(tasks):
    """批量删除任务"""
    print("\n" + "=" * 40)
    print("🗑️ 批量删除任务")
    print("=" * 40)
    
    if not tasks:
        print("没有任务需要删除")
        return
    
    print(f"准备删除 {len(tasks)} 个任务:")
    for task in tasks:
        print(f"  - {task['name']}")
    print("")
    
    confirm = input("确认删除所有这些任务吗? (y/n): ").lower().strip()
    if confirm != 'y':
        print("已取消删除")
        return
    
    success_count = 0
    for task in tasks:
        if delete_system_task(task['name']):
            success_count += 1
    
    print(f"\n📊 删除结果: {success_count}/{len(tasks)} 成功")


def check_delete_logic():
    """检查删除逻辑"""
    print("\n" + "=" * 40)
    print("🔍 检查删除逻辑")
    print("=" * 40)
    
    print("当前删除逻辑分析:")
    print("")
    print("1. 数字人任务:")
    print("   创建: FishWin_{task.name}_{task.task_id[:8]}")
    print("   删除: FishWin_{task.name}_{task.task_id[:8]}")
    print("   状态: ✅ 逻辑一致")
    print("")
    
    print("2. 视频管理任务:")
    print("   创建: VideoManagement_{task.name}_{task.task_id[:8]}")
    print("   删除: 需要检查是否使用相同逻辑")
    print("")
    
    print("🔧 可能的问题:")
    print("  1. VideoManagementScheduleManager 继承了基类的删除方法")
    print("  2. 基类删除方法使用 FishWin_ 前缀")
    print("  3. 但创建时使用 VideoManagement_ 前缀")
    print("  4. 导致删除时找不到任务")
    print("")


def fix_delete_logic():
    """修复删除逻辑"""
    print("=" * 40)
    print("🔧 修复删除逻辑")
    print("=" * 40)
    
    print("需要在 VideoManagementScheduleManager 中重写 delete_system_task 方法:")
    print("")
    print("修复代码:")
    print("""
def delete_system_task(self, task):
    \"\"\"删除系统定时任务 - 视频管理专用\"\"\"
    try:
        # 使用VideoManagement前缀，与创建时保持一致
        task_name = f"VideoManagement_{task.name}_{task.task_id[:8]}"
        result = subprocess.run([
            "schtasks", "/delete", "/tn", task_name, "/f"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            self.log_message.emit(f"系统定时任务已删除：{task.name}")
        else:
            self.log_message.emit(f"删除系统任务失败：{result.stderr}")

    except Exception as e:
        self.log_message.emit(f"删除系统任务时出错：{e}")
""")
    print("")


def test_task_name_generation():
    """测试任务名称生成"""
    print("\n" + "=" * 40)
    print("🧪 测试任务名称生成")
    print("=" * 40)
    
    # 模拟任务对象
    class MockTask:
        def __init__(self, name, task_id):
            self.name = name
            self.task_id = task_id
    
    # 测试不同的任务
    test_tasks = [
        MockTask("12", "22c361bf-1234-5678-9abc-def012345678"),
        MockTask("1", "a6e91526-1234-5678-9abc-def012345678"),
        MockTask("测试任务", "12345678-1234-5678-9abc-def012345678")
    ]
    
    print("任务名称生成测试:")
    for task in test_tasks:
        video_name = f"VideoManagement_{task.name}_{task.task_id[:8]}"
        fish_name = f"FishWin_{task.name}_{task.task_id[:8]}"
        
        print(f"  任务: {task.name} (ID: {task.task_id[:8]})")
        print(f"    视频管理格式: {video_name}")
        print(f"    数字人格式:   {fish_name}")
        print("")


def create_manual_cleanup_script():
    """创建手动清理脚本"""
    print("=" * 40)
    print("📝 创建手动清理脚本")
    print("=" * 40)
    
    script_content = '''@echo off
echo 手动清理视频管理系统任务
echo ========================================

echo 删除 VideoManagement_12_22c361bf
schtasks /delete /tn VideoManagement_12_22c361bf /f

echo 删除 VideoManagement_1_a6e91526
schtasks /delete /tn VideoManagement_1_a6e91526 /f

echo 查询剩余的相关任务
schtasks /query /fo table | findstr VideoManagement
schtasks /query /fo table | findstr FishWin

echo ========================================
echo 清理完成
pause
'''
    
    try:
        with open("manual_cleanup_tasks.bat", 'w', encoding='gbk') as f:
            f.write(script_content)
        print("✅ 已创建手动清理脚本: manual_cleanup_tasks.bat")
        print("  可以右键以管理员身份运行此脚本")
    except Exception as e:
        print(f"❌ 创建脚本失败: {e}")


def show_next_steps():
    """显示下一步操作"""
    print("\n" + "=" * 40)
    print("📋 下一步操作")
    print("=" * 40)
    
    print("立即解决方案:")
    print("  1. 手动删除现有任务（推荐）")
    print("  2. 修复程序中的删除逻辑")
    print("  3. 重新创建系统任务")
    print("")
    
    print("手动删除方法:")
    print("  方法1: 使用本脚本的批量删除功能")
    print("  方法2: 使用生成的BAT脚本")
    print("  方法3: 打开任务计划程序手动删除")
    print("  方法4: 使用PowerShell命令")
    print("")
    
    print("PowerShell命令:")
    print("  schtasks /delete /tn VideoManagement_12_22c361bf /f")
    print("  schtasks /delete /tn VideoManagement_1_a6e91526 /f")
    print("")
    
    print("修复程序逻辑:")
    print("  需要在VideoManagementScheduleManager中重写delete_system_task方法")
    print("  确保删除时使用与创建时相同的任务名称格式")
    print("")
    
    print("验证修复:")
    print("  1. 删除所有现有任务")
    print("  2. 重新创建系统任务")
    print("  3. 在程序中删除任务")
    print("  4. 检查系统中是否真的删除了")


if __name__ == "__main__":
    print("🧹 系统任务清理和修复工具")
    print("=" * 60)
    
    # 列出所有相关任务
    tasks = list_all_video_management_tasks()
    
    # 检查删除逻辑
    check_delete_logic()
    
    # 测试任务名称生成
    test_task_name_generation()
    
    # 修复删除逻辑说明
    fix_delete_logic()
    
    # 创建手动清理脚本
    create_manual_cleanup_script()
    
    # 显示下一步操作
    show_next_steps()
    
    # 询问是否批量删除
    if tasks:
        print("\n" + "=" * 60)
        delete_choice = input("是否现在批量删除这些任务? (y/n): ").lower().strip()
        if delete_choice == 'y':
            batch_delete_tasks(tasks)
        
        # 再次查询确认
        print("\n🔍 删除后的任务状态:")
        remaining_tasks = list_all_video_management_tasks()
        if not remaining_tasks:
            print("✅ 所有相关任务已清理完成")
        else:
            print(f"⚠️ 还有 {len(remaining_tasks)} 个任务未删除")
    
    print("\n🎯 总结:")
    print("  1. 现有任务已清理（如果选择了删除）")
    print("  2. 需要修复程序中的删除逻辑")
    print("  3. 重新创建系统任务进行测试")
    print("  4. 验证删除功能是否正常工作")
