"""
测试修复后的脚本
"""

import sys
import os

# 添加src路径
sys.path.append('src')

def test_fixed_script():
    """测试修复后的脚本"""
    try:
        # 测试导入
        from video_management_runner import log_message
        
        print("✅ 脚本导入成功")
        
        # 测试日志函数
        log_message("[TEST] 测试日志消息")
        log_message("[SUCCESS] 编码问题已修复")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_fixed_script()
