#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试搜索高亮根本原因修复
验证CSS样式表不会覆盖动态高亮设置
"""

import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_css_background_color_removed():
    """测试CSS样式表中的背景色设置已移除"""
    print("=== 测试CSS样式表背景色设置已移除 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查QTableWidget::item中是否移除了强制的背景色设置
        import re

        # 提取所有QTableWidget::item样式块
        item_style_blocks = re.findall(r'QTableWidget::item\s*\{([^}]*)\}', content, re.DOTALL)

        problematic_found = False
        for i, block in enumerate(item_style_blocks):
            if 'background-color:' in block and 'white' in block:
                print(f"❌ QTableWidget::item块{i+1}中仍有强制白色背景:")
                print(f"   内容: {block.strip()}")
                problematic_found = True
            else:
                print(f"✅ QTableWidget::item块{i+1}无强制背景色设置")

        if problematic_found:
            return False
        

        
        # 检查是否添加了正确的注释
        correct_comments = [
            "不设置background-color，允许动态高亮",
        ]
        
        for comment in correct_comments:
            if comment in content:
                print(f"✅ 正确注释存在: {comment}")
            else:
                print(f"❌ 正确注释缺失: {comment}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ CSS背景色测试失败: {str(e)}")
        return False

def test_enhanced_highlight_implementation():
    """测试增强的高亮实现"""
    print("\n=== 测试增强的高亮实现 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查增强的高亮功能
        enhanced_features = [
            "highlight_color = QColor\\(\"#ffeb3b\"\\)",  # 高亮颜色变量
            "text_color = QColor\\(\"#000000\"\\)",      # 文字颜色变量
            "self\\..*_table\\.update\\(\\)",            # 强制刷新
            "actual_bg = item\\.background\\(\\)",       # 验证背景色
            "actual_fg = item\\.foreground\\(\\)",       # 验证前景色
            "实际背景色.*actual_bg\\.color\\(\\)\\.name\\(\\)",  # 背景色验证日志
            "实际前景色.*actual_fg\\.color\\(\\)\\.name\\(\\)",  # 前景色验证日志
        ]
        
        import re
        for feature in enhanced_features:
            if re.search(feature, content):
                print(f"✅ 增强高亮功能存在: {feature}")
            else:
                print(f"❌ 增强高亮功能缺失: {feature}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 增强高亮实现测试失败: {str(e)}")
        return False

def test_debug_logging_added():
    """测试调试日志已添加"""
    print("\n=== 测试调试日志已添加 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查调试日志
        debug_logs = [
            "🔍 高亮设置: 行.*列",
            "🔍 设置背景色:.*实际背景色:",
            "🔍 设置前景色:.*实际前景色:",
        ]
        
        import re
        for log in debug_logs:
            if re.search(log, content):
                print(f"✅ 调试日志存在: {log}")
            else:
                print(f"❌ 调试日志缺失: {log}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 调试日志测试失败: {str(e)}")
        return False

def test_table_update_calls():
    """测试表格更新调用"""
    print("\n=== 测试表格更新调用 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查表格更新调用
        update_calls = [
            "self\\.vm_table\\.update\\(\\)",     # 视频管理表格更新
            "self\\.voice_table\\.update\\(\\)",  # 音频管理表格更新
        ]
        
        import re
        for call in update_calls:
            if re.search(call, content):
                print(f"✅ 表格更新调用存在: {call}")
            else:
                print(f"❌ 表格更新调用缺失: {call}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 表格更新调用测试失败: {str(e)}")
        return False

def test_style_consistency():
    """测试样式一致性"""
    print("\n=== 测试样式一致性 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 提取所有表格样式表
        import re
        
        # 数字人模块样式
        dh_style = re.search(r'self\.dh_table\.setStyleSheet\("""(.*?)"""\)', content, re.DOTALL)
        # 视频管理模块样式
        vm_style = re.search(r'self\.vm_table\.setStyleSheet\("""(.*?)"""\)', content, re.DOTALL)
        # 音频管理模块样式
        voice_style = re.search(r'self\.voice_table\.setStyleSheet\("""(.*?)"""\)', content, re.DOTALL)
        
        if not all([dh_style, vm_style, voice_style]):
            print("❌ 无法找到所有模块的样式表")
            return False
        
        # 检查QTableWidget::item部分是否一致
        item_styles = []
        for name, style_match in [("数字人", dh_style), ("视频管理", vm_style), ("音频管理", voice_style)]:
            item_style = re.search(r'QTableWidget::item \{(.*?)\}', style_match.group(1), re.DOTALL)
            if item_style:
                item_styles.append((name, item_style.group(1).strip()))
            else:
                print(f"❌ {name}模块缺少QTableWidget::item样式")
                return False
        
        # 检查所有模块的item样式是否一致
        base_style = item_styles[0][1]
        for name, style in item_styles[1:]:
            if style != base_style:
                print(f"❌ {name}模块的item样式与基准不一致")
                print(f"   基准: {base_style}")
                print(f"   {name}: {style}")
                return False
        
        print("✅ 所有模块的QTableWidget::item样式一致")
        
        # 检查是否都没有强制背景色
        for name, style in item_styles:
            if "background-color:" in style:
                print(f"❌ {name}模块仍有强制背景色设置")
                return False
            else:
                print(f"✅ {name}模块无强制背景色设置")
        
        return True
        
    except Exception as e:
        print(f"❌ 样式一致性测试失败: {str(e)}")
        return False

def test_color_validation_logic():
    """测试颜色验证逻辑"""
    print("\n=== 测试颜色验证逻辑 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查颜色验证逻辑
        validation_features = [
            "actual_bg\\.color\\(\\)\\.name\\(\\)",  # 背景色名称获取
            "actual_fg\\.color\\(\\)\\.name\\(\\)",  # 前景色名称获取
            "highlight_color\\.name\\(\\)",         # 设置颜色名称获取
            "text_color\\.name\\(\\)",              # 文字颜色名称获取
        ]
        
        import re
        for feature in validation_features:
            if re.search(feature, content):
                print(f"✅ 颜色验证逻辑存在: {feature}")
            else:
                print(f"❌ 颜色验证逻辑缺失: {feature}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 颜色验证逻辑测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 搜索高亮根本原因修复测试")
    print("=" * 60)
    
    # 测试CSS背景色设置已移除
    if not test_css_background_color_removed():
        print("❌ CSS背景色设置移除测试失败")
        return False
    
    # 测试增强的高亮实现
    if not test_enhanced_highlight_implementation():
        print("❌ 增强高亮实现测试失败")
        return False
    
    # 测试调试日志已添加
    if not test_debug_logging_added():
        print("❌ 调试日志测试失败")
        return False
    
    # 测试表格更新调用
    if not test_table_update_calls():
        print("❌ 表格更新调用测试失败")
        return False
    
    # 测试样式一致性
    if not test_style_consistency():
        print("❌ 样式一致性测试失败")
        return False
    
    # 测试颜色验证逻辑
    if not test_color_validation_logic():
        print("❌ 颜色验证逻辑测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！")
    print("\n📋 根本原因修复总结:")
    print("✅ 1. CSS样式表冲突修复:")
    print("  - 移除了QTableWidget::item中的强制background-color设置")
    print("  - 允许动态设置的高亮颜色正常显示")
    print("  - 添加注释说明不设置背景色的原因")
    
    print("\n✅ 2. 高亮设置增强:")
    print("  - 使用变量存储颜色值，提高代码可读性")
    print("  - 添加表格强制刷新调用(table.update())")
    print("  - 增加颜色设置验证和调试日志")
    
    print("\n✅ 3. 调试功能完善:")
    print("  - 添加详细的高亮设置日志")
    print("  - 验证实际设置的颜色值")
    print("  - 便于排查高亮显示问题")
    
    print("\n✅ 4. 样式一致性保证:")
    print("  - 所有模块的QTableWidget::item样式完全一致")
    print("  - 统一移除强制背景色设置")
    print("  - 确保动态高亮在所有模块中正常工作")
    
    print("\n🎯 解决的根本问题:")
    print("  - CSS样式表优先级高于代码设置 → 移除CSS中的强制背景色")
    print("  - 高亮设置后不可见 → 添加表格刷新和验证机制")
    print("  - 调试困难 → 添加详细的颜色设置验证日志")
    print("  - 模块间不一致 → 统一所有模块的样式表设置")
    
    print("\n🚀 预期效果:")
    print("  - 搜索后匹配项有明显的黄色高亮显示")
    print("  - 高亮颜色不会被CSS样式表覆盖")
    print("  - 调试日志显示颜色设置的详细信息")
    print("  - 所有模块的搜索高亮效果完全一致")
    
    print("\n💡 技术要点:")
    print("  - CSS样式表的优先级通常高于代码动态设置")
    print("  - 移除CSS中的强制样式是解决冲突的关键")
    print("  - table.update()强制刷新确保视觉更新")
    print("  - 颜色验证帮助确认设置是否生效")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
