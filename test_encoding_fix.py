"""
测试编码修复
"""

import os
import sys
import json
import subprocess

# 添加src路径
sys.path.append('src')


def test_encoding_fix():
    """测试编码修复"""
    print("=" * 60)
    print("🔧 测试编码修复")
    print("=" * 60)
    
    print("🔍 问题分析:")
    print("  错误信息:")
    print("    ❌ 'gbk' codec can't encode character '\\u274c'")
    print("    ❌ illegal multibyte sequence")
    print("    ❌ 位置: subprocess启动时")
    print("")
    print("  根本原因:")
    print("    1. Windows默认使用GBK编码")
    print("    2. emoji字符（❌ = \\u274c）无法用GBK编码")
    print("    3. subprocess传递的数据包含emoji")
    print("    4. 子进程输出包含emoji但编码设置不当")
    print("")
    
    print("🔧 修复内容:")
    print("  1. ✅ subprocess编码安全:")
    print("     - 添加encoding='utf-8'和errors='replace'")
    print("     - 提供fallback到系统编码")
    print("     - 使用locale.getpreferredencoding()")
    print("")
    print("  2. ✅ 移除子进程emoji:")
    print("     - 🚀 → [START]")
    print("     - ✅ → [OK]")
    print("     - ❌ → [ERROR]")
    print("     - 🔄 → [PROCESS]")
    print("")
    print("  3. ✅ JSON数据安全:")
    print("     - 清理video_list中的特殊字符")
    print("     - 使用ensure_ascii=True")
    print("     - 转义无法编码的字符")
    print("")
    print("  4. ✅ 子进程输出编码:")
    print("     - Windows上设置UTF-8输出")
    print("     - 使用io.TextIOWrapper包装")
    print("     - errors='replace'处理编码错误")
    print("")
    
    print("📋 修复后的架构:")
    print("  主进程:")
    print("    1. 清理video_list数据（移除特殊字符）")
    print("    2. 生成ASCII安全的JSON")
    print("    3. 使用UTF-8编码启动subprocess")
    print("    4. 设置errors='replace'处理编码错误")
    print("")
    print("  子进程:")
    print("    1. 设置UTF-8输出编码")
    print("    2. 使用ASCII安全的日志前缀")
    print("    3. 输出JSON结果（ASCII编码）")
    print("    4. 安全处理所有字符串")
    print("")
    
    print("🎯 预期效果:")
    print("  ✅ 不再出现GBK编码错误")
    print("  ✅ subprocess能正常启动")
    print("  ✅ 子进程能正常输出")
    print("  ✅ JSON数据能正确传递")
    print("  ✅ 重命名结果能正确返回")
    print("")
    
    print("🔍 技术细节:")
    print("  编码安全措施:")
    print("    - subprocess: encoding='utf-8', errors='replace'")
    print("    - JSON: ensure_ascii=True")
    print("    - 输出: io.TextIOWrapper with UTF-8")
    print("    - 字符清理: encode('ascii', 'ignore')")
    print("")
    print("  Fallback机制:")
    print("    - UTF-8失败 → 系统默认编码")
    print("    - 字符清理失败 → repr()转义")
    print("    - 输出失败 → 错误替换")
    print("")
    
    print("🚀 测试建议:")
    print("  1. 重启程序")
    print("  2. 再次进行飞影上传测试")
    print("  3. 观察日志:")
    print("     - 应该看到: '[START] 独立进程开始重命名...'")
    print("     - 不应该看到: GBK编码错误")
    print("  4. 检查进程是否正常完成")
    print("")
    
    print("💡 调试方法:")
    print("  如果还有问题，可以独立测试子进程:")
    print("  test_data = '{\"video_list\":[{\"actor_name\":\"测试\",\"video_id\":\"123\"}],\"headless\":true}'")
    print("  python src/core/rename_process.py test_data")
    print("")
    
    print("=" * 60)
    print("🔧 编码修复完成")
    print("=" * 60)


def test_subprocess_encoding():
    """测试subprocess编码"""
    print("\n" + "=" * 40)
    print("🧪 测试subprocess编码")
    print("=" * 40)
    
    try:
        # 测试数据
        test_data = {
            "video_list": [
                {
                    "actor_name": "测试演员",
                    "video_id": "12345"
                }
            ],
            "headless": True
        }
        
        # 测试JSON编码
        json_str = json.dumps(test_data, ensure_ascii=True)
        print(f"✅ JSON编码测试通过: {len(json_str)} 字符")
        
        # 测试subprocess命令构建
        script_path = os.path.join("src", "core", "rename_process.py")
        cmd = [sys.executable, script_path, json_str]
        print(f"✅ 命令构建测试通过: {len(cmd)} 个参数")
        
        print("✅ 所有编码测试通过")
        
    except Exception as e:
        print(f"❌ 编码测试失败: {str(e)}")


if __name__ == "__main__":
    test_encoding_fix()
    test_subprocess_encoding()
