#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import json

print("SIMPLE_TEST: Process started")
sys.stdout.flush()

# 模拟输入参数解析
if len(sys.argv) >= 2:
    try:
        input_data = json.loads(sys.argv[1])
        print(f"SIMPLE_TEST: Input data received: {input_data}")
        sys.stdout.flush()
    except Exception as e:
        print(f"SIMPLE_TEST: Input parsing failed: {e}")
        sys.stdout.flush()
else:
    print("SIMPLE_TEST: No input data")
    sys.stdout.flush()

print("SIMPLE_TEST: Process ending")
sys.stdout.flush()

# 输出结果
result = {
    "success": True,
    "results": [{"test": "completed"}]
}
print("RESULT_JSON:" + json.dumps(result, ensure_ascii=True))
sys.stdout.flush()