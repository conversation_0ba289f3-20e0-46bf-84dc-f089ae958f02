"""
测试表格显示修复
"""

import os
import pandas as pd


def test_table_display():
    """测试表格显示修复"""
    print("=" * 60)
    print("🧪 测试表格显示修复")
    print("=" * 60)
    
    avatar_list_path = "data/avatar_list.xlsx"
    if not os.path.exists(avatar_list_path):
        print("❌ 数据文件不存在")
        return
    
    # 读取Excel文件
    df = pd.read_excel(avatar_list_path)
    print(f"📊 总数据量: {len(df)} 条")
    
    # 检查列结构
    print(f"\n📋 Excel文件列结构:")
    for i, col in enumerate(df.columns):
        print(f"  {i+1:2d}. {col}")
    
    # 检查"是否重命名"列
    if "是否重命名" in df.columns:
        print(f"\n✅ '是否重命名'列存在")
        
        # 统计"是否重命名"列的值
        rename_counts = df["是否重命名"].value_counts()
        print(f"  是否重命名统计:")
        for status, count in rename_counts.items():
            print(f"    '{status}': {count} 条")
        
        # 显示一些示例数据
        print(f"\n📋 示例数据 (前5条):")
        sample_columns = ["ID", "拍摄演员名称", "是否上传飞影", "是否重命名", "更新日期"]
        available_columns = [col for col in sample_columns if col in df.columns]
        
        for i, (_, row) in enumerate(df.head(5).iterrows()):
            print(f"  {i+1}. ", end="")
            for col in available_columns:
                value = row.get(col, "")
                print(f"{col}: '{value}' | ", end="")
            print()
    else:
        print(f"\n❌ '是否重命名'列不存在，需要添加")
        # 添加列
        df["是否重命名"] = ""
        df.to_excel(avatar_list_path, index=False)
        print(f"✅ 已添加'是否重命名'列")
    
    # 模拟创建一些测试数据
    print(f"\n🔧 创建测试数据...")
    
    # 找到前3条记录，设置不同的重命名状态
    if len(df) >= 3:
        # 设置第1条为空（需要重命名）
        df.iloc[0, df.columns.get_loc("是否重命名")] = ""
        
        # 设置第2条为"重命名失败"
        df.iloc[1, df.columns.get_loc("是否重命名")] = "重命名失败"
        
        # 设置第3条为"是"（已重命名）
        df.iloc[2, df.columns.get_loc("是否重命名")] = "是"
        
        # 保存文件
        df.to_excel(avatar_list_path, index=False)
        print(f"✅ 已创建测试数据")
        
        # 显示测试数据
        print(f"\n📋 测试数据:")
        for i in range(3):
            row = df.iloc[i]
            actor_name = row.get("拍摄演员名称", "")
            video_id = row.get("ID", "")
            upload_status = row.get("是否上传飞影", "")
            rename_status = row.get("是否重命名", "")
            print(f"  {i+1}. {actor_name}-{video_id} | 上传: '{upload_status}' | 重命名: '{rename_status}'")
    
    # 验证表格列结构
    print(f"\n📊 预期的表格列结构:")
    expected_headers = ["序号", "ID", "视频URL", "上传人", "演员", "视频版型", "场景", "表现形式", "服装", "是否上传飞影", "是否重命名", "更新日期", "删除"]
    for i, header in enumerate(expected_headers):
        print(f"  {i:2d}. {header}")
    
    print(f"\n💡 修复内容:")
    print(f"  1. ✅ 在video_material_manager.py中添加了'是否重命名'列")
    print(f"  2. ✅ 修复了删除按钮的列索引 (11 → 12)")
    print(f"  3. ✅ 更新了列名映射")
    
    print(f"\n🚀 下一步:")
    print(f"  1. 重启主程序: python src/main.py")
    print(f"  2. 进入视频管理界面")
    print(f"  3. 检查'是否重命名'列是否正确显示")
    print(f"  4. 检查删除按钮是否在正确位置")
    
    print("\n" + "=" * 60)
    print("🎯 表格显示修复测试完成")
    print("=" * 60)


if __name__ == "__main__":
    test_table_display()
