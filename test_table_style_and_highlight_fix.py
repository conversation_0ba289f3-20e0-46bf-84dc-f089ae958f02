#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试表格样式和搜索高亮修复
验证所有模块表格样式统一，搜索高亮功能正常
"""

import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_alternating_row_colors_disabled():
    """测试交替行颜色已禁用"""
    print("=== 测试交替行颜色已禁用 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查所有模块都禁用了交替行颜色
        modules = [
            ("数字人模块", "dh_table.setAlternatingRowColors\\(False\\)"),
            ("视频管理模块", "vm_table.setAlternatingRowColors\\(False\\)"),
            ("音频管理模块", "voice_table.setAlternatingRowColors\\(False\\)"),
        ]
        
        import re
        for module_name, pattern in modules:
            if re.search(pattern, content):
                print(f"✅ {module_name}交替行颜色已禁用")
            else:
                print(f"❌ {module_name}交替行颜色未禁用")
                return False
        
        # 检查是否有注释说明
        comments = [
            "禁用交替行颜色，统一白色背景",
        ]
        
        for comment in comments:
            if comment in content:
                print(f"✅ 注释说明存在: {comment}")
            else:
                print(f"❌ 注释说明缺失: {comment}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 交替行颜色测试失败: {str(e)}")
        return False

def test_table_style_consistency():
    """测试表格样式一致性"""
    print("\n=== 测试表格样式一致性 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查样式一致性
        style_features = [
            "border: 1px solid #e0e0e0",  # 边框颜色
            "border-radius: 8px",  # 圆角
            "background-color: white",  # 背景色
            "gridline-color: #f0f0f0",  # 网格线颜色
            "background-color: #f8f9fa",  # 表头背景色
            "font-weight: bold",  # 表头字体
        ]
        
        import re
        for feature in style_features:
            # 统计出现次数，应该至少有2次（视频管理和音频管理）
            count = len(re.findall(re.escape(feature), content))
            if count >= 2:
                print(f"✅ 样式一致性检查通过: {feature} (出现{count}次)")
            else:
                print(f"❌ 样式一致性检查失败: {feature} (仅出现{count}次)")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 表格样式一致性测试失败: {str(e)}")
        return False

def test_voice_table_style_complete():
    """测试音频管理模块样式表完整性"""
    print("\n=== 测试音频管理模块样式表完整性 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查音频管理模块样式表是否完整
        voice_style_features = [
            "background-color: white",  # 项目背景色
            "background-color: #f8f9fa",  # 表头背景色
            "padding: 8px",  # 内边距
            "border: none",  # 无边框
        ]
        
        import re
        # 提取音频管理模块的样式表部分
        voice_style_match = re.search(
            r'self\.voice_table\.setStyleSheet\("""(.*?)"""\)',
            content, re.DOTALL
        )
        
        if not voice_style_match:
            print("❌ 音频管理模块样式表未找到")
            return False
        
        voice_style = voice_style_match.group(1)
        
        for feature in voice_style_features:
            if re.search(feature, voice_style):
                print(f"✅ 音频管理样式表功能存在: {feature}")
            else:
                print(f"❌ 音频管理样式表功能缺失: {feature}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 音频管理模块样式表测试失败: {str(e)}")
        return False

def test_search_highlight_enhancement():
    """测试搜索高亮增强功能"""
    print("\n=== 测试搜索高亮增强功能 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查搜索高亮增强功能
        highlight_features = [
            # 视频管理模块
            ("视频管理高亮设置", "item.setBackground\\(QColor\\(\"#ffeb3b\"\\)\\).*vm"),
            ("视频管理前景色设置", "item.setForeground\\(QColor\\(\"#000000\"\\)\\).*vm"),
            ("视频管理清除前景色", "item.setForeground\\(QColor\\(\\)\\).*vm"),
            
            # 音频管理模块
            ("音频管理高亮设置", "item.setBackground\\(QColor\\(\"#ffeb3b\"\\)\\).*voice"),
            ("音频管理前景色设置", "item.setForeground\\(QColor\\(\"#000000\"\\)\\).*voice"),
            ("音频管理清除前景色", "item.setForeground\\(QColor\\(\\)\\).*voice"),
        ]
        
        import re
        for feature_name, pattern in highlight_features:
            if re.search(pattern, content, re.DOTALL):
                print(f"✅ {feature_name}存在")
            else:
                print(f"❌ {feature_name}缺失")
                return False
        
        # 检查高亮注释
        highlight_comments = [
            "使用更强的高亮设置",
            "黑色文字确保可读性",
        ]
        
        for comment in highlight_comments:
            if comment in content:
                print(f"✅ 高亮注释存在: {comment}")
            else:
                print(f"❌ 高亮注释缺失: {comment}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 搜索高亮增强测试失败: {str(e)}")
        return False

def test_grid_lines_enabled():
    """测试网格线已启用"""
    print("\n=== 测试网格线已启用 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查所有模块都启用了网格线
        grid_features = [
            ("数字人模块", "dh_table.setShowGrid\\(True\\)"),
            ("视频管理模块", "vm_table.setShowGrid\\(True\\)"),
            ("音频管理模块", "voice_table.setShowGrid\\(True\\)"),
        ]
        
        import re
        for module_name, pattern in grid_features:
            if re.search(pattern, content):
                print(f"✅ {module_name}网格线已启用")
            else:
                print(f"❌ {module_name}网格线未启用")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 网格线测试失败: {str(e)}")
        return False

def test_style_comments():
    """测试样式注释"""
    print("\n=== 测试样式注释 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查样式注释
        style_comments = [
            "与数字人和视频管理模块保持一致",
            "与数字人模块保持一致",
            "显示网格线",
        ]
        
        for comment in style_comments:
            if comment in content:
                print(f"✅ 样式注释存在: {comment}")
            else:
                print(f"❌ 样式注释缺失: {comment}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 样式注释测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 表格样式和搜索高亮修复测试")
    print("=" * 60)
    
    # 测试交替行颜色已禁用
    if not test_alternating_row_colors_disabled():
        print("❌ 交替行颜色禁用测试失败")
        return False
    
    # 测试表格样式一致性
    if not test_table_style_consistency():
        print("❌ 表格样式一致性测试失败")
        return False
    
    # 测试音频管理模块样式表完整性
    if not test_voice_table_style_complete():
        print("❌ 音频管理模块样式表测试失败")
        return False
    
    # 测试搜索高亮增强功能
    if not test_search_highlight_enhancement():
        print("❌ 搜索高亮增强测试失败")
        return False
    
    # 测试网格线已启用
    if not test_grid_lines_enabled():
        print("❌ 网格线测试失败")
        return False
    
    # 测试样式注释
    if not test_style_comments():
        print("❌ 样式注释测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！")
    print("\n📋 修复总结:")
    print("✅ 1. 统一表格背景色:")
    print("  - 所有模块禁用交替行颜色(setAlternatingRowColors(False))")
    print("  - 统一使用白色背景，提供更好的搜索高亮对比度")
    print("  - 添加网格线显示，保持表格结构清晰")
    
    print("\n✅ 2. 完善音频管理模块样式:")
    print("  - 补充完整的样式表定义")
    print("  - 与数字人和视频管理模块样式完全一致")
    print("  - 包含表头、项目、边框等完整样式")
    
    print("\n✅ 3. 增强搜索高亮效果:")
    print("  - 同时设置背景色和前景色确保高亮可见")
    print("  - 使用黄色背景(#ffeb3b)和黑色文字(#000000)")
    print("  - 清除高亮时同时清除背景色和前景色")
    
    print("\n✅ 4. 保持样式一致性:")
    print("  - 所有模块使用相同的边框、圆角、颜色")
    print("  - 统一的表头样式和网格线设置")
    print("  - 一致的内边距和字体设置")
    
    print("\n🎯 解决的问题:")
    print("  - 音频管理模块交替行颜色影响搜索高亮 → 禁用交替行颜色")
    print("  - 音频管理模块样式表不完整 → 补充完整样式定义")
    print("  - 搜索高亮在某些背景下不明显 → 增强高亮设置")
    print("  - 模块间样式不一致 → 统一所有模块样式")
    
    print("\n🚀 预期效果:")
    print("  - 所有表格都是统一的白色背景")
    print("  - 搜索高亮在所有模块中都清晰可见")
    print("  - 表格样式在所有模块中完全一致")
    print("  - 网格线帮助用户更好地阅读表格内容")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
