"""
测试VBS路径修复
"""

import os
import sys

# 添加src路径
sys.path.append('src')


def test_path_logic():
    """测试路径逻辑"""
    print("=" * 60)
    print("🔧 测试VBS路径修复")
    print("=" * 60)
    
    print("🔍 问题分析:")
    print("  错误路径: d:\\project\\guangliu02\\src\\run_video_management_scheduled.vbs")
    print("  正确路径: d:\\project\\guangliu02\\run_video_management_scheduled.vbs")
    print("  原因: 程序运行在src目录，os.getcwd()返回src路径")
    print("")
    
    print("📁 当前路径信息:")
    current_dir = os.getcwd()
    print(f"  当前工作目录: {current_dir}")
    print(f"  是否在src目录: {current_dir.endswith('src')}")
    
    # 测试智能路径处理
    if current_dir.endswith('src'):
        project_root = os.path.dirname(current_dir)
        print(f"  项目根目录: {project_root}")
    else:
        project_root = current_dir
        print(f"  项目根目录: {project_root}")
    
    print("")
    
    print("📝 路径构建测试:")
    script_path = os.path.join(project_root, "run_video_management_auto.bat")
    vbs_path = os.path.join(project_root, "run_video_management_scheduled.vbs")
    
    print(f"  BAT文件路径: {script_path}")
    print(f"  VBS文件路径: {vbs_path}")
    print("")
    
    print("✅ 文件存在性检查:")
    print(f"  BAT文件存在: {os.path.exists(script_path)}")
    print(f"  VBS文件存在: {os.path.exists(vbs_path)}")
    print("")
    
    return project_root, script_path, vbs_path


def test_vbs_content(vbs_path):
    """测试VBS文件内容"""
    print("=" * 40)
    print("📄 VBS文件内容检查")
    print("=" * 40)
    
    if os.path.exists(vbs_path):
        print(f"✅ VBS文件存在: {vbs_path}")
        try:
            with open(vbs_path, 'r', encoding='utf-8') as f:
                content = f.read()
            print("文件内容:")
            print("  " + content.replace('\n', '\n  '))
            
            # 检查路径是否正确
            if 'src\\run_video_management_auto.bat' in content:
                print("❌ 发现错误路径: 包含src目录")
            elif 'run_video_management_auto.bat' in content:
                print("✅ 路径正确: 不包含src目录")
            else:
                print("⚠️ 未找到BAT文件路径")
                
        except Exception as e:
            print(f"❌ 读取VBS文件失败: {e}")
    else:
        print(f"❌ VBS文件不存在: {vbs_path}")


def create_correct_vbs(project_root):
    """创建正确的VBS文件"""
    print("\n" + "=" * 40)
    print("🔧 创建正确的VBS文件")
    print("=" * 40)
    
    script_path = os.path.join(project_root, "run_video_management_auto.bat")
    vbs_path = os.path.join(project_root, "run_video_management_scheduled.vbs")
    
    # 确保使用绝对路径
    script_path = os.path.abspath(script_path)
    script_path = script_path.replace('/', '\\')
    script_dir = os.path.dirname(script_path)
    
    # 创建正确的VBS内容
    vbs_content = f'''Set WshShell = CreateObject("WScript.Shell")
WshShell.CurrentDirectory = "{script_dir}"
WshShell.Run """{script_path} scheduled""", 0, False
'''
    
    print(f"目标VBS文件: {vbs_path}")
    print(f"BAT文件路径: {script_path}")
    print(f"工作目录: {script_dir}")
    print("")
    print("VBS文件内容:")
    print("  " + vbs_content.replace('\n', '\n  '))
    
    try:
        with open(vbs_path, 'w', encoding='utf-8') as f:
            f.write(vbs_content)
        print("✅ VBS文件已更新")
        return True
    except Exception as e:
        print(f"❌ 更新VBS文件失败: {e}")
        return False


def test_manual_vbs_execution(vbs_path):
    """测试手动执行VBS文件"""
    print("\n" + "=" * 40)
    print("🧪 测试手动执行VBS文件")
    print("=" * 40)
    
    if not os.path.exists(vbs_path):
        print(f"❌ VBS文件不存在: {vbs_path}")
        return False
    
    print(f"准备执行: wscript.exe \"{vbs_path}\"")
    print("注意: VBS会隐藏执行，不会显示窗口")
    print("")
    
    import subprocess
    try:
        result = subprocess.run([
            "wscript.exe", vbs_path
        ], capture_output=True, text=True, timeout=30)
        
        print(f"执行结果: 返回码 {result.returncode}")
        if result.stdout:
            print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
        
        if result.returncode == 0:
            print("✅ VBS文件执行成功")
            
            # 检查是否生成了日志文件
            print("\n📋 检查日志文件:")
            log_dir = "feiyingshuziren/log"
            if os.path.exists(log_dir):
                import datetime
                today = datetime.datetime.now().strftime("%Y-%m-%d")
                today_log_dir = os.path.join(log_dir, today)
                
                if os.path.exists(today_log_dir):
                    log_files = os.listdir(today_log_dir)
                    recent_logs = [f for f in log_files if 'controller' in f]
                    
                    if recent_logs:
                        print(f"  ✅ 找到日志文件: {recent_logs}")
                        
                        # 显示最新日志的时间
                        latest_log = max(recent_logs)
                        log_path = os.path.join(today_log_dir, latest_log)
                        mod_time = os.path.getmtime(log_path)
                        import datetime
                        mod_datetime = datetime.datetime.fromtimestamp(mod_time)
                        print(f"  最新日志时间: {mod_datetime.strftime('%H:%M:%S')}")
                    else:
                        print("  ⚠️ 没有找到controller日志文件")
                else:
                    print(f"  ⚠️ 今天的日志目录不存在: {today_log_dir}")
            else:
                print(f"  ⚠️ 日志根目录不存在: {log_dir}")
            
            return True
        else:
            print("❌ VBS文件执行失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ VBS文件执行超时（30秒）")
        return False
    except Exception as e:
        print(f"❌ 执行VBS文件时出错: {e}")
        return False


def show_next_steps():
    """显示下一步操作"""
    print("\n" + "=" * 40)
    print("📋 下一步操作")
    print("=" * 40)
    
    print("修复完成后的操作:")
    print("  1. ✅ 路径逻辑已修复")
    print("  2. ✅ VBS文件已更新")
    print("  3. ✅ 手动测试已完成")
    print("")
    
    print("现在可以:")
    print("  1. 在程序中创建新的系统任务")
    print("  2. 验证任务路径是否正确")
    print("  3. 测试任务执行是否正常")
    print("  4. 测试任务删除是否正常")
    print("")
    
    print("验证要点:")
    print("  ✅ 任务路径不包含src目录")
    print("  ✅ VBS文件使用绝对路径")
    print("  ✅ 任务能够正常执行")
    print("  ✅ 生成正确的日志文件")
    print("")
    
    print("如果仍有问题:")
    print("  1. 检查程序是否以管理员权限运行")
    print("  2. 确认防病毒软件没有阻止")
    print("  3. 手动执行VBS文件进行测试")
    print("  4. 查看Windows事件查看器")


if __name__ == "__main__":
    print("🔧 VBS路径修复测试工具")
    print("=" * 60)
    
    # 测试路径逻辑
    project_root, script_path, vbs_path = test_path_logic()
    
    # 检查VBS文件内容
    test_vbs_content(vbs_path)
    
    # 创建正确的VBS文件
    if create_correct_vbs(project_root):
        # 测试手动执行
        print("\n" + "=" * 60)
        test_choice = input("是否测试手动执行VBS文件? (y/n): ").lower().strip()
        if test_choice == 'y':
            test_manual_vbs_execution(vbs_path)
    
    # 显示下一步操作
    show_next_steps()
    
    print("\n" + "=" * 60)
    print("🎉 VBS路径问题修复完成！")
    print("现在可以在程序中创建新的系统任务进行测试")
    print("=" * 60)
