"""
强力的asyncio错误抑制器
"""

import sys
import os
import warnings
import threading
from contextlib import contextmanager


class StrongAsyncioErrorSuppressor:
    """强力的asyncio错误抑制器"""
    
    def __init__(self):
        self.original_stderr = sys.stderr
        self.original_stdout = sys.stdout
        self.lock = threading.Lock()
        self.suppressed_count = 0
        
    def write(self, text):
        with self.lock:
            # 检查是否是asyncio相关的错误
            if self._should_suppress(text):
                self.suppressed_count += 1
                return
            
            # 正常输出
            self.original_stderr.write(text)
    
    def _should_suppress(self, text):
        """判断是否应该抑制这个错误"""
        # 检查是否包含asyncio错误的特征
        asyncio_patterns = [
            "_ProactorBasePipeTransport.__del__",
            "BaseSubprocessTransport.__del__",
            "StreamWriter.__del__",
            "Event loop is closed",
            "I/O operation on closed pipe",
            "unclosed transport",
            "Exception ignored in:",
            "ResourceWarning"
        ]
        
        return any(pattern in text for pattern in asyncio_patterns)
    
    def flush(self):
        self.original_stderr.flush()
    
    def get_suppressed_count(self):
        return self.suppressed_count


def apply_strong_suppression():
    """应用强力的asyncio错误抑制"""
    if sys.platform != "win32":
        return None
    
    if os.getenv("DISABLE_ASYNCIO_FILTER") == "1":
        print("⚠️ asyncio错误抑制已禁用（调试模式）")
        return None
    
    try:
        # 方法1: 抑制ResourceWarning
        warnings.filterwarnings("ignore", category=ResourceWarning)
        
        # 方法2: 抑制asyncio相关的警告
        warnings.filterwarnings("ignore", message=".*unclosed.*")
        warnings.filterwarnings("ignore", message=".*Event loop is closed.*")
        
        # 方法3: 替换stderr
        suppressor = StrongAsyncioErrorSuppressor()
        sys.stderr = suppressor
        
        # 方法4: 设置环境变量
        os.environ['PYTHONASYNCIODEBUG'] = '0'
        os.environ['PYTHONWARNINGS'] = 'ignore::ResourceWarning'
        
        print("✅ 已启用强力asyncio错误抑制")
        print("💡 如需调试，设置环境变量 DISABLE_ASYNCIO_FILTER=1")
        
        return suppressor
        
    except Exception as e:
        print(f"⚠️ 启用错误抑制失败: {e}")
        return None


@contextmanager
def suppress_asyncio_errors():
    """上下文管理器：临时抑制asyncio错误"""
    if sys.platform != "win32":
        yield
        return
    
    original_stderr = sys.stderr
    original_stdout = sys.stdout
    
    try:
        # 临时替换输出流
        suppressor = StrongAsyncioErrorSuppressor()
        sys.stderr = suppressor
        sys.stdout = suppressor
        
        # 临时抑制警告
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", category=ResourceWarning)
            warnings.filterwarnings("ignore", message=".*unclosed.*")
            warnings.filterwarnings("ignore", message=".*Event loop is closed.*")
            
            yield suppressor
            
    finally:
        # 恢复原始输出流
        sys.stderr = original_stderr
        sys.stdout = original_stdout


def monkey_patch_asyncio():
    """猴子补丁：修改asyncio的错误处理"""
    try:
        import asyncio
        
        # 尝试修改asyncio的错误处理
        if hasattr(asyncio, 'get_event_loop'):
            original_get_event_loop = asyncio.get_event_loop
            
            def patched_get_event_loop():
                try:
                    return original_get_event_loop()
                except RuntimeError:
                    # 如果事件循环已关闭，创建新的
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    return loop
            
            asyncio.get_event_loop = patched_get_event_loop
        
        print("✅ 已应用asyncio猴子补丁")
        
    except Exception as e:
        print(f"⚠️ 应用asyncio补丁失败: {e}")


def setup_comprehensive_suppression():
    """设置全面的错误抑制"""
    if sys.platform != "win32":
        return
    
    if os.getenv("DISABLE_ASYNCIO_FILTER") == "1":
        print("⚠️ asyncio错误抑制已禁用（调试模式）")
        return
    
    print("🔧 正在设置全面的asyncio错误抑制...")
    
    # 应用所有抑制方法
    suppressor = apply_strong_suppression()
    monkey_patch_asyncio()
    
    if suppressor:
        print("✅ 全面的asyncio错误抑制已启用")
        return suppressor
    else:
        print("⚠️ 部分抑制方法可能失败")
        return None
