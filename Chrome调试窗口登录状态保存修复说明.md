# Chrome调试窗口登录状态保存修复说明

## 问题描述

用户反馈程序自动打开的Chrome窗口不是调试窗口，无法保存登录状态，每次使用都需要重新登录。

## 问题分析

### 原始问题
1. **临时目录问题**：程序使用临时目录作为Chrome用户数据目录
   ```python
   # 原始代码
   import tempfile
   self.chrome_debug_dir = os.path.join(tempfile.gettempdir(), "chrome-debug-profile")
   ```

2. **登录状态丢失**：每次启动都使用全新的临时目录，无法保存：
   - 登录Cookie
   - 浏览器缓存
   - 用户偏好设置
   - 自动填充数据

3. **用户体验差**：每次素材更新都需要重新登录

## 修复方案

### 1. 使用固定用户数据目录

**修改位置**：`src/core/video_material_manager.py`

```python
# 修复后的代码
# 使用用户文档目录下的固定文件夹，保存登录状态
user_documents = os.path.expanduser("~/Documents")
self.chrome_debug_dir = os.path.join(user_documents, "GuangliuAssistant", "chrome-debug-profile")

# 确保目录存在
os.makedirs(self.chrome_debug_dir, exist_ok=True)

# 调试信息
self.log_message.emit(f"🔧 Chrome调试目录: {self.chrome_debug_dir}")
self.log_message.emit(f"💾 使用持久化用户数据，将保存登录状态")
```

**目录位置**：`~/Documents/GuangliuAssistant/chrome-debug-profile`

### 2. 添加清理调试数据功能

**新增方法**：`clear_chrome_debug_data()`

```python
def clear_chrome_debug_data(self) -> bool:
    """清理Chrome调试数据目录（用于重置登录状态）"""
    try:
        import shutil
        if os.path.exists(self.chrome_debug_dir):
            # 先尝试关闭可能正在运行的Chrome进程
            self.kill_debug_chrome_processes()
            
            # 删除调试目录
            shutil.rmtree(self.chrome_debug_dir)
            self.log_message.emit(f"🧹 已清理Chrome调试数据目录: {self.chrome_debug_dir}")
            
            # 重新创建目录
            os.makedirs(self.chrome_debug_dir, exist_ok=True)
            self.log_message.emit(f"📁 已重新创建调试目录，登录状态已重置")
            return True
        else:
            self.log_message.emit(f"📁 调试目录不存在，无需清理")
            return True
    except Exception as e:
        self.log_message.emit(f"❌ 清理调试数据失败: {str(e)}")
        return False
```

### 3. 添加UI控制按钮

**新增按钮**：`重置登录`

```python
# 创建清理调试数据按钮
self.btn_clear_debug = QPushButton("重置登录")
self.btn_clear_debug.setObjectName("warningButton")
self.btn_clear_debug.setToolTip("清理Chrome调试数据，重置登录状态（登录失效时使用）")
self.btn_clear_debug.setFixedWidth(120)
self.btn_clear_debug.setIcon(self.get_icon("delete.svg"))
self.btn_clear_debug.setIconSize(QSize(20, 20))
self.btn_clear_debug.clicked.connect(self.on_clear_debug_clicked)
```

**按钮处理方法**：
- 确认对话框提醒用户操作后果
- 执行清理操作
- 显示操作结果

### 4. 添加警告按钮样式

**新增样式**：`warningButton`

```css
/* 警告按钮 */
QPushButton#warningButton {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    border-radius: 6px;
    padding: 8px 12px;
    min-height: 20px;
    font-size: 14px;
    font-weight: 500;
    outline: none;
}

QPushButton#warningButton:hover {
    background-color: #ffeaa7;
    border-color: #ffd93d;
}
```

## 修复效果

### ✅ 登录状态持久化
- Chrome调试窗口现在使用固定的用户数据目录
- 登录状态、Cookie、缓存等数据会被保存
- 下次启动时自动恢复登录状态

### ✅ 用户体验改善
- 首次登录后，后续使用无需重复登录
- 浏览器设置和偏好会被保留
- 自动填充功能正常工作

### ✅ 问题排查功能
- 新增"重置登录"按钮
- 当登录状态出现问题时，可以一键清理重置
- 清理后下次使用需要重新登录

### ✅ 安全性考虑
- 数据存储在用户文档目录，权限安全
- 提供清理功能，可以随时重置
- 确认对话框防止误操作

## 使用说明

### 正常使用
1. 首次使用时需要在自动打开的Chrome窗口中登录
2. 登录完成后，登录状态会自动保存
3. 后续使用时会自动恢复登录状态

### 问题排查
1. 如果登录状态失效或出现问题
2. 点击"重置登录"按钮
3. 确认清理操作
4. 下次使用时重新登录

### 数据位置
- Chrome调试数据目录：`~/Documents/GuangliuAssistant/chrome-debug-profile`
- 可以手动删除此目录来重置登录状态

## 技术细节

### Chrome启动参数
```python
startup_args = [
    chrome_path,
    f"--remote-debugging-port={self.debug_port}",
    f"--remote-debugging-address=0.0.0.0",
    f"--user-data-dir={self.chrome_debug_dir}",  # 固定用户数据目录
    "--no-first-run", 
    "--no-default-browser-check",
    # ... 其他参数
]
```

### 目录结构
```
~/Documents/GuangliuAssistant/
└── chrome-debug-profile/
    ├── Default/
    │   ├── Cookies
    │   ├── Local Storage/
    │   ├── Session Storage/
    │   └── ...
    └── ...
```

现在Chrome调试窗口具备了完整的登录状态保存功能，用户体验得到显著改善！
