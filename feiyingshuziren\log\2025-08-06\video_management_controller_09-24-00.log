[2025-08-06 09:24:00] 日志文件: d:\project\guangliu02\feiyingshuziren\log\2025-08-06\video_management_controller_09-24-00.log
[2025-08-06 09:24:00] 视频管理自动化控制器启动
[2025-08-06 09:24:00] 任务类型: full_process
[2025-08-06 09:24:00] 无头模式: False
[2025-08-06 09:24:00] 开始时间: 2025-08-06 09:24:00
[2025-08-06 09:24:00] 控制器初始化完成
[2025-08-06 09:24:00] 任务类型: full_process
[2025-08-06 09:24:00] 无头模式: False
[2025-08-06 09:24:00] 工作目录: d:\project\guangliu02
[2025-08-06 09:24:00] 源码目录: d:\project\guangliu02\src
[2025-08-06 09:24:00] [CONTROLLER] 将执行 3 个任务: material, upload, rename
[2025-08-06 09:24:00] 
[CONTROLLER] 第1步：素材更新
[2025-08-06 09:24:00] ============================================================
[2025-08-06 09:24:00] [MATERIAL] 开始执行素材更新任务
[2025-08-06 09:24:00] ============================================================
[2025-08-06 09:24:00] [MATERIAL] 开始下载素材数据...
[2025-08-06 09:24:00] [MATERIAL] 🚀 开始下载素材数据...
[2025-08-06 09:24:00] [MATERIAL] 🔍 检查Chrome调试端口 9222 是否可用...
[2025-08-06 09:24:02] [MATERIAL] 🔧 调试端口浏览器未运行，正在自动启动...
[2025-08-06 09:24:02] [MATERIAL] 💡 程序将自动打开Chrome浏览器窗口
[2025-08-06 09:24:04] [MATERIAL] 🔍 发现 16 个Chrome进程
[2025-08-06 09:24:04] [MATERIAL] 🔍 发现普通Chrome进程（使用默认用户数据目录）: PID 2184
[2025-08-06 09:24:04] [MATERIAL] 🔍 检测到现有Chrome进程
[2025-08-06 09:24:04] [MATERIAL] 🔄 为确保程序稳定运行，将强制关闭所有Chrome进程
[2025-08-06 09:24:04] [MATERIAL] 💡 程序将使用独立的Chrome实例，不会影响后续使用
[2025-08-06 09:24:04] [MATERIAL] 🔄 找到 16 个Chrome进程，开始关闭...
[2025-08-06 09:24:06] [MATERIAL] ✅ 已处理 10 个Chrome进程
[2025-08-06 09:24:09] [MATERIAL] ✅ 已关闭所有Chrome进程
[2025-08-06 09:24:09] [MATERIAL] 🔄 使用默认Chrome配置启动调试实例
[2025-08-06 09:24:09] [MATERIAL] 🔄 确保调试端口可用...
[2025-08-06 09:24:09] [MATERIAL] ✅ Chrome调试目录已创建: C:\Users\<USER>\Program Files\Google\Chrome\Application\chrome.exe --remote-debugging-port=9222 --remote-debugging-address=0.0.0.0 --user-data-dir=C:\Users\<USER>\project\guangliu02\data\temp\material_data_20250806_092440.xlsx
[2025-08-06 09:24:41] [MATERIAL] ✅ 文件下载完成: d:\project\guangliu02\data\temp\material_data_20250806_092440.xlsx
[2025-08-06 09:24:42] [MATERIAL] 📁 检测到下载文件，大小: 724418 字节
[2025-08-06 09:24:44] [MATERIAL] 🧹 正在清理Chrome调试进程...
[2025-08-06 09:24:44] [MATERIAL] 🔄 已关闭记录的调试Chrome进程: PID 4412
[2025-08-06 09:24:45] [MATERIAL] 🔄 已强制关闭 1 个调试Chrome进程
[2025-08-06 09:24:47] [MATERIAL] ✅ Chrome调试进程清理完成
[2025-08-06 09:24:47] [MATERIAL] ✅ 素材数据下载成功，文件大小: 724418 字节
[2025-08-06 09:24:47] [MATERIAL] 🔄 开始处理下载的数据...
[2025-08-06 09:24:49] [MATERIAL] 📊 下载文件包含 6153 条记录
[2025-08-06 09:24:49] [MATERIAL] 🔄 去重处理: 6153 -> 3869 条记录
[2025-08-06 09:24:49] [MATERIAL] 🔄 开始与现有数据合并...
[2025-08-06 09:24:50] [MATERIAL] 📊 现有数据包含 3880 条记录
[2025-08-06 09:24:50] [MATERIAL] ℹ️ 没有新增记录
[2025-08-06 09:24:50] [MATERIAL] 🔄 按ID去重（保留最新记录）: 3880 -> 3880 条记录
[2025-08-06 09:24:52] [MATERIAL] 💾 数据已保存，总计 3880 条记录
[2025-08-06 09:24:52] [MATERIAL] 🗑️ 已清理临时文件: material_data_20250806_092440.xlsx
[2025-08-06 09:24:52] [MATERIAL] ✅ 已清理 1 个临时文件
[2025-08-06 09:24:52] [MATERIAL] 🧹 素材更新完成，开始清理Chrome调试进程...
[2025-08-06 09:24:52] [MATERIAL] 🧹 正在清理Chrome调试进程...
[2025-08-06 09:24:53] [MATERIAL] 🔍 未找到需要关闭的调试Chrome进程
[2025-08-06 09:24:55] [MATERIAL] ✅ Chrome调试进程清理完成
[2025-08-06 09:24:56] [MATERIAL] 🔍 未找到需要关闭的调试Chrome进程
[2025-08-06 09:24:56] [MATERIAL] 🧹 调试浏览器进程清理完成
[2025-08-06 09:24:56] [MATERIAL] 素材更新任务完成
[2025-08-06 09:24:56] 
[CONTROLLER] 第2步：飞影上传
[2025-08-06 09:24:56] ============================================================
[2025-08-06 09:24:56] [UPLOAD] 开始执行飞影上传任务
[2025-08-06 09:24:56] ============================================================
[2025-08-06 09:24:56] [UPLOAD] 获取待上传的视频列表...
[2025-08-06 09:24:59] [UPLOAD] 获取到数据: <class 'pandas.core.frame.DataFrame'>, 行数: 34
[2025-08-06 09:24:59] [UPLOAD] 没有需要上传的视频
[2025-08-06 09:24:59] 
[CONTROLLER] 第3步：自动重命名
[2025-08-06 09:24:59] ============================================================
[2025-08-06 09:24:59] [RENAME] 开始执行自动重命名任务
[2025-08-06 09:24:59] ============================================================
[2025-08-06 09:24:59] [RENAME] 获取需要重命名的视频列表...
[2025-08-06 09:25:01] [RENAME] 获取到数据: <class 'pandas.core.frame.DataFrame'>, 行数: 34
[2025-08-06 09:25:01] [RENAME] 没有需要重命名的视频
[2025-08-06 09:25:01] 
============================================================
[2025-08-06 09:25:01] [CONTROLLER] 任务执行总结
[2025-08-06 09:25:01] ============================================================
[2025-08-06 09:25:01] 任务类型: full_process
[2025-08-06 09:25:01] 总任务数: 3
[2025-08-06 09:25:01] 完成任务: 3
[2025-08-06 09:25:01] 失败任务: 0
[2025-08-06 09:25:01] 成功率: 100.0%
[2025-08-06 09:25:01] [CONTROLLER] 所有任务执行成功！
[2025-08-06 09:25:01] 开始时间: 2025-08-06 09:24:00
[2025-08-06 09:25:01] 结束时间: 2025-08-06 09:25:01
[2025-08-06 09:25:01] 总耗时: 61.7秒
[2025-08-06 09:25:01] [CONTROLLER] 视频管理自动化任务结束
