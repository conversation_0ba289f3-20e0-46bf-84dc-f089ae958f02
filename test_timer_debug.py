"""
调试程序内定时任务问题
"""

import os
import sys

# 添加src路径
sys.path.append('src')


def test_timer_debug():
    """调试程序内定时任务问题"""
    print("=" * 60)
    print("🔍 调试程序内定时任务问题")
    print("=" * 60)
    
    print("🔧 已修复的问题:")
    print("  1. 系统任务创建失败")
    print("     问题: create_vbs_file() 缺少参数")
    print("     修复: 添加 python_exe 参数")
    print("     修复前: self.create_vbs_file(vbs_path, script_path)")
    print("     修复后: self.create_vbs_file(vbs_path, python_exe, script_path)")
    print("")
    
    print("  2. 程序内定时任务不继承父类功能")
    print("     问题: VideoManagementScheduleManager 没有调用父类初始化")
    print("     修复: 改为调用 super().__init__()")
    print("     修复前: QObject.__init__(self)")
    print("     修复后: super().__init__()")
    print("")
    
    print("📍 日志位置说明:")
    print("  程序内定时任务日志:")
    print("    位置: 程序主界面的数字人日志区域")
    print("    格式: [视频管理] 日志内容")
    print("    示例:")
    print("      [视频管理] 程序内定时任务已启动：测试任务，间隔 2 分钟，无限制运行")
    print("      [视频管理] 定时任务触发: 测试任务")
    print("      [视频管理] 执行完整流程：素材更新 → 飞影上传 → 自动重命名")
    print("      [视频管理] 视频管理脚本已启动，进程ID: 12345")
    print("")
    
    print("  脚本执行日志:")
    print("    位置: feiyingshuziren/log/YYYY-MM-DD/video_management_auto_HH-MM-SS.log")
    print("    示例路径: feiyingshuziren/log/2025-01-08/video_management_auto_17-45-30.log")
    print("    内容: 详细的任务执行过程和结果")
    print("")
    
    print("🔍 调试步骤:")
    print("  1. 检查程序主界面日志:")
    print("     - 切换到数字人页面")
    print("     - 查看右侧日志区域")
    print("     - 寻找 [视频管理] 开头的日志")
    print("")
    print("  2. 创建测试任务:")
    print("     - 打开视频管理定时任务界面")
    print("     - 选择'程序内定时'标签")
    print("     - 点击'新建任务'")
    print("     - 设置:")
    print("       * 任务名称: 测试任务")
    print("       * 描述: 测试程序内定时")
    print("       * 启用任务: 勾选")
    print("       * 开始时间: 当前时间+1分钟")
    print("       * 重复间隔: 2分钟")
    print("       * 持续时间: 10分钟")
    print("       * 执行日期: 全部勾选")
    print("")
    print("  3. 观察日志输出:")
    print("     - 任务创建后应该看到:")
    print("       [视频管理] 程序内定时任务已启动：测试任务，间隔 2 分钟，持续 10 小时")
    print("     - 到达开始时间后应该看到:")
    print("       [视频管理] 定时任务触发: 测试任务")
    print("     - 然后应该看到:")
    print("       [视频管理] 视频管理脚本已启动，进程ID: XXXX")
    print("")
    
    print("  4. 检查脚本执行日志:")
    print("     - 打开文件管理器")
    print("     - 导航到项目根目录")
    print("     - 进入 feiyingshuziren/log/今天日期/ 文件夹")
    print("     - 查找最新的 video_management_auto_*.log 文件")
    print("     - 打开查看详细执行日志")
    print("")
    
    print("⚠️ 可能的问题:")
    print("  1. 任务没有启动:")
    print("     - 检查任务是否启用")
    print("     - 检查开始时间是否正确")
    print("     - 检查重复间隔是否大于0")
    print("")
    print("  2. 任务启动但不触发:")
    print("     - 检查程序是否一直运行")
    print("     - 检查任务是否在持续时间内")
    print("     - 检查系统时间是否正确")
    print("")
    print("  3. 任务触发但脚本不执行:")
    print("     - 检查脚本路径是否正确")
    print("     - 检查Python环境是否可用")
    print("     - 检查权限是否充足")
    print("")
    
    print("🔧 故障排除命令:")
    print("  手动测试脚本:")
    print("    python src/video_management_runner.py --task-type material_only")
    print("")
    print("  检查日志目录:")
    print("    dir feiyingshuziren\\log")
    print("    dir feiyingshuziren\\log\\2025-01-08")
    print("")
    print("  检查进程:")
    print("    tasklist | findstr python")
    print("")
    
    print("📝 日志示例:")
    print("  正常的程序内定时任务日志应该是:")
    print("    [17:45:00] [视频管理] 程序内定时任务已启动：测试任务，间隔 2 分钟，无限制运行")
    print("    [17:46:00] [视频管理] 定时任务触发: 测试任务")
    print("    [17:46:00] [视频管理] 执行完整流程：素材更新 → 飞影上传 → 自动重命名")
    print("    [17:46:00] [视频管理] 视频管理脚本已启动，进程ID: 12345")
    print("    [17:46:01] [视频管理] 视频管理任务 '测试任务' 正在执行中...")
    print("    [17:46:06] [视频管理] 视频管理任务 '测试任务' 正在执行中...")
    print("    [17:46:30] [视频管理] 视频管理任务 '测试任务' 执行成功完成")
    print("")
    
    print("📂 日志文件位置:")
    print("  程序日志: 程序主界面 → 数字人页面 → 右侧日志区域")
    print("  脚本日志: feiyingshuziren/log/YYYY-MM-DD/video_management_auto_HH-MM-SS.log")
    print("")
    
    print("🚀 测试建议:")
    print("  1. 重启程序以应用修复")
    print("  2. 创建一个2分钟间隔的测试任务")
    print("  3. 观察程序主界面的日志输出")
    print("  4. 等待任务触发并检查执行情况")
    print("  5. 查看 feiyingshuziren/log 目录中的详细日志")
    print("")
    
    print("=" * 60)
    print("🔍 调试信息已提供")
    print("🔧 请按照步骤进行测试和调试")
    print("=" * 60)


def check_log_directory():
    """检查日志目录"""
    print("\n" + "=" * 40)
    print("📂 检查日志目录")
    print("=" * 40)
    
    # 检查项目根目录
    project_root = os.getcwd()
    print(f"项目根目录: {project_root}")
    
    # 检查feiyingshuziren目录
    feiyingshuziren_dir = os.path.join(project_root, "feiyingshuziren")
    if os.path.exists(feiyingshuziren_dir):
        print(f"✅ feiyingshuziren目录存在: {feiyingshuziren_dir}")
        
        # 检查log目录
        log_dir = os.path.join(feiyingshuziren_dir, "log")
        if os.path.exists(log_dir):
            print(f"✅ log目录存在: {log_dir}")
            
            # 列出日期目录
            try:
                date_dirs = [d for d in os.listdir(log_dir) if os.path.isdir(os.path.join(log_dir, d))]
                if date_dirs:
                    print(f"📅 找到日期目录: {date_dirs}")
                    
                    # 检查最新的日期目录
                    latest_date = max(date_dirs)
                    latest_dir = os.path.join(log_dir, latest_date)
                    print(f"📂 最新日期目录: {latest_dir}")
                    
                    # 列出日志文件
                    log_files = [f for f in os.listdir(latest_dir) if f.endswith('.log')]
                    if log_files:
                        print(f"📝 找到日志文件: {log_files}")
                        
                        # 显示最新的日志文件
                        vm_logs = [f for f in log_files if 'video_management' in f]
                        if vm_logs:
                            latest_log = max(vm_logs)
                            latest_log_path = os.path.join(latest_dir, latest_log)
                            print(f"📄 最新视频管理日志: {latest_log_path}")
                        else:
                            print("⚠️ 没有找到视频管理日志文件")
                    else:
                        print("⚠️ 日期目录中没有日志文件")
                else:
                    print("⚠️ log目录中没有日期目录")
            except Exception as e:
                print(f"❌ 读取log目录失败: {e}")
        else:
            print(f"❌ log目录不存在: {log_dir}")
    else:
        print(f"❌ feiyingshuziren目录不存在: {feiyingshuziren_dir}")
        print("💡 这可能是第一次运行，目录会在任务执行时自动创建")


if __name__ == "__main__":
    test_timer_debug()
    check_log_directory()
