"""
测试最终改进
"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta

# 添加src路径
sys.path.append('src')


def test_date_filtering():
    """测试日期筛选逻辑"""
    print("=" * 60)
    print("🧪 测试日期筛选逻辑改进")
    print("=" * 60)
    
    avatar_list_path = "data/avatar_list.xlsx"
    if not os.path.exists(avatar_list_path):
        print("❌ 数据文件不存在")
        return
    
    # 读取Excel文件
    df = pd.read_excel(avatar_list_path)
    print(f"📊 总数据量: {len(df)} 条")
    
    # 转换更新日期列
    df["更新日期"] = pd.to_datetime(df["更新日期"], errors='coerce').dt.date
    
    # 计算日期范围
    today = datetime.now().date()
    three_days_ago = today - timedelta(days=3)
    
    print(f"📅 今天: {today}")
    print(f"📅 3天前: {three_days_ago}")
    
    # 测试不同的筛选条件
    print(f"\n🔍 筛选条件测试:")
    
    # 1. 原来的条件（只筛选今天）
    mask_today = (df["是否上传飞影"] == "是") & (
        (df["是否重命名"].isna()) |
        (df["是否重命名"] == "") |
        (df["是否重命名"] != "是")
    ) & (df["更新日期"] == today)
    today_count = mask_today.sum()
    print(f"  只筛选今天的视频: {today_count} 条")
    
    # 2. 新的条件（筛选最近3天）
    mask_recent = (df["是否上传飞影"] == "是") & (
        (df["是否重命名"].isna()) |
        (df["是否重命名"] == "") |
        (df["是否重命名"] != "是")
    ) & (df["更新日期"] >= three_days_ago)
    recent_count = mask_recent.sum()
    print(f"  筛选最近3天的视频: {recent_count} 条")
    
    # 3. 全量条件（不限制日期）
    mask_all = (df["是否上传飞影"] == "是") & (
        (df["是否重命名"].isna()) |
        (df["是否重命名"] == "") |
        (df["是否重命名"] != "是")
    )
    all_count = mask_all.sum()
    print(f"  全量筛选（不限日期）: {all_count} 条")
    
    # 显示最近3天的记录
    if recent_count > 0:
        print(f"\n📋 最近3天需要重命名的视频:")
        recent_df = df[mask_recent]
        for i, (_, row) in enumerate(recent_df.iterrows()):
            actor_name = str(row.get("拍摄演员名称", "")).strip()
            video_id = str(row.get("ID", "")).strip()
            update_date = row.get("更新日期", "")
            rename_status = row.get("是否重命名", "")
            print(f"    {i+1}. {actor_name}-{video_id} | 日期: {update_date} | 重命名: {repr(rename_status)}")
    
    print(f"\n💡 改进效果:")
    if recent_count > today_count:
        print(f"  ✅ 筛选范围扩大: {today_count} → {recent_count} 条")
        print(f"  ✅ 增加了 {recent_count - today_count} 个视频")
    elif recent_count == today_count:
        print(f"  ℹ️ 筛选结果相同: {recent_count} 条")
    else:
        print(f"  ⚠️ 筛选结果减少（不应该发生）")


def test_upload_status_update():
    """测试上传状态更新逻辑"""
    print("\n" + "=" * 60)
    print("🧪 测试上传状态更新逻辑")
    print("=" * 60)
    
    print("📋 上传完成后的更新逻辑:")
    print("  1. 设置 '是否上传飞影' = '是'")
    print("  2. 更新 '更新日期' = 今天的日期时间")
    print("  3. 这样就能被最近3天的筛选条件捕获")
    
    # 模拟更新日期的格式
    today = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"  示例更新日期格式: {today}")


def test_ui_improvements():
    """测试UI改进"""
    print("\n" + "=" * 60)
    print("🧪 测试UI改进")
    print("=" * 60)
    
    print("📋 UI改进内容:")
    print("  1. ✅ 按钮名称: '自动重命名' → '飞影重命名'")
    print("  2. ✅ 按钮位置: 移到'刷新表格'按钮后面")
    print("  3. ✅ 对话框标题: '确认重命名' → '确认飞影重命名'")
    print("  4. ✅ 日志信息: 更新为'飞影重命名'相关")


def test_workflow_logic():
    """测试工作流程逻辑"""
    print("\n" + "=" * 60)
    print("🧪 测试工作流程逻辑")
    print("=" * 60)
    
    print("📋 飞影上传完成后的自动重命名流程:")
    print("  1. 飞影上传完成")
    print("  2. 更新Excel文件:")
    print("     - 设置 '是否上传飞影' = '是'")
    print("     - 更新 '更新日期' = 当前时间")
    print("  3. 刷新表格显示")
    print("  4. 筛选最近3天上传且未重命名的视频")
    print("  5. 自动开始重命名")
    print("  6. 重命名完成后弹窗总结")
    
    print("\n📋 手动'飞影重命名'按钮流程:")
    print("  1. 点击'飞影重命名'按钮")
    print("  2. 筛选所有已上传但未重命名的视频（不限日期）")
    print("  3. 确认对话框")
    print("  4. 开始重命名")
    print("  5. 重命名完成后弹窗总结")


def main():
    """主测试函数"""
    test_date_filtering()
    test_upload_status_update()
    test_ui_improvements()
    test_workflow_logic()
    
    print("\n" + "=" * 60)
    print("🎯 最终改进测试完成")
    print("=" * 60)
    print("\n🚀 改进总结:")
    print("  ✅ 筛选条件: 今天 → 最近3天")
    print("  ✅ 上传完成后更新'更新日期'")
    print("  ✅ 按钮名称和位置优化")
    print("  ✅ 工作流程逻辑完善")
    
    print("\n🚀 下一步:")
    print("  1. 重启主程序: python src/main.py")
    print("  2. 进入视频管理界面")
    print("  3. 观察'飞影重命名'按钮位置")
    print("  4. 测试完整的上传+重命名流程")


if __name__ == "__main__":
    main()
