#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试数据一致性和重复加载修复
1. 重复ID检测和去重
2. 数据一致性检查
3. 避免重复加载
"""

import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_duplicate_id_handling():
    """测试重复ID处理"""
    print("=== 测试重复ID处理 ===")
    
    try:
        with open("src/core/video_material_manager.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查去重相关代码
        dedup_features = [
            "drop_duplicates(subset=['ID']",  # ID去重
            "duplicate_ids = df[df['ID'].duplicated",  # 重复ID检测
            "保留最新记录",  # 保留最新记录的注释
            "sort_values('更新日期', ascending=False",  # 按更新日期排序
        ]
        
        for feature in dedup_features:
            if feature in content:
                print(f"✅ 去重功能存在: {feature}")
            else:
                print(f"❌ 去重功能缺失: {feature}")
                return False
        
        # 检查去重在数据读取和保存时都有处理
        read_dedup_count = content.count("drop_duplicates(subset=['ID']")
        if read_dedup_count >= 2:
            print(f"✅ 去重处理在多个位置实现: {read_dedup_count} 处")
        else:
            print(f"❌ 去重处理不够完整: 只有 {read_dedup_count} 处")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 重复ID处理测试失败: {str(e)}")
        return False

def test_data_consistency():
    """测试数据一致性"""
    print("\n=== 测试数据一致性 ===")
    
    try:
        with open("src/core/video_material_manager.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查数据一致性相关代码
        consistency_features = [
            "发现.*重复ID",  # 重复ID检测日志
            "已去重.*保留最新记录",  # 去重日志
            "去重后的数据已保存",  # 保存去重数据
            "总计.*条记录",  # 记录统计
        ]
        
        import re
        for feature in consistency_features:
            if re.search(feature, content):
                print(f"✅ 一致性功能存在: {feature}")
            else:
                print(f"❌ 一致性功能缺失: {feature}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据一致性测试失败: {str(e)}")
        return False

def test_reload_prevention():
    """测试重复加载防止"""
    print("\n=== 测试重复加载防止 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查重复加载防止相关代码
        reload_features = [
            "force_reload=False",  # 默认不强制重新加载
            "self.vm_table.rowCount() > 0",  # 检查表格是否已有数据
            "表格已有数据，无需重新加载",  # 跳过重新加载的日志
            "force_reload=True",  # 强制重新加载的选项
        ]
        
        for feature in reload_features:
            if feature in content:
                print(f"✅ 重复加载防止功能存在: {feature}")
            else:
                print(f"❌ 重复加载防止功能缺失: {feature}")
                return False
        
        # 检查刷新按钮
        refresh_features = [
            "btn_refresh_table",  # 刷新表格按钮
            "on_refresh_table_clicked",  # 刷新按钮处理方法
            "刷新表格数据，重新检查重复ID",  # 按钮提示
        ]
        
        for feature in refresh_features:
            if feature in content:
                print(f"✅ 刷新功能存在: {feature}")
            else:
                print(f"❌ 刷新功能缺失: {feature}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 重复加载防止测试失败: {str(e)}")
        return False

def test_force_reload_scenarios():
    """测试强制重新加载的场景"""
    print("\n=== 测试强制重新加载场景 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查需要强制重新加载的场景
        force_reload_scenarios = [
            "on_material_update_completed.*force_reload=True",  # 素材更新完成
            "飞影上传完成.*force_reload=True",  # 飞影上传完成
            "on_refresh_table_clicked.*force_reload=True",  # 手动刷新
        ]
        
        import re
        for scenario in force_reload_scenarios:
            if re.search(scenario, content, re.DOTALL):
                print(f"✅ 强制重新加载场景存在: {scenario}")
            else:
                print(f"❌ 强制重新加载场景缺失: {scenario}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 强制重新加载场景测试失败: {str(e)}")
        return False

def test_logging_improvements():
    """测试日志改进"""
    print("\n=== 测试日志改进 ===")
    
    try:
        with open("src/core/video_material_manager.py", "r", encoding="utf-8") as f:
            vm_content = f.read()
        
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            ui_content = f.read()
        
        # 检查日志改进
        log_improvements = [
            ("发现.*重复ID", vm_content),  # 重复ID检测日志
            ("已去重.*条记录", vm_content),  # 去重统计日志
            ("表格已有数据，无需重新加载", ui_content),  # 跳过加载日志
            ("正在刷新表格数据", ui_content),  # 刷新开始日志
            ("表格数据刷新完成", ui_content),  # 刷新完成日志
        ]
        
        import re
        for log_pattern, content in log_improvements:
            if re.search(log_pattern, content):
                print(f"✅ 日志改进存在: {log_pattern}")
            else:
                print(f"❌ 日志改进缺失: {log_pattern}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 日志改进测试失败: {str(e)}")
        return False

def test_code_quality():
    """测试代码质量"""
    print("\n=== 测试代码质量 ===")
    
    try:
        with open("src/core/video_material_manager.py", "r", encoding="utf-8") as f:
            vm_content = f.read()
        
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            ui_content = f.read()
        
        # 检查代码质量
        quality_checks = [
            ("try:", vm_content.count("try:")),
            ("except Exception as e:", vm_content.count("except Exception as e:")),
            ("self.log_message.emit", vm_content.count("self.log_message.emit")),
            ("self.append_vm_log", ui_content.count("self.append_vm_log")),
        ]
        
        for check_name, count in quality_checks:
            if count > 0:
                print(f"✅ {check_name}: {count} 处")
            else:
                print(f"❌ {check_name}: 未找到")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 代码质量测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 数据一致性和重复加载修复测试")
    print("=" * 60)
    
    # 测试重复ID处理
    if not test_duplicate_id_handling():
        print("❌ 重复ID处理测试失败")
        return False
    
    # 测试数据一致性
    if not test_data_consistency():
        print("❌ 数据一致性测试失败")
        return False
    
    # 测试重复加载防止
    if not test_reload_prevention():
        print("❌ 重复加载防止测试失败")
        return False
    
    # 测试强制重新加载场景
    if not test_force_reload_scenarios():
        print("❌ 强制重新加载场景测试失败")
        return False
    
    # 测试日志改进
    if not test_logging_improvements():
        print("❌ 日志改进测试失败")
        return False
    
    # 测试代码质量
    if not test_code_quality():
        print("❌ 代码质量测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！")
    print("\n📋 修复总结:")
    print("✅ 1. 重复ID处理:")
    print("  - 数据读取时自动检测重复ID")
    print("  - 按更新日期排序，保留最新记录")
    print("  - 数据保存时进行去重处理")
    print("  - 自动保存去重后的数据")
    
    print("\n✅ 2. 数据一致性保障:")
    print("  - 详细的重复ID检测日志")
    print("  - 去重统计信息显示")
    print("  - 数据保存确认日志")
    print("  - 错误处理和恢复机制")
    
    print("\n✅ 3. 重复加载防止:")
    print("  - 检查表格是否已有数据")
    print("  - 默认不重复加载相同数据")
    print("  - 提供强制刷新选项")
    print("  - 添加手动刷新按钮")
    
    print("\n✅ 4. 智能刷新机制:")
    print("  - 素材更新完成后强制刷新")
    print("  - 飞影上传完成后强制刷新")
    print("  - 手动刷新按钮可随时刷新")
    print("  - 模块切换时智能判断是否需要加载")
    
    print("\n🎯 解决的问题:")
    print("  - 表格内容与本地不一致 → 自动去重和数据同步")
    print("  - 重复ID问题 → 智能去重保留最新记录")
    print("  - 每次切换都重新加载 → 智能加载机制")
    print("  - 数据不一致 → 完善的一致性检查")
    
    print("\n🚀 预期效果:")
    print("  - 表格数据与本地文件完全一致")
    print("  - 不再有重复ID的问题")
    print("  - 模块切换更快，不重复加载")
    print("  - 数据更新及时，一致性更好")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
