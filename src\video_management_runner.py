#!/usr/bin/env python3
"""
视频管理自动化任务脚本
执行顺序：素材更新 → 飞影上传 → 自动重命名
"""

import sys
import os
import asyncio
import argparse
from datetime import datetime
import traceback

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 全局日志文件句柄
log_file = None

def setup_logging():
    """设置日志文件到feiyingshuziren文件夹"""
    global log_file

    # 创建feiyingshuziren/log文件夹（在项目根目录下）
    # 从src目录向上一级到项目根目录
    root_dir = os.path.dirname(project_root)
    log_dir = os.path.join(root_dir, "feiyingshuziren", "log")
    os.makedirs(log_dir, exist_ok=True)

    # 创建按日期的子文件夹
    today = datetime.now().strftime("%Y-%m-%d")
    daily_log_dir = os.path.join(log_dir, today)
    os.makedirs(daily_log_dir, exist_ok=True)

    # 创建日志文件
    timestamp = datetime.now().strftime("%H-%M-%S")
    log_filename = f"video_management_auto_{timestamp}.log"
    log_filepath = os.path.join(daily_log_dir, log_filename)

    # 打开日志文件
    log_file = open(log_filepath, 'w', encoding='utf-8')
    return log_filepath

def log_message(message):
    """记录日志消息"""
    global log_file
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_line = f"[{timestamp}] {message}"
    
    # 输出到控制台
    print(log_line)
    
    # 写入日志文件
    if log_file:
        log_file.write(log_line + '\n')
        log_file.flush()

class HeadlessLogAdapter:
    """无头模式日志适配器，将Qt信号转换为普通日志输出"""
    def __init__(self):
        pass
    
    def emit(self, message):
        """模拟Qt信号的emit方法"""
        log_message(message)

def run_material_update():
    """执行素材更新任务"""
    try:
        log_message("=" * 60)
        log_message("🔄 开始执行素材更新任务")
        log_message("=" * 60)
        
        # 导入视频素材管理器
        from core.video_material_manager import VideoMaterialManager
        from core.config_manager import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建视频素材管理器
        video_manager = VideoMaterialManager(config_manager)
        
        # 连接日志信号到适配器
        log_adapter = HeadlessLogAdapter()
        video_manager.log_message.connect(log_adapter.emit)
        
        # 执行素材更新
        log_message("🔄 开始更新视频素材数据...")
        # 使用异步方法下载素材数据
        import asyncio
        success = asyncio.run(video_manager.download_material_data())
        
        if success:
            log_message("✅ 素材更新任务完成")
            return True
        else:
            log_message("❌ 素材更新任务失败")
            return False
            
    except Exception as e:
        log_message(f"❌ 素材更新任务异常: {str(e)}")
        log_message(f"详细错误: {traceback.format_exc()}")
        return False

async def run_hifly_upload():
    """执行飞影上传任务"""
    try:
        log_message("=" * 60)
        log_message("🚀 开始执行飞影上传任务")
        log_message("=" * 60)
        
        # 导入飞影上传管理器
        from core.hifly_upload_manager import HiflyUploadManager
        from core.config_manager import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建飞影上传管理器
        upload_manager = HiflyUploadManager(config_manager)
        
        # 连接日志信号到适配器
        log_adapter = HeadlessLogAdapter()
        upload_manager.log_message.connect(log_adapter.emit)
        
        # 获取待上传的视频列表
        log_message("🔍 获取待上传的视频列表...")
        
        # 这里需要从视频管理数据中获取需要上传的视频
        # 暂时使用示例数据，实际应该从Excel文件中读取
        upload_list = []
        
        # 从视频素材管理器获取需要上传的数据
        from core.video_material_manager import VideoMaterialManager
        video_manager = VideoMaterialManager(config_manager)
        
        # 获取最近一周的数据
        recent_data = video_manager.get_recent_week_data()
        
        # 筛选需要上传的视频（是否上传飞影 = 否）
        for record in recent_data:
            if record.get('是否上传飞影', '否') == '否':
                upload_list.append({
                    'video_path': record.get('视频路径', ''),
                    'actor_name': record.get('演员', ''),
                    'video_id': record.get('ID', ''),
                    'title': f"{record.get('演员', '')}-{record.get('ID', '')}"
                })
        
        if not upload_list:
            log_message("ℹ️ 没有需要上传的视频")
            return True
        
        log_message(f"📋 找到 {len(upload_list)} 个待上传视频")

        # 设置上传队列
        upload_manager.upload_queue = upload_list

        # 执行上传
        success = upload_manager.start_upload()

        if success:
            log_message("✅ 飞影上传任务完成")
            return True
        else:
            log_message("❌ 飞影上传任务失败")
            return False
        
    except Exception as e:
        log_message(f"❌ 飞影上传任务异常: {str(e)}")
        log_message(f"详细错误: {traceback.format_exc()}")
        return False

async def run_auto_rename():
    """执行自动重命名任务"""
    try:
        log_message("=" * 60)
        log_message("✏️ 开始执行自动重命名任务")
        log_message("=" * 60)
        
        # 导入重命名自动化
        from core.hifly_rename_automation import batch_rename_videos
        
        # 获取需要重命名的视频列表
        log_message("🔍 获取需要重命名的视频列表...")
        
        # 从视频素材管理器获取需要重命名的数据
        from core.video_material_manager import VideoMaterialManager
        from core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        video_manager = VideoMaterialManager(config_manager)
        
        # 获取最近一周的数据
        recent_data = video_manager.get_recent_week_data()
        
        # 筛选需要重命名的视频（是否重命名 = 否）
        rename_list = []
        for record in recent_data:
            if record.get('是否重命名', '否') == '否':
                rename_list.append({
                    'actor_name': record.get('演员', ''),
                    'video_id': record.get('ID', '')
                })
        
        if not rename_list:
            log_message("ℹ️ 没有需要重命名的视频")
            return True
        
        log_message(f"📋 找到 {len(rename_list)} 个待重命名视频")
        
        # 执行批量重命名
        results = await batch_rename_videos(rename_list)
        
        # 统计结果
        success_count = sum(1 for r in results if "成功" in r.get('result', ''))
        total_count = len(results)
        
        log_message(f"📊 重命名完成: {success_count}/{total_count} 成功")
        
        # 更新数据库中的重命名状态
        for result in results:
            if "成功" in result.get('result', ''):
                # 这里应该更新Excel文件中的重命名状态
                log_message(f"✅ {result['actor_name']}-{result['video_id']} 重命名成功")
        
        log_message("✅ 自动重命名任务完成")
        return True
        
    except Exception as e:
        log_message(f"❌ 自动重命名任务异常: {str(e)}")
        log_message(f"详细错误: {traceback.format_exc()}")
        return False

async def main():
    """主函数"""
    try:
        # 解析命令行参数
        parser = argparse.ArgumentParser(description='视频管理自动化任务脚本')
        parser.add_argument('--task-type', default='full_process',
                          choices=['full_process', 'material_only', 'upload_only', 'rename_only',
                                 'material_upload', 'upload_rename'],
                          help='任务类型')
        args = parser.parse_args()

        # 设置日志
        log_filepath = setup_logging()
        log_message(f"📝 日志文件: {log_filepath}")

        log_message("🚀 视频管理自动化任务开始")
        log_message(f"🎯 任务类型: {args.task_type}")
        log_message(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 根据任务类型确定要执行的任务
        tasks_to_run = []
        if args.task_type == 'full_process':
            tasks_to_run = ['material', 'upload', 'rename']
        elif args.task_type == 'material_only':
            tasks_to_run = ['material']
        elif args.task_type == 'upload_only':
            tasks_to_run = ['upload']
        elif args.task_type == 'rename_only':
            tasks_to_run = ['rename']
        elif args.task_type == 'material_upload':
            tasks_to_run = ['material', 'upload']
        elif args.task_type == 'upload_rename':
            tasks_to_run = ['upload', 'rename']

        log_message(f"📋 将执行 {len(tasks_to_run)} 个任务: {', '.join(tasks_to_run)}")

        # 任务执行标志
        all_success = True
        completed_count = 0

        # 执行任务
        for i, task_type in enumerate(tasks_to_run, 1):
            if task_type == 'material':
                log_message(f"\n" + "=" * 60)
                log_message(f"📋 第{i}步：素材更新")
                log_message("=" * 60)

                if run_material_update():
                    completed_count += 1
                    log_message("✅ 素材更新任务完成")
                else:
                    log_message("❌ 素材更新任务失败")
                    all_success = False

            elif task_type == 'upload':
                log_message(f"\n" + "=" * 60)
                log_message(f"📤 第{i}步：飞影上传")
                log_message("=" * 60)

                if await run_hifly_upload():
                    completed_count += 1
                    log_message("✅ 飞影上传任务完成")
                else:
                    log_message("❌ 飞影上传任务失败")
                    all_success = False

            elif task_type == 'rename':
                log_message(f"\n" + "=" * 60)
                log_message(f"🏷️ 第{i}步：自动重命名")
                log_message("=" * 60)

                if await run_auto_rename():
                    completed_count += 1
                    log_message("✅ 自动重命名任务完成")
                else:
                    log_message("❌ 自动重命名任务失败")
                    all_success = False

        # 任务完成总结
        log_message("\n" + "=" * 60)
        log_message("📊 任务执行总结")
        log_message("=" * 60)
        log_message(f"🎯 任务类型: {args.task_type}")
        log_message(f"📋 总任务数: {len(tasks_to_run)}")
        log_message(f"✅ 完成任务: {completed_count}")
        log_message(f"❌ 失败任务: {len(tasks_to_run) - completed_count}")
        if len(tasks_to_run) > 0:
            success_rate = (completed_count / len(tasks_to_run)) * 100
            log_message(f"📈 成功率: {success_rate:.1f}%")

        if all_success:
            log_message("🎉 所有任务执行成功！")
        else:
            log_message("⚠️ 部分任务执行失败，请检查日志")

        log_message(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        log_message("🚀 视频管理自动化任务结束")

    except Exception as e:
        log_message(f"❌ 主程序异常: {str(e)}")
        log_message(f"详细错误: {traceback.format_exc()}")

    finally:
        # 关闭日志文件
        if log_file:
            log_file.close()

if __name__ == "__main__":
    # 运行主程序
    asyncio.run(main())
