#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试简化日志功能
验证详细调试日志已被注释或移除
"""

import os
import sys
import re

def test_simplified_logging():
    """测试日志简化效果"""
    print("=== 测试日志简化效果 ===")
    
    files_to_check = [
        "src/ui/main_window.py",
        "src/core/video_material_manager.py"
    ]
    
    # 应该被简化的日志模式
    verbose_patterns = [
        # 表格填充详细日志
        r"🔍.*处理第.*行.*原始索引",
        r"🔍.*行.*列.*->",
        r"✅.*行.*列.*设置成功",
        r"🔍.*DataFrame索引",
        r"🔍.*DataFrame列名",
        
        # 数据读取详细日志
        r"📊.*缓存无效.*重新读取",
        r"📊.*数据缓存已更新",
        r"📊.*读取文件成功",
        r"📊.*更新日期统计.*总计.*有效.*空",
        
        # 表格操作详细日志
        r"🧹.*表格已完全清空",
        r"📏.*表格行数已设置",
        r"📝.*开始填充.*行数据",
        r"✅.*数据填充完成",
        r"📋.*表格填充完成",
        r"📊.*表格已按.*排列",
        
        # 搜索高亮详细日志
        r"🔍.*高亮设置.*行.*列",
        r"🔍.*设置背景色.*实际背景色",
        r"🔍.*设置前景色.*实际前景色",
        r"🔍.*当前单元格.*行.*列",
        
        # Chrome调试详细日志
        r"🔍.*找到可用调试端口",
        r"🔍.*端口.*已被占用",
        r"🔍.*检查端口.*时出错",
        r"🔍.*找到Chrome用户数据目录",
    ]
    
    # 应该保留的关键日志模式
    important_patterns = [
        r"🔄.*正在加载.*数据",
        r"✅.*文件完整性检查通过",
        r"✅.*已加载.*条.*数据",
        r"🔍.*正在搜索",
        r"✅.*搜索.*找到.*个匹配项",
        r"搜索结果.*\/",
        r"❌.*失败",
        r"⚠️.*警告",
    ]
    
    results = {}
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            continue
            
        print(f"\n📁 检查文件: {file_path}")
        
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            file_results = {
                "verbose_found": [],
                "important_found": [],
                "commented_verbose": []
            }
            
            # 检查详细日志是否被简化
            for pattern in verbose_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    # 检查是否被注释
                    commented_matches = re.findall(rf"#.*{pattern}", content)
                    if commented_matches:
                        file_results["commented_verbose"].extend(commented_matches)
                        print(f"✅ 详细日志已注释: {pattern}")
                    else:
                        file_results["verbose_found"].extend(matches)
                        print(f"⚠️ 详细日志仍存在: {pattern}")
            
            # 检查重要日志是否保留
            for pattern in important_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    file_results["important_found"].extend(matches)
                    print(f"✅ 重要日志已保留: {pattern}")
            
            results[file_path] = file_results
            
        except Exception as e:
            print(f"❌ 检查文件失败: {file_path}, 错误: {str(e)}")
    
    return results

def test_search_logging():
    """测试搜索功能的日志简化"""
    print("\n=== 测试搜索功能日志简化 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查搜索相关的日志
        search_log_patterns = [
            (r"🔍.*正在搜索", "搜索开始日志", True),  # 应该保留
            (r"✅.*搜索.*找到.*个匹配项", "搜索结果日志", True),  # 应该保留
            (r"搜索结果.*\/", "搜索进度日志", True),  # 应该保留
            (r"🔍.*高亮设置.*行.*列", "高亮详细日志", False),  # 应该被简化
            (r"🔍.*设置背景色", "颜色设置日志", False),  # 应该被简化
        ]
        
        for pattern, description, should_exist in search_log_patterns:
            matches = re.findall(pattern, content)
            commented_matches = re.findall(rf"#.*{pattern}", content)
            
            if should_exist:
                if matches and not commented_matches:
                    print(f"✅ {description}正确保留")
                else:
                    print(f"❌ {description}被错误移除")
            else:
                if commented_matches or not matches:
                    print(f"✅ {description}正确简化")
                else:
                    print(f"⚠️ {description}仍然存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试搜索日志失败: {str(e)}")
        return False

def test_data_loading_logging():
    """测试数据加载的日志简化"""
    print("\n=== 测试数据加载日志简化 ===")
    
    try:
        with open("src/core/video_material_manager.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查数据加载相关的日志
        loading_log_patterns = [
            (r"🔄.*正在加载.*数据", "加载开始日志", True),  # 应该保留
            (r"✅.*文件完整性检查通过", "完整性检查日志", True),  # 应该保留
            (r"✅.*已加载.*条.*数据", "加载完成日志", True),  # 应该保留
            (r"📊.*缓存无效.*重新读取", "缓存详细日志", False),  # 应该被简化
            (r"📊.*数据缓存已更新", "缓存更新日志", False),  # 应该被简化
            (r"📊.*读取文件成功", "读取详细日志", False),  # 应该被简化
        ]
        
        for pattern, description, should_exist in loading_log_patterns:
            matches = re.findall(pattern, content)
            commented_matches = re.findall(rf"#.*{pattern}", content)
            
            if should_exist:
                if matches and not commented_matches:
                    print(f"✅ {description}正确保留")
                else:
                    print(f"❌ {description}被错误移除")
            else:
                if commented_matches or not matches:
                    print(f"✅ {description}正确简化")
                else:
                    print(f"⚠️ {description}仍然存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试数据加载日志失败: {str(e)}")
        return False

def generate_summary(results):
    """生成简化效果总结"""
    print("\n" + "=" * 60)
    print("📊 日志简化效果总结")
    print("=" * 60)
    
    total_verbose_found = 0
    total_commented = 0
    total_important_found = 0
    
    for file_path, file_results in results.items():
        verbose_count = len(file_results["verbose_found"])
        commented_count = len(file_results["commented_verbose"])
        important_count = len(file_results["important_found"])
        
        total_verbose_found += verbose_count
        total_commented += commented_count
        total_important_found += important_count
        
        print(f"\n📁 {file_path}:")
        print(f"  ✅ 已注释的详细日志: {commented_count}")
        print(f"  ⚠️ 仍存在的详细日志: {verbose_count}")
        print(f"  ✅ 保留的重要日志: {important_count}")
    
    print(f"\n🎯 总体效果:")
    print(f"  ✅ 已简化的详细日志: {total_commented}")
    print(f"  ⚠️ 仍需简化的日志: {total_verbose_found}")
    print(f"  ✅ 保留的重要日志: {total_important_found}")
    
    if total_verbose_found == 0:
        print(f"\n🎉 日志简化完成！所有详细日志都已被适当处理")
    else:
        print(f"\n⚠️ 还有 {total_verbose_found} 个详细日志需要进一步简化")
    
    print(f"\n💡 简化策略:")
    print(f"  - 保留关键操作的开始和结果日志")
    print(f"  - 简化或注释详细的过程日志")
    print(f"  - 保留错误和警告日志")
    print(f"  - 保留搜索结果和进度日志")

def main():
    """主测试函数"""
    print("🧪 日志简化功能测试")
    print("=" * 60)
    
    # 测试整体日志简化效果
    results = test_simplified_logging()
    
    # 测试搜索功能日志
    search_ok = test_search_logging()
    
    # 测试数据加载日志
    loading_ok = test_data_loading_logging()
    
    # 生成总结
    generate_summary(results)
    
    print(f"\n🎯 测试结果:")
    print(f"  - 搜索功能日志: {'✅ 通过' if search_ok else '❌ 需要调整'}")
    print(f"  - 数据加载日志: {'✅ 通过' if loading_ok else '❌ 需要调整'}")
    
    print(f"\n📋 用户体验改进:")
    print(f"  ✅ 减少了冗余的调试信息")
    print(f"  ✅ 保留了关键的操作反馈")
    print(f"  ✅ 保持了错误和警告提示")
    print(f"  ✅ 搜索功能仍有清晰的进度指示")
    
    print(f"\n🚀 预期效果:")
    print(f"  - 日志窗口更加清爽")
    print(f"  - 用户能快速找到关键信息")
    print(f"  - 调试信息在需要时可以重新启用")
    print(f"  - 应用性能略有提升（减少日志输出）")
    
    return search_ok and loading_ok

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
