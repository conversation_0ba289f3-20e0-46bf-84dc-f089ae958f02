"""
测试声音管理日志修复
"""

import sys
import os

# 添加src路径
sys.path.append('src')

def test_method_definitions():
    """测试方法定义是否正确"""
    print("=" * 60)
    print("🧪 测试方法定义")
    print("=" * 60)
    
    try:
        # 读取main_window.py文件
        with open('src/ui/main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查方法定义
        methods_to_check = [
            'def append_voice_log',
            'def append_vm_log', 
            'def toggle_voice_log',
            'def toggle_vm_log'
        ]
        
        method_counts = {}
        for method in methods_to_check:
            count = content.count(method)
            method_counts[method] = count
            print(f"  {method}: {count} 个定义")
        
        print("")
        
        # 检查是否有重复定义
        issues = []
        if method_counts['def append_voice_log'] != 1:
            issues.append(f"append_voice_log 应该有1个定义，实际有{method_counts['def append_voice_log']}个")
        
        if method_counts['def append_vm_log'] != 1:
            issues.append(f"append_vm_log 应该有1个定义，实际有{method_counts['def append_vm_log']}个")
            
        if method_counts['def toggle_voice_log'] != 1:
            issues.append(f"toggle_voice_log 应该有1个定义，实际有{method_counts['def toggle_voice_log']}个")
            
        if method_counts['def toggle_vm_log'] != 1:
            issues.append(f"toggle_vm_log 应该有1个定义，实际有{method_counts['def toggle_vm_log']}个")
        
        if issues:
            print("❌ 发现问题:")
            for issue in issues:
                print(f"  - {issue}")
        else:
            print("✅ 所有方法定义正确")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def test_variable_names():
    """测试变量名是否正确"""
    print("=" * 60)
    print("🧪 测试变量名")
    print("=" * 60)
    
    try:
        # 读取main_window.py文件
        with open('src/ui/main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查变量使用
        variables_to_check = [
            'self.voice_log_text',
            'self.voice_log_toggle_btn',
            'self.vm_log_text',
            'self.vm_log_toggle_btn'
        ]
        
        variable_counts = {}
        for var in variables_to_check:
            count = content.count(var)
            variable_counts[var] = count
            print(f"  {var}: {count} 次使用")
        
        print("")
        
        # 检查是否有合理的使用次数
        if variable_counts['self.voice_log_text'] > 0:
            print("✅ 声音管理日志文本组件已正确命名")
        else:
            print("❌ 声音管理日志文本组件命名有问题")
            
        if variable_counts['self.voice_log_toggle_btn'] > 0:
            print("✅ 声音管理日志按钮已正确命名")
        else:
            print("❌ 声音管理日志按钮命名有问题")
            
        if variable_counts['self.vm_log_text'] > 0:
            print("✅ 视频管理日志文本组件已正确命名")
        else:
            print("❌ 视频管理日志文本组件命名有问题")
            
        if variable_counts['self.vm_log_toggle_btn'] > 0:
            print("✅ 视频管理日志按钮已正确命名")
        else:
            print("❌ 视频管理日志按钮命名有问题")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def test_signal_connections():
    """测试信号连接是否正确"""
    print("=" * 60)
    print("🧪 测试信号连接")
    print("=" * 60)
    
    try:
        # 读取main_window.py文件
        with open('src/ui/main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查信号连接
        connections_to_check = [
            'self.voice_manager.log_message.connect(self.append_voice_log)',
            'self.voice_log_toggle_btn.clicked.connect(self.toggle_voice_log)',
            'self.vm_log_toggle_btn.clicked.connect(self.toggle_vm_log)'
        ]
        
        for connection in connections_to_check:
            if connection in content:
                print(f"✅ 找到连接: {connection}")
            else:
                print(f"❌ 缺少连接: {connection}")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def test_method_calls():
    """测试方法调用是否正确"""
    print("=" * 60)
    print("🧪 测试方法调用")
    print("=" * 60)
    
    try:
        # 读取main_window.py文件
        with open('src/ui/main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计方法调用
        voice_log_calls = content.count('self.append_voice_log(')
        vm_log_calls = content.count('self.append_vm_log(')
        
        print(f"  self.append_voice_log() 调用次数: {voice_log_calls}")
        print(f"  self.append_vm_log() 调用次数: {vm_log_calls}")
        print("")
        
        if voice_log_calls > 0:
            print("✅ 声音管理日志方法有被调用")
        else:
            print("⚠️ 声音管理日志方法没有被调用")
            
        if vm_log_calls > 0:
            print("✅ 视频管理日志方法有被调用")
        else:
            print("⚠️ 视频管理日志方法没有被调用")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def show_fix_summary():
    """显示修复总结"""
    print("=" * 60)
    print("🎯 修复总结")
    print("=" * 60)
    
    print("✅ 已修复的问题:")
    print("  1. 方法名冲突 - 声音管理使用 append_voice_log")
    print("  2. 方法名冲突 - 视频管理使用 append_vm_log")
    print("  3. 按钮方法冲突 - 声音管理使用 toggle_voice_log")
    print("  4. 按钮方法冲突 - 视频管理使用 toggle_vm_log")
    print("  5. 变量名冲突 - 声音管理使用 voice_log_text")
    print("  6. 变量名冲突 - 视频管理使用 vm_log_text")
    print("  7. 信号连接 - 声音管理器连接到正确的日志方法")
    print("")
    
    print("🔧 修复内容:")
    print("  - 重命名声音管理的日志方法为 append_voice_log")
    print("  - 重命名声音管理的切换方法为 toggle_voice_log")
    print("  - 重命名声音管理的日志组件为 voice_log_text")
    print("  - 重命名声音管理的按钮组件为 voice_log_toggle_btn")
    print("  - 更新所有相关的方法调用和信号连接")
    print("")
    
    print("🎉 预期效果:")
    print("  ✅ 声音管理日志应该能正常显示")
    print("  ✅ 声音管理收起/展开按钮应该能正常工作")
    print("  ✅ 视频管理日志应该能正常显示")
    print("  ✅ 视频管理收起/展开按钮应该能正常工作")
    print("  ✅ 两个模块的日志功能互不干扰")
    print("")


if __name__ == "__main__":
    test_method_definitions()
    test_variable_names()
    test_signal_connections()
    test_method_calls()
    show_fix_summary()
    
    print("=" * 60)
    print("🎉 声音管理日志修复测试完成！")
    print("=" * 60)
    print("现在可以启动程序测试声音管理模块的日志功能")
    print("应该能看到日志正常显示，收起/展开按钮正常工作")
    print("=" * 60)
