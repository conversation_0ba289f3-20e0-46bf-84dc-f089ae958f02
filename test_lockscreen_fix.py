"""
测试锁屏问题修复
"""

import sys
import os
import time
import asyncio

# 添加src路径
sys.path.append('src')

def test_system_utils():
    """测试系统工具功能"""
    print("=" * 60)
    print("🧪 测试系统工具功能")
    print("=" * 60)
    
    try:
        from utils.system_utils import SystemUtils
        
        # 测试锁屏检测
        is_locked = SystemUtils.is_system_locked()
        print(f"🔒 系统锁屏状态: {'已锁屏' if is_locked else '未锁屏'}")
        
        # 测试Chrome进程检测
        chrome_procs = SystemUtils.get_chrome_processes()
        print(f"🌐 Chrome进程数量: {len(chrome_procs)}")
        for proc in chrome_procs[:3]:  # 显示前3个
            print(f"   PID: {proc['pid']}, 名称: {proc['name']}")
        
        # 测试系统信息
        sys_info = SystemUtils.get_system_info()
        print(f"💻 系统信息:")
        for key, value in sys_info.items():
            print(f"   {key}: {value}")
        
        # 测试任务延迟判断
        should_delay, reason = SystemUtils.should_delay_task()
        print(f"⏸️ 是否延迟任务: {'是' if should_delay else '否'}")
        if should_delay:
            print(f"   延迟原因: {reason}")
        
        print("✅ 系统工具测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 系统工具测试失败: {e}")
        return False


def test_video_material_manager():
    """测试视频素材管理器的锁屏检测功能"""
    print("=" * 60)
    print("🧪 测试视频素材管理器锁屏检测")
    print("=" * 60)
    
    try:
        from core.video_material_manager import VideoMaterialManager
        from core.config_manager import ConfigManager
        
        # 创建管理器实例
        config_manager = ConfigManager()
        manager = VideoMaterialManager(config_manager)
        
        # 测试系统准备检查
        ready, status = manager.check_system_readiness()
        print(f"🔍 系统准备状态: {'准备就绪' if ready else '未准备好'}")
        print(f"   状态信息: {status}")
        
        # 如果系统未准备好，测试等待功能
        if not ready and "锁屏" in status:
            print("🔒 检测到锁屏状态，测试等待功能...")
            print("   (实际使用中会等待解锁，这里跳过)")
        
        print("✅ 视频素材管理器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 视频素材管理器测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False


def test_browser_stability_config():
    """测试浏览器稳定性配置"""
    print("=" * 60)
    print("🧪 测试浏览器稳定性配置")
    print("=" * 60)
    
    try:
        from core.video_material_manager import VideoMaterialManager
        from core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        manager = VideoMaterialManager(config_manager)
        
        print("🔧 浏览器配置参数:")
        print("   基础参数:")
        print("     --no-sandbox")
        print("     --disable-dev-shm-usage")
        print("     --disable-background-timer-throttling")
        print("     --disable-backgrounding-occluded-windows")
        print("     --disable-renderer-backgrounding")
        
        print(f"   无头模式: {'启用' if manager.headless_mode else '禁用'}")
        
        if manager.headless_mode:
            print("   无头模式参数:")
            print("     --disable-gpu")
            print("     --window-size=1920,1080")
            print("     --virtual-time-budget=5000")
        else:
            print("   有头模式参数:")
            print("     --start-maximized")
            print("     --disable-infobars")
        
        print("✅ 浏览器配置测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 浏览器配置测试失败: {e}")
        return False


def show_improvement_summary():
    """显示改进总结"""
    print("=" * 60)
    print("🎯 锁屏问题修复总结")
    print("=" * 60)
    
    print("✅ 已实现的改进:")
    print("  1. 锁屏检测功能")
    print("     - 检测LogonUI进程")
    print("     - 检查winlogon会话状态")
    print("     - 验证用户登录状态")
    print("")
    
    print("  2. 系统状态检查")
    print("     - CPU和内存使用率检查")
    print("     - Chrome进程数量监控")
    print("     - 综合系统准备状态评估")
    print("")
    
    print("  3. 智能等待机制")
    print("     - 锁屏时自动等待解锁")
    print("     - 可配置的最大等待时间")
    print("     - 定期状态检查和反馈")
    print("")
    
    print("  4. 优化的下载等待")
    print("     - 缩短超时时间(30秒)")
    print("     - 增加浏览器进程监控")
    print("     - 更频繁的状态检查(5秒)")
    print("     - 进程断开时立即停止")
    print("")
    
    print("  5. 增强的浏览器稳定性")
    print("     - 添加多个稳定性参数")
    print("     - 禁用后台限制功能")
    print("     - 优化无头模式配置")
    print("     - 减少进程被系统终止的可能")
    print("")
    
    print("🎯 预期效果:")
    print("  ✅ 锁屏时任务自动延迟执行")
    print("  ✅ 解锁后任务自动继续")
    print("  ✅ 浏览器进程更稳定")
    print("  ✅ 下载等待时间更合理")
    print("  ✅ 错误处理更完善")
    print("")


def show_usage_recommendations():
    """显示使用建议"""
    print("=" * 60)
    print("💡 使用建议")
    print("=" * 60)
    
    print("🕐 定时任务设置建议:")
    print("  1. 避开深夜时段(23:00-06:00)")
    print("  2. 选择工作时间执行(09:00-18:00)")
    print("  3. 设置合理的执行间隔(≥30分钟)")
    print("")
    
    print("🖥️ 系统环境建议:")
    print("  1. 保持系统不锁屏(工作时间)")
    print("  2. 确保网络连接稳定")
    print("  3. 避免同时运行大量Chrome进程")
    print("  4. 定期重启系统清理资源")
    print("")
    
    print("⚙️ 配置优化建议:")
    print("  1. 启用无头模式(headless_mode: true)")
    print("  2. 适当调整并发数量")
    print("  3. 监控系统资源使用")
    print("  4. 定期检查日志文件")
    print("")
    
    print("🔧 故障排除:")
    print("  1. 检查系统锁屏状态")
    print("  2. 查看Chrome进程数量")
    print("  3. 验证网络连接")
    print("  4. 检查磁盘空间")
    print("")


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始锁屏问题修复测试")
    print("=" * 60)
    
    results = []
    
    # 测试系统工具
    results.append(("系统工具", test_system_utils()))
    
    # 测试视频素材管理器
    results.append(("视频素材管理器", test_video_material_manager()))
    
    # 测试浏览器配置
    results.append(("浏览器配置", test_browser_stability_config()))
    
    # 显示测试结果
    print("=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("")
    if all_passed:
        print("🎉 所有测试通过！锁屏问题修复已就绪")
    else:
        print("⚠️ 部分测试失败，请检查相关模块")
    
    return all_passed


if __name__ == "__main__":
    # 运行测试
    success = run_all_tests()
    
    # 显示改进总结
    show_improvement_summary()
    
    # 显示使用建议
    show_usage_recommendations()
    
    print("=" * 60)
    print("🎯 总结")
    print("=" * 60)
    if success:
        print("✅ 锁屏问题修复完成，可以进行实际测试")
        print("💡 建议在锁屏状态下测试定时任务功能")
    else:
        print("❌ 修复过程中发现问题，请检查相关模块")
    print("=" * 60)
