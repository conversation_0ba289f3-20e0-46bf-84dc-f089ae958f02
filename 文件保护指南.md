# 重要文件保护指南

## 🔒 重要文件列表

以下文件是程序正常运行必需的，**绝对不能删除**：

### 数据文件
- `data/avatar_list.xlsx` - 视频素材数据（最重要）
- `data/voice_id_list.xlsx` - 声音ID数据
- `data/script_template.xlsx` - 脚本模板

### 配置文件
- `config/app_config.json` - 应用配置
- `config/settings.json` - 用户设置
- `config/text_extractor_rules.json` - 文本提取规则

## 🛡️ 保护措施

### 1. 备份机制
- 程序会自动创建 `data/avater_list备份.xlsx`
- 定期手动备份重要文件

### 2. 测试文件命名规范
- 测试文件必须以 `test_` 开头
- 临时文件使用 `temp_` 前缀
- 避免使用与重要文件相同的名称

### 3. 清理规则
- 只清理 `data/temp/` 目录中的文件
- 只删除明确标记为临时的文件
- 绝不删除主数据文件

## ⚠️ 注意事项

1. **测试后检查**：每次运行测试后检查重要文件是否存在
2. **备份恢复**：如果主文件丢失，从备份文件恢复
3. **版本控制**：重要文件应该纳入版本控制

## 🔧 恢复方法

如果 `avatar_list.xlsx` 丢失：
```bash
cp "data/avater_list备份.xlsx" "data/avatar_list.xlsx"
```

如果备份也丢失，需要重新下载素材数据。
