#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试蓝色高亮功能
验证搜索高亮颜色已从黄色改为蓝色
"""

import os
import sys
import re

def test_highlight_color_change():
    """测试高亮颜色修改"""
    print("=== 测试搜索高亮颜色修改 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否还有黄色高亮
        yellow_patterns = [
            r"#ffeb3b",
            r"黄色高亮",
            r"highlight_color.*ffeb3b",
        ]
        
        # 检查是否有蓝色高亮
        blue_patterns = [
            r"#4a90e2",
            r"蓝色高亮",
            r"highlight_color.*4a90e2",
        ]
        
        # 检查文字颜色
        text_color_patterns = [
            (r"color.*#ffffff", "白色文字", True),   # 应该存在
            (r"color.*#000000", "黑色文字", False),  # 应该被替换
            (r"白色文字", "白色文字注释", True),      # 应该存在
            (r"黑色文字", "黑色文字注释", False),     # 应该被替换
        ]
        
        print("\n🔍 检查黄色高亮残留:")
        yellow_found = False
        for pattern in yellow_patterns:
            matches = re.findall(pattern, content)
            if matches:
                print(f"❌ 仍有黄色高亮: {pattern} -> {matches}")
                yellow_found = True
            else:
                print(f"✅ 黄色高亮已移除: {pattern}")
        
        print("\n🔍 检查蓝色高亮:")
        blue_found = False
        for pattern in blue_patterns:
            matches = re.findall(pattern, content)
            if matches:
                print(f"✅ 蓝色高亮已添加: {pattern} -> {len(matches)}个")
                blue_found = True
            else:
                print(f"❌ 蓝色高亮缺失: {pattern}")
        
        print("\n🔍 检查文字颜色:")
        for pattern, description, should_exist in text_color_patterns:
            matches = re.findall(pattern, content)
            if should_exist:
                if matches:
                    print(f"✅ {description}正确存在: {len(matches)}个")
                else:
                    print(f"❌ {description}缺失")
            else:
                if matches:
                    print(f"⚠️ {description}仍然存在: {len(matches)}个")
                else:
                    print(f"✅ {description}已正确移除")
        
        # 统计修改的位置
        vm_blue_count = len(re.findall(r"vm_table.*#4a90e2", content))
        voice_blue_count = len(re.findall(r"voice_table.*#4a90e2", content))
        
        print(f"\n📊 修改统计:")
        print(f"  - 视频管理模块蓝色高亮: {vm_blue_count}处")
        print(f"  - 音频管理模块蓝色高亮: {voice_blue_count}处")
        print(f"  - 总计蓝色高亮: {vm_blue_count + voice_blue_count}处")
        
        return not yellow_found and blue_found
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_color_consistency():
    """测试颜色一致性"""
    print("\n=== 测试颜色一致性 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查所有高亮颜色是否一致
        highlight_colors = re.findall(r'highlight_color = QColor\("([^"]+)"\)', content)
        background_colors = re.findall(r'background-color: ([^;]+) !important', content)
        text_colors = re.findall(r'text_color = QColor\("([^"]+)"\)', content)
        font_colors = re.findall(r'color: ([^;]+) !important', content)
        
        print(f"🎨 发现的高亮颜色: {set(highlight_colors)}")
        print(f"🎨 发现的背景颜色: {set(background_colors)}")
        print(f"🎨 发现的文字颜色: {set(text_colors)}")
        print(f"🎨 发现的字体颜色: {set(font_colors)}")
        
        # 检查一致性
        expected_highlight = "#4a90e2"
        expected_text = "#ffffff"
        
        highlight_consistent = all(color == expected_highlight for color in highlight_colors)
        background_consistent = all(color.strip() == expected_highlight for color in background_colors)
        text_consistent = all(color == expected_text for color in text_colors)
        font_consistent = all(color.strip() == expected_text for color in font_colors)
        
        print(f"\n✅ 一致性检查:")
        print(f"  - 高亮颜色一致: {'✅' if highlight_consistent else '❌'}")
        print(f"  - 背景颜色一致: {'✅' if background_consistent else '❌'}")
        print(f"  - 文字颜色一致: {'✅' if text_consistent else '❌'}")
        print(f"  - 字体颜色一致: {'✅' if font_consistent else '❌'}")
        
        return highlight_consistent and background_consistent and text_consistent and font_consistent
        
    except Exception as e:
        print(f"❌ 一致性测试失败: {str(e)}")
        return False

def test_module_coverage():
    """测试模块覆盖率"""
    print("\n=== 测试模块覆盖率 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查各个模块的高亮功能
        modules = [
            ("视频管理", r"vm_table.*#4a90e2"),
            ("音频管理", r"voice_table.*#4a90e2"),
            ("脚本管理", r"table_scripts.*#4a90e2"),
        ]
        
        coverage_results = {}
        for module_name, pattern in modules:
            matches = re.findall(pattern, content)
            coverage_results[module_name] = len(matches)
            
            if matches:
                print(f"✅ {module_name}模块: {len(matches)}处蓝色高亮")
            else:
                print(f"⚠️ {module_name}模块: 未发现蓝色高亮")
        
        # 检查搜索功能
        search_functions = [
            ("视频管理搜索", r"def.*search.*vm"),
            ("音频管理搜索", r"def.*search.*voice"),
            ("脚本管理搜索", r"def.*search.*script"),
        ]
        
        print(f"\n🔍 搜索功能检查:")
        for func_name, pattern in search_functions:
            matches = re.findall(pattern, content)
            if matches:
                print(f"✅ {func_name}: 存在")
            else:
                print(f"⚠️ {func_name}: 未发现或命名不同")
        
        return sum(coverage_results.values()) >= 2  # 至少两个模块有高亮
        
    except Exception as e:
        print(f"❌ 模块覆盖率测试失败: {str(e)}")
        return False

def generate_color_preview():
    """生成颜色预览"""
    print("\n=== 颜色预览 ===")
    
    colors = {
        "蓝色高亮": "#4a90e2",
        "白色文字": "#ffffff", 
        "原黄色": "#ffeb3b",
        "原黑色": "#000000",
    }
    
    print("🎨 颜色对比:")
    for name, hex_color in colors.items():
        # 转换为RGB
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        print(f"  {name}: {hex_color.upper()} -> RGB{rgb}")
    
    print(f"\n💡 新的高亮方案:")
    print(f"  - 背景色: 蓝色 (#4A90E2) - 清晰、专业的蓝色")
    print(f"  - 文字色: 白色 (#FFFFFF) - 与蓝色背景形成良好对比")
    print(f"  - 对比度: 高对比度，易于阅读")
    print(f"  - 视觉效果: 现代、清爽、不刺眼")

def main():
    """主测试函数"""
    print("🧪 蓝色高亮功能测试")
    print("=" * 60)
    
    # 测试颜色修改
    color_ok = test_highlight_color_change()
    
    # 测试颜色一致性
    consistency_ok = test_color_consistency()
    
    # 测试模块覆盖率
    coverage_ok = test_module_coverage()
    
    # 生成颜色预览
    generate_color_preview()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    print(f"🎯 测试结果:")
    print(f"  - 颜色修改: {'✅ 通过' if color_ok else '❌ 失败'}")
    print(f"  - 颜色一致性: {'✅ 通过' if consistency_ok else '❌ 失败'}")
    print(f"  - 模块覆盖: {'✅ 通过' if coverage_ok else '❌ 失败'}")
    
    overall_success = color_ok and consistency_ok and coverage_ok
    
    if overall_success:
        print(f"\n🎉 蓝色高亮修改成功！")
        print(f"\n📋 修改总结:")
        print(f"  ✅ 黄色高亮 (#ffeb3b) → 蓝色高亮 (#4a90e2)")
        print(f"  ✅ 黑色文字 (#000000) → 白色文字 (#ffffff)")
        print(f"  ✅ 视频管理和音频管理模块都已更新")
        print(f"  ✅ 所有高亮方法都使用统一的蓝色方案")
        
        print(f"\n🚀 用户体验改进:")
        print(f"  - 蓝色高亮更加现代和专业")
        print(f"  - 白色文字在蓝色背景上对比度更好")
        print(f"  - 颜色不刺眼，长时间使用更舒适")
        print(f"  - 与应用整体设计风格更协调")
    else:
        print(f"\n❌ 部分测试失败，需要检查修改")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
