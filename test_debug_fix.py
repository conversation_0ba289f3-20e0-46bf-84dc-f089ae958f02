"""
测试调试修复
"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta

# 添加src路径
sys.path.append('src')


def simulate_get_uploaded_videos_for_rename(only_recent_uploaded=False):
    """模拟get_uploaded_videos_for_rename方法"""
    print("=" * 60)
    print(f"🧪 模拟get_uploaded_videos_for_rename (only_recent_uploaded={only_recent_uploaded})")
    print("=" * 60)
    
    try:
        avatar_list_path = "data/avatar_list.xlsx"
        if not os.path.exists(avatar_list_path):
            print("❌ 文件不存在")
            return []

        # 读取Excel文件
        df = pd.read_excel(avatar_list_path)
        print(f"🔍 读取Excel文件: {len(df)} 条记录")

        # 调试：检查刚上传的视频状态
        if only_recent_uploaded:
            # 检查最近更新的几条记录
            df_sorted = df.sort_values('更新日期', ascending=False)
            recent_records = df_sorted.head(5)
            print(f"🔍 最近更新的5条记录:")
            for _, row in recent_records.iterrows():
                video_id = row.get('ID', '')
                upload_status = row.get('是否上传飞影', '')
                rename_status = row.get('是否重命名', '')
                update_date = row.get('更新日期', '')
                print(f"  ID {video_id}: 上传={upload_status}, 重命名={rename_status}, 日期={update_date}")

        # 筛选已上传但未重命名的视频
        if "是否上传飞影" not in df.columns:
            print("❌ 缺少'是否上传飞影'列")
            return []

        # 添加是否重命名列（如果不存在）
        if "是否重命名" not in df.columns:
            print("⚠️ 添加'是否重命名'列")
            df["是否重命名"] = ""
            df.to_excel(avatar_list_path, index=False)
        else:
            print("✅ '是否重命名'列已存在")

        # 调试：统计各种状态的数量
        upload_yes_count = (df["是否上传飞影"] == "是").sum()
        rename_not_yes_count = (df["是否重命名"] != "是").sum()
        print(f"🔍 已上传飞影的记录: {upload_yes_count} 条")
        print(f"🔍 重命名状态不为'是'的记录: {rename_not_yes_count} 条")

        # 调试：检查"是否重命名"列的具体值
        rename_values = df["是否重命名"].value_counts(dropna=False)
        print(f"🔍 '是否重命名'列值统计:")
        for value, count in rename_values.items():
            print(f"  '{value}': {count} 条")

        if only_recent_uploaded:
            # 只重命名最近上传的视频（最近3天上传的）
            today = datetime.now().date()
            three_days_ago = today - timedelta(days=3)
            print(f"📅 日期范围: {three_days_ago} 至 {today}")

            # 筛选条件：是否上传飞影 = "是" 且 是否重命名 != "是" 且 更新日期是最近3天
            if "更新日期" in df.columns:
                try:
                    # 转换更新日期列为日期类型
                    df["更新日期"] = pd.to_datetime(df["更新日期"], errors='coerce').dt.date
                    print(f"✅ 日期列转换成功")
                except Exception as e:
                    print(f"❌ 日期列转换失败: {e}")
                    return []
                
                # 修复筛选条件：正确处理NaN值和字符串，筛选最近3天
                mask_upload = (df["是否上传飞影"] == "是")
                mask_rename = (
                    (df["是否重命名"].isna()) |  # NaN值
                    (df["是否重命名"] == "") |   # 空字符串
                    (df["是否重命名"] != "是")   # 其他值（如"重命名失败"）
                )
                mask_date = (df["更新日期"] >= three_days_ago)
                
                # 调试：分别统计各个条件
                print(f"🔍 筛选条件分解:")
                print(f"  上传飞影='是': {mask_upload.sum()} 条")
                print(f"  重命名状态需要处理: {mask_rename.sum()} 条")
                print(f"  更新日期>={three_days_ago}: {mask_date.sum()} 条")
                
                try:
                    mask = mask_upload & mask_rename & mask_date
                    print(f"✅ 筛选条件组合成功")
                except Exception as e:
                    print(f"❌ 筛选条件组合失败: {e}")
                    return []
                
                print(f"📅 筛选最近3天({three_days_ago} 至 {today})上传的视频进行重命名")
            else:
                # 如果没有更新日期列，则使用所有符合条件的
                mask = (df["是否上传飞影"] == "是") & (
                    (df["是否重命名"].isna()) |  # NaN值
                    (df["是否重命名"] == "") |   # 空字符串
                    (df["是否重命名"] != "是")   # 其他值（如"重命名失败"）
                )
                print("⚠️ 没有更新日期列，将重命名所有符合条件的视频")
        else:
            # 重命名所有符合条件的视频
            mask = (df["是否上传飞影"] == "是") & (
                (df["是否重命名"].isna()) |  # NaN值
                (df["是否重命名"] == "") |   # 空字符串
                (df["是否重命名"] != "是")   # 其他值（如"重命名失败"）
            )
            print("📋 筛选所有已上传但未重命名的视频")

        need_rename_df = df[mask]
        
        # 添加调试信息
        print(f"🔍 筛选结果: {len(need_rename_df)} 条记录符合条件")
        if len(need_rename_df) > 0:
            print(f"📋 符合条件的视频ID: {list(need_rename_df['ID'].head(5))}")

        video_list = []
        for _, row in need_rename_df.iterrows():
            try:
                actor_name = str(row.get("拍摄演员名称", "")).strip()
                video_id = str(row.get("ID", "")).strip()
                
                if actor_name and video_id:
                    video_list.append({
                        "actor_name": actor_name,
                        "video_id": video_id
                    })
                    print(f"  ✅ 添加: {actor_name}-{video_id}")
            except Exception as e:
                print(f"⚠️ 处理记录时出错: {e}")
                continue

        print(f"📊 最终返回: {len(video_list)} 个视频")
        return video_list

    except Exception as e:
        import traceback
        print(f"❌ 获取重命名视频列表失败: {str(e)}")
        print(f"❌ 异常详情: {traceback.format_exc()}")
        return []


def main():
    """主测试函数"""
    print("测试1: 最近上传模式")
    result1 = simulate_get_uploaded_videos_for_rename(only_recent_uploaded=True)
    
    print("\n" + "="*60)
    print("测试2: 全量模式")
    result2 = simulate_get_uploaded_videos_for_rename(only_recent_uploaded=False)
    
    print("\n" + "="*60)
    print("🎯 测试总结")
    print("="*60)
    print(f"最近上传模式: {len(result1)} 个视频")
    print(f"全量模式: {len(result2)} 个视频")
    
    if len(result1) > 0:
        print("✅ 最近上传模式正常")
    else:
        print("❌ 最近上传模式有问题")
    
    if len(result2) > 0:
        print("✅ 全量模式正常")
    else:
        print("❌ 全量模式有问题")


if __name__ == "__main__":
    main()
