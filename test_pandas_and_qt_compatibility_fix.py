#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试pandas和Qt兼容性修复
验证pandas参数兼容性和Qt方法调用修复
"""

import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_pandas_compatibility():
    """测试pandas兼容性修复"""
    print("=== 测试pandas兼容性修复 ===")
    
    try:
        import pandas as pd
        
        # 创建测试数据
        data = {
            'ID': [1, 2, 3, 4, 5],
            '更新日期': ['2025-07-31', '2025-07-30', None, '2025-07-29', '2025-07-28'],
            'name': ['A', 'B', 'C', 'D', 'E']
        }
        df = pd.DataFrame(data)
        df['更新日期'] = pd.to_datetime(df['更新日期'])
        
        print(f"pandas版本: {pd.__version__}")
        print(f"测试数据: {len(df)} 行")
        
        # 测试新版本参数
        try:
            result_new = df.sort_values('更新日期', ascending=False, na_position='last')
            print("✅ 新版本pandas参数(na_position='last')测试成功")
            new_param_works = True
        except TypeError as e:
            print(f"❌ 新版本pandas参数失败: {str(e)}")
            new_param_works = False
        
        # 测试旧版本参数
        try:
            result_old = df.sort_values('更新日期', ascending=False, na_last=True)
            print("✅ 旧版本pandas参数(na_last=True)测试成功")
            old_param_works = True
        except TypeError as e:
            print(f"❌ 旧版本pandas参数失败: {str(e)}")
            old_param_works = False
        
        # 验证兼容性代码逻辑
        if new_param_works or old_param_works:
            print("✅ pandas兼容性代码逻辑正确")
            return True
        else:
            print("❌ 两种参数都不工作，需要检查pandas版本")
            return False
        
    except Exception as e:
        print(f"❌ pandas兼容性测试失败: {str(e)}")
        return False

def test_pandas_compatibility_in_code():
    """测试代码中的pandas兼容性修复"""
    print("\n=== 测试代码中的pandas兼容性修复 ===")
    
    try:
        with open("src/core/video_material_manager.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查兼容性代码
        compatibility_features = [
            "na_position='last'",  # 新版本参数
            "na_last=True",        # 旧版本参数
            "try:",                # 异常处理
            "except TypeError:",   # 类型错误捕获
            "兼容旧版本pandas",     # 注释说明
        ]
        
        import re
        for feature in compatibility_features:
            count = len(re.findall(re.escape(feature), content))
            if count > 0:
                print(f"✅ 兼容性功能存在: {feature} (出现{count}次)")
            else:
                print(f"❌ 兼容性功能缺失: {feature}")
                return False
        
        # 检查所有sort_values调用都有兼容性处理
        sort_values_calls = re.findall(r'sort_values\([^)]*na_', content)
        if len(sort_values_calls) >= 3:  # 应该有3个地方使用了兼容性处理
            print(f"✅ 找到 {len(sort_values_calls)} 个兼容性处理的sort_values调用")
        else:
            print(f"❌ 兼容性处理的sort_values调用数量不足: {len(sort_values_calls)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 代码兼容性测试失败: {str(e)}")
        return False

def test_qt_update_method_fix():
    """测试Qt update方法修复"""
    print("\n=== 测试Qt update方法修复 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否修复了update()方法调用
        correct_calls = [
            "self.vm_table.viewport().update()",     # 视频管理表格
            "self.voice_table.viewport().update()",  # 音频管理表格
        ]
        
        wrong_calls = [
            "self.vm_table.update()",     # 错误的调用方式
            "self.voice_table.update()",  # 错误的调用方式
        ]
        
        import re
        
        # 检查正确的调用
        for call in correct_calls:
            if re.search(re.escape(call), content):
                print(f"✅ 正确的update调用存在: {call}")
            else:
                print(f"❌ 正确的update调用缺失: {call}")
                return False
        
        # 检查是否还有错误的调用
        for call in wrong_calls:
            # 排除正确调用中包含的部分
            pattern = re.escape(call) + r'(?!\.viewport\(\)\.update\(\))'
            if re.search(pattern, content):
                print(f"❌ 仍存在错误的update调用: {call}")
                return False
            else:
                print(f"✅ 已修复错误的update调用: {call}")
        
        return True
        
    except Exception as e:
        print(f"❌ Qt update方法测试失败: {str(e)}")
        return False

def test_error_handling_robustness():
    """测试错误处理健壮性"""
    print("\n=== 测试错误处理健壮性 ===")
    
    try:
        with open("src/core/video_material_manager.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查错误处理模式
        error_handling_patterns = [
            "try:.*na_position.*except TypeError:.*na_last",  # pandas兼容性处理
        ]
        
        import re
        for pattern in error_handling_patterns:
            if re.search(pattern, content, re.DOTALL):
                print(f"✅ 错误处理模式存在: {pattern}")
            else:
                print(f"❌ 错误处理模式缺失: {pattern}")
                return False
        
        # 检查异常处理的完整性
        try_blocks = len(re.findall(r'try:', content))
        except_blocks = len(re.findall(r'except TypeError:', content))
        
        if try_blocks >= except_blocks and except_blocks >= 3:
            print(f"✅ 异常处理完整: {try_blocks} try块, {except_blocks} except块")
        else:
            print(f"❌ 异常处理不完整: {try_blocks} try块, {except_blocks} except块")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理健壮性测试失败: {str(e)}")
        return False

def test_compatibility_comments():
    """测试兼容性注释"""
    print("\n=== 测试兼容性注释 ===")
    
    try:
        with open("src/core/video_material_manager.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查兼容性注释
        compatibility_comments = [
            "尝试使用新版本pandas的参数",
            "兼容旧版本pandas",
        ]
        
        for comment in compatibility_comments:
            if comment in content:
                print(f"✅ 兼容性注释存在: {comment}")
            else:
                print(f"❌ 兼容性注释缺失: {comment}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 兼容性注释测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 pandas和Qt兼容性修复测试")
    print("=" * 60)
    
    # 测试pandas兼容性
    if not test_pandas_compatibility():
        print("❌ pandas兼容性测试失败")
        return False
    
    # 测试代码中的pandas兼容性修复
    if not test_pandas_compatibility_in_code():
        print("❌ 代码pandas兼容性测试失败")
        return False
    
    # 测试Qt update方法修复
    if not test_qt_update_method_fix():
        print("❌ Qt update方法修复测试失败")
        return False
    
    # 测试错误处理健壮性
    if not test_error_handling_robustness():
        print("❌ 错误处理健壮性测试失败")
        return False
    
    # 测试兼容性注释
    if not test_compatibility_comments():
        print("❌ 兼容性注释测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！")
    print("\n📋 修复总结:")
    print("✅ 1. pandas兼容性修复:")
    print("  - 新版本pandas: 使用na_position='last'参数")
    print("  - 旧版本pandas: 使用na_last=True参数")
    print("  - 异常处理: try-except确保兼容性")
    print("  - 3个位置都添加了兼容性处理")
    
    print("\n✅ 2. Qt方法调用修复:")
    print("  - 修复QTableWidget.update()参数问题")
    print("  - 使用table.viewport().update()正确刷新")
    print("  - 视频管理和音频管理模块都已修复")
    
    print("\n✅ 3. 错误处理增强:")
    print("  - 添加TypeError异常捕获")
    print("  - 确保代码在不同pandas版本下都能工作")
    print("  - 提供清晰的兼容性注释说明")
    
    print("\n🎯 解决的问题:")
    print("  - 数据合并失败: na_last参数不兼容 → 添加na_position兼容性")
    print("  - 搜索出错: update()方法参数错误 → 使用viewport().update()")
    print("  - 版本兼容性: 不同pandas版本参数不同 → try-except处理")
    
    print("\n🚀 预期效果:")
    print("  - 素材更新的数据合并功能正常工作")
    print("  - 搜索高亮功能不再报错")
    print("  - 代码在不同版本的pandas下都能正常运行")
    print("  - 表格刷新正常，高亮显示正确")
    
    print("\n💡 技术要点:")
    print("  - pandas版本差异: na_last → na_position")
    print("  - Qt刷新机制: widget.update() → widget.viewport().update()")
    print("  - 兼容性设计: try新版本 → except用旧版本")
    print("  - 错误处理: 确保在任何情况下都有fallback方案")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
