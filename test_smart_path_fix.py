"""
测试智能路径修复
"""

import os
import sys

# 添加src路径
sys.path.append('src')


def test_smart_path_fix():
    """测试智能路径修复"""
    print("=" * 60)
    print("🔧 测试智能路径修复")
    print("=" * 60)
    
    print("🔍 问题分析:")
    print("  程序从src目录启动: python src/main.py")
    print("  导致工作目录为: d:\\project\\guangliu02\\src")
    print("  路径拼接结果: src + src/video_management_runner.py = src/src/video_management_runner.py")
    print("")
    
    print("✅ 智能修复方案:")
    print("  检测当前工作目录是否以'src'结尾")
    print("  如果是，向上一级到项目根目录")
    print("  然后正确拼接路径")
    print("")
    
    print("🧪 路径测试:")
    
    # 模拟从项目根目录运行
    print("  1. 从项目根目录运行:")
    root_dir = "d:\\project\\guangliu02"
    if not root_dir.endswith('src'):
        script_path = os.path.join(root_dir, "src", "video_management_runner.py")
        print(f"     工作目录: {root_dir}")
        print(f"     脚本路径: {script_path}")
        print(f"     路径正确: ✅")
    print("")
    
    # 模拟从src目录运行
    print("  2. 从src目录运行:")
    src_dir = "d:\\project\\guangliu02\\src"
    if src_dir.endswith('src'):
        project_root = os.path.dirname(src_dir)  # 向上一级
        script_path = os.path.join(project_root, "src", "video_management_runner.py")
        print(f"     工作目录: {src_dir}")
        print(f"     项目根目录: {project_root}")
        print(f"     脚本路径: {script_path}")
        print(f"     路径正确: ✅")
    print("")
    
    print("📝 修复后的代码逻辑:")
    print("""
current_dir = os.getcwd()

if current_dir.endswith('src'):
    # 程序从src目录启动，向上一级
    project_root = os.path.dirname(current_dir)
    script_path = os.path.join(project_root, "src", "video_management_runner.py")
else:
    # 程序从项目根目录启动
    script_path = os.path.join(current_dir, "src", "video_management_runner.py")

# 验证路径存在
if not os.path.exists(script_path):
    print(f"错误: 脚本文件不存在: {script_path}")
    return

print(f"使用脚本路径: {script_path}")
""")
    print("")
    
    print("🚀 测试建议:")
    print("  1. 程序已经在运行，修复会立即生效")
    print("  2. 创建新的程序内定时任务进行测试")
    print("  3. 观察日志中的脚本路径信息")
    print("  4. 验证任务执行成功")
    print("")
    
    print("📊 预期结果:")
    print("  日志应该显示:")
    print("    [视频管理] 使用脚本路径: d:\\project\\guangliu02\\src\\video_management_runner.py")
    print("    [视频管理] 视频管理脚本已启动，进程ID: XXXX")
    print("    [视频管理] 视频管理任务 '任务名' 执行成功完成")
    print("")
    print("  不应该再看到:")
    print("    ❌ can't open file 'd:\\\\project\\\\guangliu02\\\\src\\\\src\\\\video_management_runner.py'")
    print("")


def show_path_logic():
    """显示路径逻辑"""
    print("=" * 40)
    print("🧠 路径逻辑说明")
    print("=" * 40)
    
    print("情况1: 程序从项目根目录启动")
    print("  命令: python src/main.py")
    print("  工作目录: d:\\project\\guangliu02")
    print("  current_dir.endswith('src'): False")
    print("  脚本路径: d:\\project\\guangliu02 + src + video_management_runner.py")
    print("  结果: d:\\project\\guangliu02\\src\\video_management_runner.py ✅")
    print("")
    
    print("情况2: 程序从src目录启动")
    print("  命令: cd src && python main.py")
    print("  工作目录: d:\\project\\guangliu02\\src")
    print("  current_dir.endswith('src'): True")
    print("  项目根目录: os.path.dirname(current_dir) = d:\\project\\guangliu02")
    print("  脚本路径: d:\\project\\guangliu02 + src + video_management_runner.py")
    print("  结果: d:\\project\\guangliu02\\src\\video_management_runner.py ✅")
    print("")
    
    print("修复前的错误情况:")
    print("  工作目录: d:\\project\\guangliu02\\src")
    print("  错误拼接: d:\\project\\guangliu02\\src + src + video_management_runner.py")
    print("  错误结果: d:\\project\\guangliu02\\src\\src\\video_management_runner.py ❌")


def create_test_command():
    """创建测试命令"""
    print("\n" + "=" * 40)
    print("🧪 测试命令")
    print("=" * 40)
    
    print("现在可以测试的步骤:")
    print("  1. 程序正在运行，修复已生效")
    print("  2. 打开视频管理定时任务界面")
    print("  3. 创建新的程序内定时任务:")
    print("     - 任务名称: 路径测试")
    print("     - 重复间隔: 1分钟")
    print("     - 持续时间: 5分钟")
    print("  4. 观察日志输出")
    print("  5. 验证脚本路径正确")
    print("")
    
    print("预期的成功日志:")
    print("  [18:XX:XX] 定时任务触发: 路径测试")
    print("  [18:XX:XX] 开始执行视频管理自动化脚本...")
    print("  [18:XX:XX] 使用脚本路径: d:\\project\\guangliu02\\src\\video_management_runner.py")
    print("  [18:XX:XX] 视频管理脚本已启动，进程ID: XXXXX")
    print("  [18:XX:XX] 视频管理任务 '路径测试' 执行成功完成")


if __name__ == "__main__":
    test_smart_path_fix()
    show_path_logic()
    create_test_command()
