"""
测试上传重命名流程修复
"""

import os
import sys

# 添加src路径
sys.path.append('src')


def test_auth_file_path():
    """测试认证文件路径"""
    print("=" * 60)
    print("🧪 测试认证文件路径修复")
    print("=" * 60)
    
    # 测试相对路径
    auth_file_path = "feiyingshuziren/essential_auth_data.json"
    print(f"📁 相对路径: {auth_file_path}")
    print(f"   存在: {os.path.exists(auth_file_path)}")
    
    # 测试绝对路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    abs_auth_path = os.path.join(current_dir, "feiyingshuziren", "essential_auth_data.json")
    print(f"📁 绝对路径: {abs_auth_path}")
    print(f"   存在: {os.path.exists(abs_auth_path)}")
    
    # 测试修复后的路径逻辑
    try:
        # 模拟main_window.py中的路径计算
        # __file__ 在main_window.py中是 src/ui/main_window.py
        # os.path.dirname(__file__) = src/ui
        # os.path.dirname(os.path.dirname(__file__)) = src
        # os.path.dirname(os.path.dirname(os.path.dirname(__file__))) = 项目根目录
        
        # 模拟从src/ui/main_window.py计算
        mock_main_window_path = os.path.join("src", "ui", "main_window.py")
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(mock_main_window_path)))
        if not project_root:  # 如果是空字符串，表示当前目录
            project_root = "."
        
        fixed_auth_path = os.path.join(project_root, "feiyingshuziren", "essential_auth_data.json")
        print(f"📁 修复后路径: {fixed_auth_path}")
        print(f"   存在: {os.path.exists(fixed_auth_path)}")
        
        if os.path.exists(fixed_auth_path):
            print("✅ 认证文件路径修复成功")
        else:
            print("❌ 认证文件路径仍有问题")
            
    except Exception as e:
        print(f"❌ 路径计算出错: {e}")


def test_rename_module_import():
    """测试重命名模块导入"""
    print("\n" + "=" * 60)
    print("🧪 测试重命名模块导入")
    print("=" * 60)
    
    try:
        from core.hifly_rename_automation import batch_rename_videos, HiflyRenameAutomation
        print("✅ 重命名模块导入成功")
        
        # 测试默认认证文件路径
        test_video_list = [{"actor_name": "测试演员", "video_id": "12345"}]
        print("✅ 测试数据创建成功")
        
    except ImportError as e:
        print(f"❌ 重命名模块导入失败: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")


def test_flow_logic():
    """测试流程逻辑"""
    print("\n" + "=" * 60)
    print("🧪 测试优化后的流程逻辑")
    print("=" * 60)
    
    print("📋 优化后的流程:")
    print("  1. 飞影上传完成")
    print("  2. 记录上传完成日志")
    print("  3. 强制刷新表格数据（更新'是否上传飞影'列）")
    print("  4. 延迟1秒后自动开始重命名")
    print("  5. 获取今天上传的视频列表")
    print("  6. 在后台线程执行重命名")
    print("  7. 重命名完成后更新Excel状态")
    print("  8. 刷新表格显示")
    print("  9. 弹窗显示最终总结（上传+重命名）")
    
    print("\n💡 关键改进:")
    print("  ✅ 不再询问用户是否重命名，直接自动执行")
    print("  ✅ 先更新表格，再重命名，最后弹窗总结")
    print("  ✅ 使用正确的认证文件路径")
    print("  ✅ 分离上传后重命名和手动重命名的处理逻辑")
    print("  ✅ 最终弹窗包含上传和重命名的完整总结")


def main():
    """主测试函数"""
    test_auth_file_path()
    test_rename_module_import()
    test_flow_logic()
    
    print("\n" + "=" * 60)
    print("🎯 测试完成")
    print("=" * 60)
    print("\n🚀 下一步:")
    print("  1. 重启主程序: python src/main.py")
    print("  2. 进入视频管理界面")
    print("  3. 点击'飞影上传'测试完整流程")
    print("  4. 观察是否按新流程执行:")
    print("     - 上传完成后不弹窗询问")
    print("     - 自动刷新表格")
    print("     - 自动开始重命名")
    print("     - 最后弹窗显示总结")


if __name__ == "__main__":
    main()
