"""
测试进程修复
"""

import os
import sys
import json

# 添加src路径
sys.path.append('src')


def test_process_fix():
    """测试进程修复"""
    print("=" * 60)
    print("🔧 测试进程修复")
    print("=" * 60)
    
    print("🔍 问题分析:")
    print("  观察到的现象:")
    print("    ✅ 重命名进程成功启动和完成")
    print("    ✅ 获得了1个结果")
    print("    ❌ asyncio事件循环关闭时有未清理资源")
    print("    ❌ 浏览器打开后立即关闭")
    print("    ❌ 重命名失败（成功: 0, 失败: 1）")
    print("")
    print("  错误信息:")
    print("    - RuntimeError: Event loop is closed")
    print("    - ValueError: I/O operation on closed pipe")
    print("    - StreamWriter.__del__ 异常")
    print("")
    
    print("🔧 修复内容:")
    print("  1. ✅ 改进异步清理:")
    print("     - 浏览器关闭后额外等待2秒")
    print("     - 清理所有挂起的异步任务")
    print("     - 安全关闭事件循环")
    print("")
    print("  2. ✅ 修复认证文件路径:")
    print("     - 计算正确的项目根目录")
    print("     - 设置绝对路径到认证文件")
    print("     - 验证文件存在性")
    print("")
    print("  3. ✅ 增强错误处理:")
    print("     - 检查认证数据加载")
    print("     - 详细的初始化日志")
    print("     - 更好的异常捕获")
    print("")
    
    print("📋 修复后的执行流程:")
    print("  独立进程:")
    print("    1. 设置UTF-8输出编码")
    print("    2. 解析命令行参数")
    print("    3. 计算认证文件路径")
    print("    4. 验证认证文件存在")
    print("    5. 创建自动化实例")
    print("    6. 加载认证数据")
    print("    7. 初始化浏览器")
    print("    8. 执行重命名任务")
    print("    9. 安全关闭浏览器")
    print("    10. 清理异步任务")
    print("    11. 关闭事件循环")
    print("    12. 输出JSON结果")
    print("")
    
    print("🎯 预期效果:")
    print("  ✅ 不再有asyncio事件循环错误")
    print("  ✅ 认证文件能正确加载")
    print("  ✅ 浏览器能正常初始化")
    print("  ✅ 重命名任务能成功执行")
    print("  ✅ 进程能优雅退出")
    print("")
    
    print("🔍 调试信息:")
    print("  应该看到的日志:")
    print("    [START] 独立进程开始重命名...")
    print("    [AUTH] 认证文件路径: ...")
    print("    [AUTH] 文件存在: True")
    print("    [INIT] 正在初始化浏览器...")
    print("    [OK] 浏览器初始化完成")
    print("    [PROCESS] [1/1] 处理视频: ...")
    print("    [SUCCESS] ... 重命名成功")
    print("    [CLOSE] 正在关闭浏览器...")
    print("    [OK] 浏览器已关闭")
    print("    [CLEANUP] 清理挂起任务...")
    print("    [OK] 事件循环已关闭")
    print("")
    
    print("🚀 测试建议:")
    print("  1. 重启程序")
    print("  2. 再次进行飞影上传测试")
    print("  3. 观察详细的进程日志")
    print("  4. 检查认证文件是否正确加载")
    print("  5. 确认重命名是否成功")
    print("")
    
    print("💡 如果认证文件问题:")
    print("  检查文件路径:")
    print("    项目根目录/feiyingshuziren/essential_auth_data.json")
    print("  确保文件存在且格式正确")
    print("")
    
    print("=" * 60)
    print("🔧 进程修复完成")
    print("=" * 60)


def test_auth_file_path():
    """测试认证文件路径"""
    print("\n" + "=" * 40)
    print("🧪 测试认证文件路径")
    print("=" * 40)
    
    try:
        # 模拟子进程中的路径计算
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(script_dir)  # 因为测试脚本在根目录
        auth_file_path = os.path.join(project_root, "feiyingshuziren", "essential_auth_data.json")
        
        print(f"📁 脚本目录: {script_dir}")
        print(f"📁 项目根目录: {project_root}")
        print(f"📄 认证文件路径: {auth_file_path}")
        print(f"✅ 文件存在: {os.path.exists(auth_file_path)}")
        
        if os.path.exists(auth_file_path):
            file_size = os.path.getsize(auth_file_path)
            print(f"📊 文件大小: {file_size} 字节")
            print("✅ 认证文件路径测试通过")
        else:
            print("❌ 认证文件不存在")
            
    except Exception as e:
        print(f"❌ 路径测试失败: {str(e)}")


if __name__ == "__main__":
    test_process_fix()
    test_auth_file_path()
