"""
测试子进程简单运行
"""

import os
import sys
import json
import subprocess
import time

# 添加src路径
sys.path.append('src')


def test_subprocess_simple():
    """测试子进程简单运行"""
    print("=" * 60)
    print("🧪 测试子进程简单运行")
    print("=" * 60)
    
    try:
        # 准备测试数据
        test_data = {
            "video_list": [
                {
                    "actor_name": "测试演员",
                    "video_id": "12345"
                }
            ],
            "headless": True
        }
        
        # 启动子进程
        script_path = os.path.join("src", "core", "rename_process.py")
        json_str = json.dumps(test_data, ensure_ascii=True)
        cmd = [sys.executable, script_path, json_str]
        
        print(f"🚀 启动命令: {' '.join(cmd[:2])} [JSON_DATA]")
        print(f"📊 JSON数据长度: {len(json_str)} 字符")
        
        # 使用subprocess.Popen
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace'
        )
        
        print(f"✅ 进程已启动，PID: {process.pid}")
        
        # 等待进程完成，最多等待30秒
        start_time = time.time()
        while True:
            poll_result = process.poll()
            current_time = time.time()
            elapsed = current_time - start_time
            
            if poll_result is not None:
                print(f"🔄 进程完成，退出码: {poll_result}，耗时: {elapsed:.1f}秒")
                break
            elif elapsed > 30:
                print(f"⚠️ 进程超时（{elapsed:.1f}秒），强制终止")
                process.terminate()
                time.sleep(2)
                if process.poll() is None:
                    process.kill()
                break
            else:
                print(f"🔄 进程运行中... ({elapsed:.1f}秒)")
                time.sleep(2)
        
        # 读取输出
        try:
            stdout, stderr = process.communicate(timeout=5)
            
            print("\n📋 标准输出:")
            if stdout:
                lines = stdout.split('\n')
                for i, line in enumerate(lines[:20]):  # 只显示前20行
                    if line.strip():
                        print(f"  {i+1:2d}: {line}")
                if len(lines) > 20:
                    print(f"  ... (还有 {len(lines)-20} 行)")
            else:
                print("  (无输出)")
            
            print("\n⚠️ 错误输出:")
            if stderr:
                error_lines = stderr.split('\n')
                for i, line in enumerate(error_lines[:10]):  # 只显示前10行
                    if line.strip():
                        print(f"  {i+1:2d}: {line}")
                if len(error_lines) > 10:
                    print(f"  ... (还有 {len(error_lines)-10} 行)")
            else:
                print("  (无错误)")
            
            # 查找结果JSON
            result_found = False
            for line in stdout.split('\n'):
                if line.startswith('RESULT_JSON:'):
                    try:
                        result_data = json.loads(line[12:])
                        print(f"\n✅ 找到结果JSON:")
                        print(f"  成功: {result_data.get('success')}")
                        print(f"  结果数量: {len(result_data.get('results', []))}")
                        result_found = True
                    except Exception as e:
                        print(f"\n❌ 解析结果JSON失败: {str(e)}")
                    break
            
            if not result_found:
                print("\n❌ 未找到结果JSON")
                
        except subprocess.TimeoutExpired:
            print("⚠️ 读取输出超时")
            process.kill()
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        print(f"❌ 详细错误: {traceback.format_exc()}")
    
    print("\n" + "=" * 60)
    print("🧪 子进程测试完成")
    print("=" * 60)


if __name__ == "__main__":
    test_subprocess_simple()
