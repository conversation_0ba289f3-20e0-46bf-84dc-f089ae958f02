"""
测试视频管理脚本的参数支持
"""

import subprocess
import sys
import os


def test_video_management_runner_args():
    """测试视频管理脚本的参数支持"""
    print("=" * 60)
    print("🧪 测试视频管理脚本参数支持")
    print("=" * 60)
    
    # 测试不同的任务类型
    test_cases = [
        ("full_process", "完整流程"),
        ("material_only", "仅素材更新"),
        ("upload_only", "仅飞影上传"),
        ("rename_only", "仅自动重命名"),
        ("material_upload", "素材更新+飞影上传"),
        ("upload_rename", "飞影上传+自动重命名")
    ]
    
    for task_type, description in test_cases:
        print(f"\n🔧 测试任务类型: {task_type} ({description})")
        print("-" * 40)
        
        try:
            # 构建命令
            cmd = [
                sys.executable, 
                "src/video_management_runner.py", 
                "--task-type", 
                task_type
            ]
            
            print(f"执行命令: {' '.join(cmd)}")
            
            # 执行命令（只运行几秒钟就停止，避免实际执行）
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=os.getcwd()
            )
            
            # 等待2秒钟
            try:
                stdout, stderr = process.communicate(timeout=2)
                print(f"✅ 脚本正常启动")
                if stdout:
                    print(f"输出: {stdout[:200]}...")
                if stderr:
                    print(f"错误: {stderr[:200]}...")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"✅ 脚本正常启动（2秒后终止）")
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🧪 参数支持测试完成")
    print("=" * 60)


def test_help_command():
    """测试帮助命令"""
    print("\n🔍 测试帮助命令:")
    print("-" * 30)
    
    try:
        cmd = [sys.executable, "src/video_management_runner.py", "--help"]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            print("✅ 帮助命令正常工作")
            print("帮助信息:")
            print(result.stdout)
        else:
            print("❌ 帮助命令失败")
            print(f"错误: {result.stderr}")
    
    except Exception as e:
        print(f"❌ 测试帮助命令失败: {str(e)}")


if __name__ == "__main__":
    test_video_management_runner_args()
    test_help_command()
