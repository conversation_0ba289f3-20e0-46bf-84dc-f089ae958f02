#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
视频素材管理器
负责从网站下载素材表格，处理数据并更新本地文件
"""

import os
import sys
import time
import pandas as pd
import asyncio
import subprocess
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from PySide6.QtCore import QObject, Signal, QThread
import tempfile
import shutil

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

try:
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

# 导入系统工具
try:
    from utils.system_utils import SystemUtils
    SYSTEM_UTILS_AVAILABLE = True
except ImportError:
    SYSTEM_UTILS_AVAILABLE = False
    print("⚠️ 系统工具模块未找到，锁屏检测功能将受限")
    print("⚠️ Playwright未安装，视频素材管理功能将受限")

# 尝试导入Chrome MCP工具作为备选
try:
    # 这里可以导入Chrome MCP相关工具
    # 例如：from chrome_mcp import ChromeManager
    CHROME_MCP_AVAILABLE = False  # 暂时设为False，等待具体实现
except ImportError:
    CHROME_MCP_AVAILABLE = False


class VideoMaterialManager(QObject):
    """视频素材管理器"""
    
    # 信号定义
    log_message = Signal(str)
    progress_updated = Signal(int, int)  # 当前进度, 总进度
    update_completed = Signal(bool, str)  # 是否成功, 消息
    
    def __init__(self, config_manager=None):
        super().__init__()
        self.config_manager = config_manager
        
        # 文件路径 - 使用绝对路径确保正确性
        current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.data_dir = os.path.join(current_dir, "data")
        self.temp_dir = os.path.join(self.data_dir, "temp")
        self.avatar_list_path = os.path.join(self.data_dir, "avatar_list.xlsx")

        # 调试信息
        self.log_message.emit(f"📁 数据目录: {self.data_dir}")
        self.log_message.emit(f"📄 素材文件路径: {self.avatar_list_path}")
        self.log_message.emit(f"📋 文件是否存在: {os.path.exists(self.avatar_list_path)}")

        # 数据缓存和性能优化
        self._data_cache = None
        self._cache_timestamp = None
        self._file_last_modified = None
        
        # 确保目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # Chrome调试配置目录 - 使用默认Chrome用户数据目录，保持登录状态
        self.chrome_debug_dir = self.get_default_chrome_user_data_dir()

        # 调试信息
        self.log_message.emit(f"🔧 Chrome用户数据目录: {self.chrome_debug_dir}")
        self.log_message.emit(f"🍪 将使用现有的登录状态和Cookie")
        
        # 网站配置
        self.website_url = "https://zxsc.baidu-int.com/ccbms/Home/WorkVideo/WorkVideoManage/Report"

        # 浏览器调试端口配置
        self.debug_port = 9222
        self.original_debug_port = 9222
        self._debug_chrome_pid = None  # 记录调试Chrome进程ID

        # 无头模式配置（使用数字人模块的设置）
        self.headless_mode = config_manager.get("headless_mode", True)  # 默认启用无头模式
        self.log_message.emit(f"🔧 素材管理无头模式: {'启用' if self.headless_mode else '禁用'}")

    def find_available_debug_port(self) -> int:
        """查找可用的调试端口"""
        import socket

        # 尝试的端口范围
        ports_to_try = [9222, 9223, 9224, 9225, 9226]

        for port in ports_to_try:
            try:
                # 检查端口是否被占用
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.settimeout(1)
                    result = s.connect_ex(('127.0.0.1', port))
                    if result != 0:  # 端口未被占用
                        return port
            except Exception:
                pass

        # 如果所有预设端口都被占用，返回默认端口
        self.log_message.emit("⚠️ 所有预设端口都被占用，使用默认端口 9222")
        return 9222

    def get_default_chrome_user_data_dir(self) -> str:
        """获取默认Chrome用户数据目录"""
        import platform
        import os

        system = platform.system()

        if system == "Windows":
            # Windows Chrome用户数据目录
            chrome_dirs = [
                os.path.expanduser("~/AppData/Local/Google/Chrome/User Data"),
                os.path.expanduser("~/AppData/Local/Chromium/User Data"),
                "C:/Users/<USER>/AppData/Local/Google/Chrome/User Data".format(os.getenv('USERNAME', '')),
            ]
        elif system == "Darwin":  # macOS
            chrome_dirs = [
                os.path.expanduser("~/Library/Application Support/Google/Chrome"),
                os.path.expanduser("~/Library/Application Support/Chromium"),
            ]
        else:  # Linux
            chrome_dirs = [
                os.path.expanduser("~/.config/google-chrome"),
                os.path.expanduser("~/.config/chromium"),
            ]

        # 查找存在的Chrome用户数据目录
        for chrome_dir in chrome_dirs:
            if os.path.exists(chrome_dir):
                return chrome_dir

        # 如果没有找到默认目录，创建一个备用目录
        fallback_dir = os.path.join(os.path.expanduser("~/Documents"), "GuangliuAssistant", "chrome-profile")
        os.makedirs(fallback_dir, exist_ok=True)
        self.log_message.emit(f"⚠️ 未找到默认Chrome目录，使用备用目录: {fallback_dir}")
        return fallback_dir

    def clear_chrome_debug_data(self) -> bool:
        """清理Chrome登录相关数据（用于重置登录状态）"""
        try:
            if not os.path.exists(self.chrome_debug_dir):
                self.log_message.emit(f"📁 Chrome用户数据目录不存在: {self.chrome_debug_dir}")
                return False

            # 先尝试关闭可能正在运行的Chrome进程
            self.kill_debug_chrome_processes()

            # 等待进程完全关闭
            import time
            time.sleep(2)

            # 清理登录相关的文件和目录
            login_related_items = [
                "Default/Cookies",
                "Default/Cookies-journal",
                "Default/Login Data",
                "Default/Login Data-journal",
                "Default/Web Data",
                "Default/Web Data-journal",
                "Default/Local Storage",
                "Default/Session Storage",
                "Default/Network Action Predictor",
                "Default/Network Action Predictor-journal",
                "Default/Top Sites",
                "Default/Top Sites-journal",
                "Default/Visited Links",
                "Default/History",
                "Default/History-journal",
            ]

            cleared_count = 0
            for item in login_related_items:
                item_path = os.path.join(self.chrome_debug_dir, item)
                try:
                    if os.path.exists(item_path):
                        if os.path.isfile(item_path):
                            os.remove(item_path)
                        else:
                            import shutil
                            shutil.rmtree(item_path)
                        cleared_count += 1
                        self.log_message.emit(f"🧹 已清理: {item}")
                except Exception as e:
                    self.log_message.emit(f"⚠️ 清理 {item} 时出错: {str(e)}")

            if cleared_count > 0:
                self.log_message.emit(f"✅ 已清理 {cleared_count} 个登录相关项目，登录状态已重置")
                self.log_message.emit(f"💡 下次启动Chrome时需要重新登录")
            else:
                self.log_message.emit(f"📋 未找到需要清理的登录数据")

            return True

        except Exception as e:
            self.log_message.emit(f"❌ 清理登录数据失败: {str(e)}")
            return False

    def check_system_readiness(self) -> tuple[bool, str]:
        """
        检查系统是否准备好执行任务

        Returns:
            tuple: (是否准备好, 状态信息)
        """
        try:
            if SYSTEM_UTILS_AVAILABLE:
                # 检查锁屏状态
                if SystemUtils.is_system_locked():
                    return False, "系统处于锁屏状态，建议等待解锁后执行"

                # 检查系统资源
                should_delay, reason = SystemUtils.should_delay_task()
                if should_delay:
                    return False, f"系统资源不足: {reason}"

                # 检查Chrome进程
                chrome_procs = SystemUtils.get_chrome_processes()
                if len(chrome_procs) > 10:
                    return False, f"Chrome进程过多({len(chrome_procs)}个)，可能影响性能"

                return True, "系统状态良好"
            else:
                # 如果系统工具不可用，进行基本检查
                return True, "系统工具不可用，跳过详细检查"

        except Exception as e:
            self.log_message.emit(f"⚠️ 系统状态检查失败: {str(e)}")
            return True, "检查失败，继续执行"

    def wait_for_system_ready(self, max_wait_minutes: int = 30) -> bool:
        """
        等待系统准备好执行任务

        Args:
            max_wait_minutes: 最大等待时间（分钟）

        Returns:
            bool: True表示系统已准备好，False表示等待超时
        """
        if not SYSTEM_UTILS_AVAILABLE:
            return True

        start_time = time.time()
        max_wait_seconds = max_wait_minutes * 60

        while time.time() - start_time < max_wait_seconds:
            ready, status = self.check_system_readiness()
            if ready:
                self.log_message.emit(f"✅ 系统准备就绪: {status}")
                return True

            self.log_message.emit(f"⏳ 等待系统准备: {status}")

            # 如果是锁屏状态，使用专门的等待方法
            if "锁屏" in status:
                remaining_minutes = (max_wait_seconds - (time.time() - start_time)) / 60
                if SystemUtils.wait_for_unlock(int(remaining_minutes)):
                    continue
                else:
                    return False

            # 其他情况等待1分钟后重试
            time.sleep(60)

        self.log_message.emit(f"⏰ 等待系统准备超时 ({max_wait_minutes}分钟)")
        return False

    def check_debug_browser(self, verbose=False) -> bool:
        """检查调试端口浏览器是否可用"""
        try:
            import requests
            # 使用IPv4地址避免IPv6连接问题，增加连接超时时间
            url = f"http://127.0.0.1:{self.debug_port}/json"
            if verbose:
                self.log_message.emit(f"🔍 检查调试端口URL: {url}")

            response = requests.get(url, timeout=10)  # 增加超时时间到10秒

            if verbose:
                self.log_message.emit(f"🔍 调试端口响应状态: {response.status_code}")

            if response.status_code == 200:
                # 检查是否有可用的页面
                data = response.json()
                if verbose:
                    self.log_message.emit(f"🔍 调试端口返回数据: {len(data) if data else 0} 个页面")

                if data and len(data) > 0:
                    self.log_message.emit(f"🔍 检测到 {len(data)} 个浏览器页面")
                    return True
            return False
        except Exception as e:
            # 显示详细的错误信息用于调试
            if verbose or "10061" not in str(e):
                self.log_message.emit(f"🔍 调试端口检查异常: {str(e)}")
            return False

    def check_existing_chrome_processes(self) -> bool:
        """检查是否有现有的Chrome进程"""
        try:
            if PSUTIL_AVAILABLE:
                import psutil
                chrome_processes = []

                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                            cmdline = proc.info['cmdline']
                            if cmdline:
                                chrome_processes.append({
                                    'pid': proc.info['pid'],
                                    'cmdline': cmdline
                                })
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue

                if chrome_processes:
                    self.log_message.emit(f"🔍 发现 {len(chrome_processes)} 个Chrome进程")

                    # 检查是否有使用默认用户数据目录的进程
                    for proc_info in chrome_processes:
                        cmdline = proc_info['cmdline']

                        # 检查是否使用了相同的用户数据目录（显式指定）
                        for arg in cmdline:
                            if '--user-data-dir' in arg and self.chrome_debug_dir in arg:
                                self.log_message.emit(f"🔍 发现Chrome进程使用相同用户数据目录: PID {proc_info['pid']}")
                                return True

                        # 检查是否是普通Chrome进程（没有显式指定用户数据目录，使用默认目录）
                        has_user_data_dir = any('--user-data-dir' in arg for arg in cmdline)
                        has_remote_debugging = any('--remote-debugging-port' in arg for arg in cmdline)

                        if not has_user_data_dir and not has_remote_debugging:
                            # 这是一个普通的Chrome进程，使用默认用户数据目录
                            self.log_message.emit(f"🔍 发现普通Chrome进程（使用默认用户数据目录）: PID {proc_info['pid']}")
                            return True

            return False
        except Exception as e:
            self.log_message.emit(f"🔍 检查现有Chrome进程时出错: {str(e)}")
            return False

    def copy_essential_chrome_data(self, source_dir: str, target_dir: str) -> bool:
        """复制关键的Chrome用户数据到独立目录"""
        try:
            import shutil
            import os

            # 确保目标目录存在
            os.makedirs(target_dir, exist_ok=True)

            self.log_message.emit("📋 开始复制Chrome用户数据...")
            self.log_message.emit("💡 注意：Cookie文件可能被Chrome锁定，无法复制，需要手动登录")

            # 需要复制的关键文件和目录（按重要性排序）
            essential_items = [
                "Default/Network/Cookies",            # 最重要：新版Chrome的Cookie位置
                "Default/Cookies",                    # 旧版Chrome的Cookie位置（兼容）
                "Default/Cookies-journal",            # Cookie日志文件
                "Default/Login Data",                 # 登录数据
                "Default/Login Data-journal",         # 登录数据日志
                "Default/Web Data",                   # 网站数据
                "Default/Web Data-journal",           # 网站数据日志
                "Default/Preferences",                # 用户偏好
                "Default/Secure Preferences",         # 安全偏好
                "Default/Local Storage",              # 本地存储
                "Default/Session Storage",            # 会话存储
                "Default/Network Action Predictor",   # 网络预测
                "Default/Top Sites",                  # 常用网站
                "Default/History",                    # 浏览历史
                "Local State"                         # 全局状态
            ]

            copied_count = 0
            critical_files = ["Default/Network/Cookies", "Default/Cookies", "Default/Login Data", "Default/Preferences"]
            critical_copied = 0

            for item in essential_items:
                source_path = os.path.join(source_dir, item)
                target_path = os.path.join(target_dir, item)

                try:
                    if os.path.exists(source_path):
                        # 确保目标目录存在
                        os.makedirs(os.path.dirname(target_path), exist_ok=True)

                        if os.path.isfile(source_path):
                            shutil.copy2(source_path, target_path)
                            file_size = os.path.getsize(source_path)
                            self.log_message.emit(f"📋 已复制: {item} ({file_size} 字节)")
                        else:
                            shutil.copytree(source_path, target_path, dirs_exist_ok=True)
                            self.log_message.emit(f"📋 已复制目录: {item}")

                        copied_count += 1
                        if item in critical_files:
                            critical_copied += 1
                    else:
                        if item in critical_files:
                            self.log_message.emit(f"⚠️ 关键文件不存在: {item}")
                        else:
                            self.log_message.emit(f"📋 文件不存在（跳过）: {item}")
                except Exception as e:
                    self.log_message.emit(f"❌ 复制 {item} 时出错: {str(e)}")
                    if item in critical_files:
                        self.log_message.emit(f"⚠️ 关键文件复制失败，可能影响登录状态")

            # 检查Cookie复制情况
            cookie_copied = any("Cookies" in item for item in essential_items if os.path.exists(os.path.join(target_dir, item)))

            if copied_count > 0:
                self.log_message.emit(f"✅ 已复制 {copied_count} 个数据文件")
                if cookie_copied:
                    self.log_message.emit(f"✅ Cookie文件复制成功，登录状态应该保持")
                    return True
                elif critical_copied >= 2:  # 至少复制了2个其他关键文件
                    self.log_message.emit(f"⚠️ Cookie文件未复制（可能被锁定），但已复制其他关键文件")
                    self.log_message.emit(f"💡 启动后需要手动登录，但其他设置会保持")
                    return True  # 仍然返回True，允许继续
                else:
                    self.log_message.emit(f"⚠️ 只复制了 {critical_copied}/{len(critical_files)} 个关键文件")
                    self.log_message.emit(f"💡 启动后需要手动登录")
                    return True  # 改为返回True，允许继续
            else:
                self.log_message.emit("❌ 未能复制任何关键数据文件")
                self.log_message.emit("💡 将使用空白配置，启动后需要手动登录")
                return True  # 即使复制失败也返回True，使用空白配置

        except Exception as e:
            self.log_message.emit(f"❌ 复制Chrome数据失败: {str(e)}")
            return False

    def force_close_all_chrome_processes(self) -> bool:
        """强制关闭所有Chrome进程"""
        try:
            if PSUTIL_AVAILABLE:
                import psutil
                closed_count = 0

                # 获取所有Chrome进程
                chrome_processes = []
                for proc in psutil.process_iter(['pid', 'name']):
                    try:
                        if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                            chrome_processes.append(proc)
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue

                if chrome_processes:
                    self.log_message.emit(f"🔄 找到 {len(chrome_processes)} 个Chrome进程，开始关闭...")

                    # 先尝试优雅关闭
                    for proc in chrome_processes:
                        try:
                            proc.terminate()
                            closed_count += 1
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            continue

                    # 等待进程关闭
                    import time
                    time.sleep(2)

                    # 强制杀死仍在运行的进程
                    for proc in chrome_processes:
                        try:
                            if proc.is_running():
                                proc.kill()
                                self.log_message.emit(f"🔄 强制关闭进程 PID: {proc.pid}")
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            continue

                    self.log_message.emit(f"✅ 已处理 {closed_count} 个Chrome进程")
                    return True
                else:
                    self.log_message.emit("📋 未找到需要关闭的Chrome进程")
                    return True
            else:
                # 如果psutil不可用，使用系统命令
                import subprocess
                import platform

                if platform.system() == "Windows":
                    try:
                        # Windows下使用taskkill命令
                        subprocess.run(["taskkill", "/f", "/im", "chrome.exe"],
                                     capture_output=True, check=False)
                        self.log_message.emit("✅ 已使用taskkill关闭Chrome进程")
                        return True
                    except Exception as e:
                        self.log_message.emit(f"⚠️ 使用taskkill关闭Chrome失败: {str(e)}")
                        return False
                else:
                    try:
                        # Linux/Mac下使用pkill命令
                        subprocess.run(["pkill", "-f", "chrome"],
                                     capture_output=True, check=False)
                        self.log_message.emit("✅ 已使用pkill关闭Chrome进程")
                        return True
                    except Exception as e:
                        self.log_message.emit(f"⚠️ 使用pkill关闭Chrome失败: {str(e)}")
                        return False

        except Exception as e:
            self.log_message.emit(f"❌ 强制关闭Chrome进程失败: {str(e)}")
            return False

    def start_debug_browser(self) -> bool:
        """启动带调试端口的浏览器"""
        try:
            import subprocess
            import platform
            import time

            # 强制关闭所有现有Chrome进程，确保程序稳定运行
            has_existing_chrome = self.check_existing_chrome_processes()

            if has_existing_chrome:
                self.log_message.emit("🔍 检测到现有Chrome进程")
                self.log_message.emit("🔄 为确保程序稳定运行，将强制关闭所有Chrome进程")
                self.log_message.emit("💡 程序将使用独立的Chrome实例，不会影响后续使用")

                # 强制关闭所有Chrome进程
                self.force_close_all_chrome_processes()

                # 等待进程完全关闭
                time.sleep(3)
                self.log_message.emit("✅ 已关闭所有Chrome进程")
            else:
                self.log_message.emit("🔄 未检测到Chrome进程，直接启动")

            # 使用默认配置启动Chrome
            self.log_message.emit("🔄 使用默认Chrome配置启动调试实例")

            if platform.system() == "Windows":
                # Windows Chrome路径
                chrome_paths = [
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                    r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME'))
                ]

                chrome_path = None
                for path in chrome_paths:
                    if os.path.exists(path):
                        chrome_path = path
                        break

                if chrome_path:
                    # 确保调试端口可用（所有Chrome进程已被关闭）
                    self.log_message.emit("🔄 确保调试端口可用...")
                    
                    # 确保Chrome调试目录存在且可写
                    try:
                        os.makedirs(self.chrome_debug_dir, exist_ok=True)
                        # 测试目录是否可写
                        test_file = os.path.join(self.chrome_debug_dir, "test_write.tmp")
                        with open(test_file, 'w') as f:
                            f.write("test")
                        os.remove(test_file)
                        self.log_message.emit(f"✅ Chrome调试目录已创建: {self.chrome_debug_dir}")
                    except Exception as e:
                        self.log_message.emit(f"⚠️ Chrome调试目录创建失败: {str(e)}")
                        # 使用系统临时目录作为备选
                        import uuid
                        self.chrome_debug_dir = os.path.join(tempfile.gettempdir(), f"chrome-debug-{uuid.uuid4().hex[:8]}")
                        os.makedirs(self.chrome_debug_dir, exist_ok=True)
                        self.log_message.emit(f"🔄 使用备选目录: {self.chrome_debug_dir}")
                    
                    # 启动参数 - 简化配置，确保稳定运行
                    startup_args = [
                        chrome_path,
                        f"--remote-debugging-port={self.debug_port}",
                        f"--remote-debugging-address=0.0.0.0",  # 监听所有接口
                        f"--user-data-dir={self.chrome_debug_dir}",  # 使用用户数据目录
                        "--no-first-run",
                        "--no-default-browser-check",
                        "--disable-web-security",  # 允许跨域访问
                        "--disable-features=VizDisplayCompositor",
                        "--remote-allow-origins=*",  # 允许跨域访问
                        "--disable-dev-shm-usage",  # 避免共享内存问题
                        "--no-sandbox",  # 避免沙盒问题
                        "--disable-prompt-on-repost",  # 禁用重新提交提示
                        "--disable-domain-reliability",  # 禁用域可靠性
                        "--enable-logging",  # 启用日志
                        "--disable-client-side-phishing-detection",  # 禁用钓鱼检测
                        "--disable-component-update",  # 禁用组件更新
                        "--no-pings",  # 禁用ping
                        "--no-crash-upload",  # 禁用崩溃上传
                    ]

                    # 根据模式添加特定参数
                    if self.headless_mode:
                        startup_args.extend([
                            "--headless",  # 无头模式
                            "--disable-gpu",  # 禁用GPU
                            "--window-size=1920,1080",  # 设置窗口大小
                        ])
                        self.log_message.emit("🔧 启用无头模式")
                    else:
                        startup_args.extend([
                            "--new-window",  # 在新窗口中打开
                            "--window-name=光流助手调试窗口",  # 自定义窗口名称
                        ])
                        self.log_message.emit("🔧 启用有界面模式")

                    # 添加目标网站URL
                    startup_args.append(self.website_url)

                    # 启动Chrome进程
                    self.log_message.emit(f"🚀 启动Chrome命令: {' '.join(startup_args[:8])}...")
                    
                    # 使用CREATE_NEW_PROCESS_GROUP避免继承父进程的信号处理
                    import subprocess
                    process = subprocess.Popen(
                        startup_args, 
                        creationflags=subprocess.CREATE_NEW_PROCESS_GROUP,
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL
                    )
                    
                    self.log_message.emit(f"🚀 已启动Chrome调试实例，PID: {process.pid}, 端口: {self.debug_port}")
                    self.log_message.emit(f"🌐 已打开目标网站: {self.website_url}")
                    
                    # 记录调试Chrome进程ID，方便后续清理
                    self._debug_chrome_pid = process.pid
                    
                    # 等待Chrome完全启动并监听端口
                    self.log_message.emit("⏳ 等待Chrome调试端口完全启动...")
                    max_wait_time = 10  # 大幅减少等待时间到10秒
                    wait_interval = 1

                    for i in range(max_wait_time):
                        time.sleep(wait_interval)

                        # 在第5次检查后启用详细模式
                        verbose = i >= 4
                        if self.check_debug_browser(verbose=verbose):
                            self.log_message.emit(f"✅ Chrome调试端口启动成功 (等待{i+1}秒)")
                            return True

                        if i == 2:
                            self.log_message.emit("💡 Chrome正在启动调试端口，请稍候...")
                        elif i == 5:
                            self.log_message.emit("⏳ 启用详细检查模式...")
                        elif i == 7:
                            self.log_message.emit("⏳ 即将跳过等待，直接尝试连接...")

                    # 超时后的处理 - 更积极地跳过等待
                    self.log_message.emit(f"⚠️ 调试端口检查超时，但Chrome进程已启动 (PID: {process.pid})")
                    self.log_message.emit("💡 跳过端口检查，直接尝试Playwright连接...")

                    return True  # 即使超时也返回True，让后续连接逻辑处理
                    
                else:
                    self.log_message.emit("❌ 未找到Chrome浏览器")
                    return False
            else:
                # macOS/Linux
                # 确保Chrome调试目录存在
                try:
                    os.makedirs(self.chrome_debug_dir, exist_ok=True)
                except:
                    pass
                    
                subprocess.Popen([
                    "google-chrome",
                    f"--remote-debugging-port={self.debug_port}",
                    "--remote-debugging-address=0.0.0.0",
                    f"--user-data-dir={self.chrome_debug_dir}",
                    "--no-first-run",
                    "--no-default-browser-check",
                    "--disable-web-security",
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-extensions",
                    "--disable-default-apps",
                    "--new-window",
                    self.website_url
                ])
                return True

        except Exception as e:
            self.log_message.emit(f"❌ 启动调试浏览器失败: {str(e)}")
            return False
    
    def kill_debug_chrome_processes(self):
        """强制关闭所有Chrome调试进程"""
        try:
            if PSUTIL_AVAILABLE:
                import psutil
                closed_count = 0
                
                # 首先尝试关闭记录的调试进程
                if self._debug_chrome_pid:
                    try:
                        debug_proc = psutil.Process(self._debug_chrome_pid)
                        if debug_proc.is_running():
                            debug_proc.kill()
                            self.log_message.emit(f"🔄 已关闭记录的调试Chrome进程: PID {self._debug_chrome_pid}")
                            closed_count += 1
                            self._debug_chrome_pid = None
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        self.log_message.emit(f"🔍 记录的调试进程 PID {self._debug_chrome_pid} 已不存在")
                        self._debug_chrome_pid = None
                
                # 然后查找并关闭其他调试Chrome进程
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                            cmdline = proc.info['cmdline'] or []
                            # 关闭所有带调试端口的Chrome进程
                            if any(f'--remote-debugging-port={self.debug_port}' in arg for arg in cmdline):
                                if proc.info['pid'] != self._debug_chrome_pid:  # 避免重复关闭
                                    proc.kill()  # 使用kill而不是terminate，确保进程被强制关闭
                                    self.log_message.emit(f"🔄 强制关闭调试Chrome进程: PID {proc.info['pid']}")
                                    closed_count += 1
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        pass

                if closed_count > 0:
                    self.log_message.emit(f"🔄 已强制关闭 {closed_count} 个调试Chrome进程")
                else:
                    self.log_message.emit("🔍 未找到需要关闭的调试Chrome进程")
            else:
                # 如果psutil不可用，使用系统命令
                import subprocess
                try:
                    # Windows: 关闭所有chrome进程（谨慎使用）
                    subprocess.run(['taskkill', '/F', '/IM', 'chrome.exe'], 
                                 capture_output=True, text=True, timeout=5)
                    self.log_message.emit("🔄 已尝试关闭Chrome进程")
                except:
                    pass
                    
        except Exception as e:
            self.log_message.emit(f"⚠️ 进程清理异常: {str(e)}")
        
        # 重置记录的PID
        self._debug_chrome_pid = None
        
    def get_recent_week_data(self) -> pd.DataFrame:
        """获取最近7天的数据（只使用更新日期列，更新日期为空的不显示）- 优化性能版本"""
        try:
            if not os.path.exists(self.avatar_list_path):
                self.log_message.emit("avatar_list.xlsx文件不存在，返回空数据")
                return pd.DataFrame()

            # 使用缓存数据提高性能，首次读取时进行文件完整性检查
            df = self._get_cached_data(force_check=True)
            if df.empty:
                self.log_message.emit("❌ 无法读取数据文件")
                return pd.DataFrame()

            # 简化日志：只显示关键信息
            # self.log_message.emit(f"📊 读取文件成功，总计 {len(df)} 条记录")

            # 只使用更新日期列进行筛选
            if '更新日期' not in df.columns:
                self.log_message.emit("❌ 未找到'更新日期'列，返回空数据")
                return pd.DataFrame()

            # 计算7天前的日期（精确到天）
            seven_days_ago = datetime.now() - timedelta(days=7)
            seven_days_ago = seven_days_ago.replace(hour=0, minute=0, second=0, microsecond=0)

            # 优化：先过滤非空的更新日期，减少转换量
            non_null_mask = df['更新日期'].notna() & (df['更新日期'] != '') & (df['更新日期'] != 'NaT')
            if not non_null_mask.any():
                self.log_message.emit("📋 所有记录的更新日期都为空，返回空数据")
                return pd.DataFrame()

            # 只对非空数据进行日期转换
            df_filtered = df[non_null_mask].copy()
            df_filtered['更新日期'] = pd.to_datetime(df_filtered['更新日期'], errors='coerce')

            # 再次过滤转换失败的日期
            valid_date_mask = df_filtered['更新日期'].notna()
            df_filtered = df_filtered[valid_date_mask]

            # 统计信息（简化版）
            total_records = len(df)
            valid_date_records = len(df_filtered)
            # 简化统计日志
            # self.log_message.emit(f"📊 更新日期统计: 总计 {total_records} 条，有效更新日期 {valid_date_records} 条，空更新日期 {total_records - valid_date_records} 条")

            if valid_date_records == 0:
                self.log_message.emit("📋 所有记录的更新日期都为空，返回空数据")
                return pd.DataFrame()

            self.log_message.emit(f"📅 筛选条件: {seven_days_ago.strftime('%Y-%m-%d')} 之后的更新日期")

            # 筛选最近7天的数据
            recent_data = df_filtered[df_filtered['更新日期'] >= seven_days_ago]

            self.log_message.emit(f"✅ 筛选结果: {len(recent_data)} 条最近7天有更新日期的记录")

            # 按更新日期降序排列（最新的在前面），然后按ID降序排列
            if len(recent_data) > 0:
                # 先按更新日期排序，再按ID排序
                recent_data = recent_data.sort_values(['更新日期', 'ID'], ascending=[False, False])
                self.log_message.emit(f"📋 数据已按更新日期和ID降序排列")

                # 检查筛选后的数据是否有重复ID
                if 'ID' in recent_data.columns:
                    duplicate_ids = recent_data[recent_data['ID'].duplicated(keep=False)]['ID'].unique()
                    if len(duplicate_ids) > 0:
                        self.log_message.emit(f"⚠️ 筛选后数据中发现 {len(duplicate_ids)} 个重复ID: {list(duplicate_ids)[:5]}{'...' if len(duplicate_ids) > 5 else ''}")
                        # 去重，保留最新的记录
                        original_count = len(recent_data)
                        recent_data = recent_data.drop_duplicates(subset=['ID'], keep='first')
                        self.log_message.emit(f"🔄 筛选数据去重: {original_count} -> {len(recent_data)} 条记录")
                    else:
                        self.log_message.emit(f"✅ 筛选后数据无重复ID")
            else:
                self.log_message.emit("📋 没有找到最近7天有更新日期的记录")

            return recent_data

        except Exception as e:
            self.log_message.emit(f"❌ 获取最近7天数据失败: {str(e)}")
            return pd.DataFrame()
    
    def get_display_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """获取需要显示的列 - 优化性能版本"""
        if df.empty:
            return df

        # 预定义列映射顺序，提高性能
        column_mapping = [
            ("ID", "ID"),
            ("素材ID", "ID"),  # 兼容旧列名
            ("视频URL", "视频URL"),
            ("外链BOS地址", "视频URL"),  # 兼容旧列名
            ("上传人邮箱后缀", "上传人邮箱后缀"),
            ("拍摄演员名称", "拍摄演员名称"),
            ("视频版型", "视频版型"),
            ("场景", "场景"),
            ("表现形式", "表现形式"),
            ("服装", "服装"),
            ("是否上传飞影", "是否上传飞影"),
            ("是否重命名", "是否重命名"),
            ("上传时间", "上传时间"),
            ("更新日期", "更新日期")
        ]

        # 快速创建显示DataFrame
        display_data = {}
        added_columns = set()

        # 按顺序处理列映射
        for original_col, display_col in column_mapping:
            if original_col in df.columns and display_col not in added_columns:
                display_data[display_col] = df[original_col]  # 保持pandas索引一致性
                added_columns.add(display_col)

        # 创建DataFrame并重置索引，确保索引连续
        display_df = pd.DataFrame(display_data)
        display_df = display_df.reset_index(drop=True)  # 重置索引为连续的0,1,2,3...

        # 如果没有"是否上传飞影"列，添加空列
        if "是否上传飞影" not in display_df.columns:
            display_df["是否上传飞影"] = ""

        # 如果没有"是否重命名"列，添加空列
        if "是否重命名" not in display_df.columns:
            display_df["是否重命名"] = ""

        # 确保有日期列用于显示
        if "更新日期" not in display_df.columns and "上传时间" in display_df.columns:
            display_df["更新日期"] = display_df["上传时间"]

        # 最终去重检查，确保显示数据中没有重复ID
        if 'ID' in display_df.columns:
            original_count = len(display_df)
            # 检查是否有重复ID
            duplicate_ids = display_df[display_df['ID'].duplicated(keep=False)]['ID'].unique()
            if len(duplicate_ids) > 0:
                self.log_message.emit(f"⚠️ 显示数据中发现 {len(duplicate_ids)} 个重复ID，正在去重...")
                # 按更新日期降序排序，保留最新的记录
                if '更新日期' in display_df.columns:
                    try:
                        # 尝试使用新版本pandas的参数
                        display_df = display_df.sort_values('更新日期', ascending=False, na_position='last')
                    except TypeError:
                        # 兼容旧版本pandas
                        display_df = display_df.sort_values('更新日期', ascending=False, na_last=True)
                display_df = display_df.drop_duplicates(subset=['ID'], keep='first')
                self.log_message.emit(f"🔄 显示数据去重完成: {original_count} -> {len(display_df)} 条记录")

        self.log_message.emit(f"📋 显示列映射完成，包含 {len(display_df.columns)} 列")

        return display_df

    async def close_popup_if_exists(self, page):
        """检测并关闭可能存在的弹窗"""
        try:
            self.log_message.emit("🔍 检测页面弹窗...")

            # 定义可能的弹窗关闭按钮选择器
            popup_close_selectors = [
                'i.el-icon-close.btn-close',  # 用户提供的选择器
                '.el-icon-close.btn-close',   # 备用选择器1
                '.modal-mask .el-icon-close', # 备用选择器2
                '.el-dialog__close',          # Element UI对话框关闭按钮
                '.el-message-box__close',     # Element UI消息框关闭按钮
                '[aria-label="Close"]',       # 通用关闭按钮
                '.close-btn',                 # 通用关闭按钮类名
                '.btn-close'                  # 通用关闭按钮类名
            ]

            popup_found = False

            # 首先检查是否有遮罩层（通常表示有弹窗）
            mask_selectors = [
                '.modal-mask',
                '.el-dialog__wrapper',
                '.el-message-box__wrapper',
                '.overlay',
                '.popup-mask'
            ]

            for mask_selector in mask_selectors:
                try:
                    mask_element = await page.query_selector(mask_selector)
                    if mask_element:
                        # 检查遮罩是否可见
                        is_visible = await mask_element.is_visible()
                        if is_visible:
                            self.log_message.emit(f"🔍 发现弹窗遮罩: {mask_selector}")
                            popup_found = True
                            break
                except Exception:
                    continue

            if not popup_found:
                # 如果没有发现遮罩，直接检查关闭按钮
                for close_selector in popup_close_selectors:
                    try:
                        close_button = await page.query_selector(close_selector)
                        if close_button:
                            is_visible = await close_button.is_visible()
                            if is_visible:
                                self.log_message.emit(f"🔍 发现弹窗关闭按钮: {close_selector}")
                                popup_found = True
                                break
                    except Exception:
                        continue

            if popup_found:
                self.log_message.emit("❗ 检测到弹窗，尝试关闭...")

                # 尝试关闭弹窗
                closed = False
                for close_selector in popup_close_selectors:
                    try:
                        # 等待关闭按钮出现（短时间）
                        await page.wait_for_selector(close_selector, timeout=2000, state="visible")

                        # 点击关闭按钮
                        await page.click(close_selector)
                        self.log_message.emit(f"✅ 已点击关闭按钮: {close_selector}")

                        # 等待弹窗消失
                        await page.wait_for_timeout(1000)

                        # 验证弹窗是否已关闭
                        try:
                            close_button = await page.query_selector(close_selector)
                            if close_button:
                                is_still_visible = await close_button.is_visible()
                                if not is_still_visible:
                                    closed = True
                                    break
                            else:
                                closed = True
                                break
                        except Exception:
                            closed = True
                            break

                    except Exception as e:
                        # 这个选择器没有找到或点击失败，尝试下一个
                        continue

                if closed:
                    self.log_message.emit("✅ 弹窗已成功关闭")
                    # 额外等待确保页面稳定
                    await page.wait_for_timeout(1000)
                else:
                    self.log_message.emit("⚠️ 无法关闭弹窗，但程序将继续尝试")
            else:
                self.log_message.emit("✅ 未检测到弹窗")

        except Exception as e:
            self.log_message.emit(f"⚠️ 弹窗检测过程出错: {str(e)}")
            # 即使弹窗检测失败，也不应该中断主流程

    def _check_and_repair_file(self) -> bool:
        """检查并修复损坏的Excel文件"""
        try:
            # 检查主文件是否存在
            if not os.path.exists(self.avatar_list_path):
                self.log_message.emit("❌ 主文件不存在，尝试从备份恢复...")
                return self._restore_from_backup()

            # 检查文件大小
            file_size = os.path.getsize(self.avatar_list_path)
            if file_size < 1000:  # 小于1KB可能是损坏的
                self.log_message.emit(f"⚠️ 主文件大小异常 ({file_size} 字节)，尝试从备份恢复...")
                return self._restore_from_backup()

            # 尝试读取文件验证完整性
            try:
                test_df = pd.read_excel(self.avatar_list_path, engine='openpyxl')
                if test_df.empty:
                    self.log_message.emit("⚠️ 主文件为空，尝试从备份恢复...")
                    return self._restore_from_backup()
                else:
                    self.log_message.emit(f"✅ 文件完整性检查通过，包含 {len(test_df)} 条记录")
                    return True
            except Exception as e:
                error_msg = str(e)
                if "File is not a zip file" in error_msg or "BadZipFile" in error_msg:
                    self.log_message.emit("❌ 文件损坏 (ZIP结构错误)，尝试从备份恢复...")
                else:
                    self.log_message.emit(f"❌ 文件读取失败: {error_msg}，尝试从备份恢复...")
                return self._restore_from_backup()

        except Exception as e:
            self.log_message.emit(f"❌ 文件检查过程出错: {str(e)}")
            return False

    def _restore_from_backup(self) -> bool:
        """从备份文件恢复主文件"""
        try:
            backup_path = os.path.join(self.data_dir, "avater_list备份.xlsx")

            if not os.path.exists(backup_path):
                self.log_message.emit("❌ 备份文件不存在，无法恢复")
                return False

            # 检查备份文件大小
            backup_size = os.path.getsize(backup_path)
            if backup_size < 1000:
                self.log_message.emit(f"❌ 备份文件也损坏 ({backup_size} 字节)")
                return False

            # 验证备份文件完整性
            try:
                test_df = pd.read_excel(backup_path, engine='openpyxl')
                if test_df.empty:
                    self.log_message.emit("❌ 备份文件为空")
                    return False
            except Exception as e:
                self.log_message.emit(f"❌ 备份文件也损坏: {str(e)}")
                return False

            # 复制备份文件到主文件
            import shutil
            shutil.copy2(backup_path, self.avatar_list_path)

            # 验证恢复结果
            restored_size = os.path.getsize(self.avatar_list_path)
            self.log_message.emit(f"✅ 已从备份恢复主文件 ({restored_size} 字节)")

            # 清除缓存，强制重新读取
            self._data_cache = None
            self._cache_timestamp = None
            self._file_last_modified = None

            return True

        except Exception as e:
            self.log_message.emit(f"❌ 备份恢复失败: {str(e)}")
            return False

    def _get_cached_data(self, force_check=False) -> pd.DataFrame:
        """获取缓存的数据，如果缓存无效则重新读取

        Args:
            force_check: 是否强制进行文件完整性检查（默认False，仅在必要时检查）
        """
        try:
            # 只在强制检查或首次读取时进行文件完整性检查
            if force_check or self._data_cache is None:
                if not self._check_and_repair_file():
                    self.log_message.emit("❌ 无法修复数据文件")
                    return pd.DataFrame()

            # 检查文件是否被修改
            current_modified = os.path.getmtime(self.avatar_list_path)

            # 如果缓存有效，直接返回
            if (self._data_cache is not None and
                self._file_last_modified is not None and
                current_modified == self._file_last_modified):
                return self._data_cache.copy()

            # 缓存无效，重新读取
            # self.log_message.emit("📊 缓存无效，重新读取数据文件...")
            df = pd.read_excel(self.avatar_list_path, engine='openpyxl')

            # 检查并处理重复ID
            if 'ID' in df.columns:
                original_count = len(df)
                # 检查是否有重复ID
                duplicate_ids = df[df['ID'].duplicated(keep=False)]['ID'].unique()
                if len(duplicate_ids) > 0:
                    self.log_message.emit(f"⚠️ 发现 {len(duplicate_ids)} 个重复ID: {list(duplicate_ids)[:5]}{'...' if len(duplicate_ids) > 5 else ''}")

                    # 按更新日期降序排序，保留最新的记录
                    if '更新日期' in df.columns:
                        try:
                            # 尝试使用新版本pandas的参数
                            df = df.sort_values('更新日期', ascending=False, na_position='last')
                        except TypeError:
                            # 兼容旧版本pandas
                            df = df.sort_values('更新日期', ascending=False, na_last=True)
                        df = df.drop_duplicates(subset=['ID'], keep='first')
                        self.log_message.emit(f"🔄 已去重（保留最新记录）: {original_count} -> {len(df)} 条记录")
                    else:
                        df = df.drop_duplicates(subset=['ID'], keep='first')
                        self.log_message.emit(f"🔄 已去重: {original_count} -> {len(df)} 条记录")

                    # 保存去重后的数据
                    try:
                        df.to_excel(self.avatar_list_path, index=False)
                        self.log_message.emit("💾 去重后的数据已保存到文件")
                    except Exception as save_error:
                        self.log_message.emit(f"⚠️ 保存去重数据失败: {str(save_error)}")

            # 更新缓存
            self._data_cache = df.copy()
            self._file_last_modified = current_modified
            self._cache_timestamp = time.time()

            # 简化日志
            # self.log_message.emit(f"📊 数据缓存已更新，总计 {len(df)} 条记录")
            return df

        except Exception as e:
            self.log_message.emit(f"❌ 读取数据文件失败: {str(e)}")
            # 如果读取失败，尝试最后一次修复
            if "File is not a zip file" in str(e):
                self.log_message.emit("🔧 检测到ZIP文件错误，尝试强制修复...")
                if self._restore_from_backup():
                    try:
                        df = pd.read_excel(self.avatar_list_path, engine='openpyxl')
                        self._data_cache = df.copy()
                        self._file_last_modified = os.path.getmtime(self.avatar_list_path)
                        self._cache_timestamp = time.time()
                        self.log_message.emit(f"✅ 修复后读取成功，共 {len(df)} 条记录")
                        return df
                    except Exception as e2:
                        self.log_message.emit(f"❌ 修复后仍无法读取: {str(e2)}")
            return pd.DataFrame()

    def update_record_field(self, record_id: str, field_name: str, new_value: str) -> bool:
        """更新单个记录的字段值 - 优化版本"""
        try:
            # 使用缓存数据，保存操作不需要文件完整性检查
            df = self._get_cached_data(force_check=False)
            if df.empty:
                self.log_message.emit("❌ 无法读取数据文件")
                return False

            # 查找对应的记录
            mask = df['ID'].astype(str) == str(record_id)
            if not mask.any():
                self.log_message.emit(f"❌ 未找到ID为 {record_id} 的记录")
                return False

            # 更新数据
            df.loc[mask, field_name] = new_value

            # 保存文件（使用优化的写入方式）
            df.to_excel(self.avatar_list_path, index=False, engine='openpyxl')

            # 更新缓存
            self._data_cache = df.copy()
            self._file_last_modified = os.path.getmtime(self.avatar_list_path)

            return True

        except Exception as e:
            self.log_message.emit(f"❌ 更新记录失败: {str(e)}")
            return False

    def delete_record(self, record_id: str) -> bool:
        """删除指定ID的记录 - 优化版本"""
        try:
            # 使用缓存数据，删除操作不需要文件完整性检查
            df = self._get_cached_data(force_check=False)
            if df.empty:
                self.log_message.emit("❌ 无法读取数据文件")
                return False

            # 查找并删除对应的记录
            mask = df['ID'].astype(str) == str(record_id)
            if not mask.any():
                self.log_message.emit(f"❌ 未找到ID为 {record_id} 的记录")
                return False

            # 删除记录
            df = df[~mask]

            # 保存文件（使用优化的写入方式）
            df.to_excel(self.avatar_list_path, index=False, engine='openpyxl')

            # 更新缓存
            self._data_cache = df.copy()
            self._file_last_modified = os.path.getmtime(self.avatar_list_path)

            self.log_message.emit(f"✅ 已删除记录: ID {record_id}")
            return True

        except Exception as e:
            self.log_message.emit(f"❌ 删除记录失败: {str(e)}")
            return False

    async def download_material_data(self) -> bool:
        """从网站下载素材数据"""
        if not PLAYWRIGHT_AVAILABLE:
            self.log_message.emit("❌ Playwright未安装，无法下载素材数据")
            return False

        # 检查系统状态
        self.log_message.emit("🔍 检查系统状态...")
        ready, status = self.check_system_readiness()
        if not ready:
            self.log_message.emit(f"⚠️ 系统未准备好: {status}")

            # 如果是锁屏状态，尝试等待解锁
            if "锁屏" in status:
                self.log_message.emit("🔒 检测到系统锁屏，等待解锁...")
                if not self.wait_for_system_ready(max_wait_minutes=30):
                    self.log_message.emit("❌ 等待系统准备超时，任务取消")
                    return False
            else:
                self.log_message.emit("❌ 系统状态不适合执行任务，建议稍后重试")
                return False
        
        try:
            self.log_message.emit("🚀 开始下载素材数据...")

            # 检查调试端口浏览器
            self.log_message.emit(f"🔍 检查Chrome调试端口 {self.debug_port} 是否可用...")
            if not self.check_debug_browser():
                self.log_message.emit("🔧 调试端口浏览器未运行，正在自动启动...")
                self.log_message.emit("💡 程序将自动打开Chrome浏览器窗口")

                if not self.start_debug_browser():
                    self.log_message.emit("❌ 启动调试浏览器失败")
                    return False
                else:
                    # 等待浏览器启动并检查连接，增加等待时间
                    import time
                    self.log_message.emit("⏳ 等待Chrome调试端口启动...")
                    self.log_message.emit("📋 程序将自动处理，无需手动操作")

                    # 大幅减少等待时间，快速跳过到Playwright连接
                    max_wait_attempts = 10  # 减少到10秒
                    self.log_message.emit(f"⏳ 快速检查调试端口 {self.debug_port}...")

                    for i in range(max_wait_attempts):
                        time.sleep(1)
                        if self.check_debug_browser():
                            self.log_message.emit(f"✅ Chrome调试端口启动成功 (等待{i+1}秒)")
                            break
                        if i == 2:
                            self.log_message.emit("💡 Chrome正在启动调试端口...")
                        elif i == 5:
                            self.log_message.emit("⏳ 即将跳过检查，直接尝试连接...")
                    else:
                        self.log_message.emit("⚠️ 跳过调试端口检查，直接尝试Playwright连接")
                        self.log_message.emit(f"💡 Chrome窗口应该已经打开，程序将继续执行")
            else:
                self.log_message.emit("✅ 检测到调试端口浏览器正在运行")

            async with async_playwright() as p:
                # 多次尝试连接到调试端口的浏览器
                browser = None
                max_connection_attempts = 5
                
                for attempt in range(max_connection_attempts):
                    try:
                        self.log_message.emit(f"🔗 尝试连接到调试端口 {self.debug_port}... (第{attempt+1}次)")
                        # 使用IPv4地址避免IPv6连接问题
                        browser = await p.chromium.connect_over_cdp(f"http://127.0.0.1:{self.debug_port}")
                        self.log_message.emit("✅ 成功连接到现有浏览器实例")
                        break
                    except Exception as e:
                        self.log_message.emit(f"⚠️ 连接调试端口失败 (第{attempt+1}次): {str(e)}")
                        if attempt < max_connection_attempts - 1:
                            wait_time = 5 if attempt > 2 else 3  # 后面的重试等待更长时间
                            self.log_message.emit(f"⏳ 等待{wait_time}秒后重试...")
                            await asyncio.sleep(wait_time)
                        else:
                            self.log_message.emit("🚀 启动新的浏览器实例...")
                            # 如果连接失败，启动新的浏览器实例（使用配置的无头模式）
                            browser_args = [
                                f"--remote-debugging-port={self.debug_port}",
                                "--disable-web-security",
                                "--disable-features=VizDisplayCompositor",
                                # 添加稳定性参数
                                "--no-sandbox",
                                "--disable-dev-shm-usage",
                                "--disable-background-timer-throttling",
                                "--disable-backgrounding-occluded-windows",
                                "--disable-renderer-backgrounding",
                                "--disable-features=TranslateUI",
                                "--disable-ipc-flooding-protection",
                                "--disable-hang-monitor",
                                "--disable-prompt-on-repost",
                                "--disable-domain-reliability",
                                "--disable-component-extensions-with-background-pages"
                            ]

                            # 根据无头模式添加额外参数
                            if self.headless_mode:
                                browser_args.extend([
                                    "--disable-gpu",
                                    "--window-size=1920,1080",
                                    "--virtual-time-budget=5000"  # 虚拟时间预算，提高稳定性
                                ])
                            else:
                                browser_args.extend([
                                    "--start-maximized",
                                    "--disable-infobars"
                                ])

                            browser = await p.chromium.launch(
                                headless=self.headless_mode,
                                args=browser_args,
                                slow_mo=100 if not self.headless_mode else 0  # 非无头模式时减慢操作
                            )
                
                if not browser:
                    self.log_message.emit("❌ 无法连接或启动浏览器")
                    return False

                # 获取或创建页面
                contexts = browser.contexts
                if contexts:
                    # 使用现有的上下文
                    context = contexts[0]
                    pages = context.pages
                    if pages:
                        page = pages[0]
                        self.log_message.emit("📄 使用现有页面")

                        # 检查当前页面URL
                        current_url = page.url
                        self.log_message.emit(f"📄 当前页面URL: {current_url}")

                        # 如果不在目标页面，则导航
                        if self.website_url not in current_url:
                            self.log_message.emit(f"📱 导航到目标页面: {self.website_url}")
                            
                            # 改进的导航重试机制
                            navigation_success = False
                            max_nav_attempts = 3
                            
                            for nav_attempt in range(max_nav_attempts):
                                try:
                                    await page.goto(self.website_url, wait_until="domcontentloaded", timeout=30000)
                                    self.log_message.emit("✅ 页面导航成功")
                                    navigation_success = True
                                    break
                                except Exception as nav_error:
                                    self.log_message.emit(f"⚠️ 页面导航失败 (第{nav_attempt+1}次): {str(nav_error)}")
                                    
                                    if nav_attempt < max_nav_attempts - 1:
                                        self.log_message.emit(f"🔄 {2}秒后重试导航...")
                                        await asyncio.sleep(2)
                                    else:
                                        self.log_message.emit("🔄 尝试创建新页面...")
                            
                            # 如果导航失败，尝试创建新页面
                            if not navigation_success:
                                try:
                                    page = await context.new_page()
                                    await page.goto(self.website_url, wait_until="domcontentloaded", timeout=30000)
                                    self.log_message.emit("✅ 在新页面中成功打开目标网站")
                                except Exception as e2:
                                    self.log_message.emit(f"❌ 新页面导航也失败: {str(e2)}")
                                    return False
                        else:
                            self.log_message.emit("✅ 已在目标页面，无需导航")
                    else:
                        page = await context.new_page()
                        self.log_message.emit("📄 在现有上下文中创建新页面")
                        await page.goto(self.website_url, wait_until="domcontentloaded", timeout=30000)
                else:
                    # 创建新的上下文和页面
                    context = await browser.new_context()
                    page = await context.new_page()
                    self.log_message.emit("📄 创建新的上下文和页面")
                    await page.goto(self.website_url, wait_until="domcontentloaded", timeout=30000)

                # 等待页面稳定并验证页面可用性
                await page.wait_for_timeout(3000)

                # 验证页面是否真正可用
                try:
                    # 检查页面是否已关闭
                    if page.is_closed():
                        self.log_message.emit("❌ 页面已关闭，无法继续操作")
                        return False

                    # 检查页面URL是否正确
                    current_url = page.url
                    self.log_message.emit(f"📄 当前页面URL: {current_url}")

                    if "chrome-extension://" in current_url or "about:" in current_url:
                        self.log_message.emit(f"⚠️ 页面URL异常: {current_url}，尝试重新导航")
                        await page.goto(self.website_url, wait_until="domcontentloaded", timeout=30000)
                        await page.wait_for_timeout(2000)
                        current_url = page.url

                    # 检查是否在登录页面
                    if "uuap.baidu.com" in current_url or "authorize" in current_url:
                        self.log_message.emit("🔐 检测到登录页面，需要手动登录")
                        self.log_message.emit("💡 请在打开的Chrome窗口中完成登录，然后程序将自动继续")
                        self.log_message.emit("⏳ 等待登录完成...")

                        # 等待登录完成（检查URL变化）
                        max_login_wait = 60  # 最多等待60秒
                        for i in range(max_login_wait):
                            await page.wait_for_timeout(1000)
                            current_url = page.url

                            # 检查是否已经跳转到目标页面
                            if self.website_url.split('?')[0] in current_url:
                                self.log_message.emit("✅ 登录成功，已跳转到目标页面")
                                break
                            elif "uuap.baidu.com" not in current_url and "authorize" not in current_url:
                                self.log_message.emit("✅ 已离开登录页面")
                                # 尝试导航到目标页面
                                await page.goto(self.website_url, wait_until="domcontentloaded", timeout=30000)
                                break

                            if i % 10 == 0 and i > 0:
                                self.log_message.emit(f"⏳ 仍在等待登录... ({i}/{max_login_wait}秒)")
                        else:
                            self.log_message.emit("⚠️ 登录等待超时，但程序将继续尝试")

                    self.log_message.emit("✅ 页面加载完成")
                except Exception as e:
                    self.log_message.emit(f"❌ 页面验证失败: {str(e)}")
                    return False

                # 检测并关闭可能存在的弹窗
                await self.close_popup_if_exists(page)

                # 1. 点击素材创建日期的选择框
                self.log_message.emit("📅 设置素材创建日期...")
                date_selector = ".el-date-editor.el-range-editor"

                try:
                    # 等待元素出现并确保可点击
                    await page.wait_for_selector(date_selector, timeout=10000, state="visible")
                    await page.click(date_selector)
                    await page.wait_for_timeout(1000)
                except Exception as e:
                    self.log_message.emit(f"❌ 点击日期选择框失败: {str(e)}")
                    # 即使出错也要清理调试进程
                    self.kill_debug_chrome_processes()
                    return False
                
                # 选择"最近一年"选项
                try:
                    # 查找并点击"最近一年"选项
                    recent_year_option = await page.wait_for_selector("text=最近一年", timeout=5000)
                    await recent_year_option.click()
                    self.log_message.emit("✅ 已选择最近一年")
                    await page.wait_for_timeout(1000)
                except Exception as e:
                    self.log_message.emit(f"⚠️ 选择最近一年失败: {str(e)}")
                
                # 2. 点击右侧的下拉选择框（剪辑素材库 -> 拍摄素材库）
                self.log_message.emit("📂 设置素材库类型...")
                try:
                    # 查找第二个下拉框
                    dropdown_selectors = await page.query_selector_all(".el-select")
                    if len(dropdown_selectors) >= 2:
                        await dropdown_selectors[1].click()
                        await page.wait_for_timeout(1000)
                        
                        # 选择"拍摄素材库"
                        shooting_option = await page.wait_for_selector("text=拍摄素材库", timeout=5000)
                        await shooting_option.click()
                        self.log_message.emit("✅ 已选择拍摄素材库")
                        await page.wait_for_timeout(1000)
                    else:
                        self.log_message.emit("⚠️ 未找到素材库选择框")
                except Exception as e:
                    self.log_message.emit(f"⚠️ 设置素材库类型失败: {str(e)}")

                # 在设置完成后检测弹窗
                await self.close_popup_if_exists(page)

                # 3. 点击导出按钮
                self.log_message.emit("📤 开始导出数据...")

                # 在导出前再次检测弹窗
                await self.close_popup_if_exists(page)

                try:
                    export_button = await page.wait_for_selector(".el-button.el-button--primary", timeout=5000)
                    
                    # 设置下载监听
                    download_path = None
                    
                    async def handle_download(download):
                        nonlocal download_path
                        # 保存到临时目录
                        download_path = os.path.join(self.temp_dir, f"material_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
                        self.log_message.emit(f"📥 开始下载文件到: {download_path}")
                        await download.save_as(download_path)
                        self.log_message.emit(f"✅ 文件下载完成: {download_path}")
                        # 记录当前下载文件路径供清理使用
                        self._current_download_file = download_path
                    
                    page.on("download", handle_download)
                    
                    # 点击导出按钮
                    await export_button.click()
                    self.log_message.emit("✅ 已点击导出按钮，等待下载...")
                    
                    # 优化的下载等待逻辑 - 缩短超时时间，增加进程监控
                    download_timeout = 30  # 缩短最大等待时间到30秒
                    check_interval = 2  # 每2秒检查一次
                    elapsed_time = 0
                    browser_alive = True

                    while elapsed_time < download_timeout and browser_alive:
                        try:
                            # 检查浏览器进程是否还活着
                            if browser.is_connected():
                                await page.wait_for_timeout(check_interval * 1000)
                            else:
                                self.log_message.emit("❌ 浏览器连接已断开")
                                browser_alive = False
                                break
                        except Exception as e:
                            self.log_message.emit(f"❌ 等待过程中出错: {str(e)}")
                            browser_alive = False
                            break

                        elapsed_time += check_interval

                        # 检查下载文件是否存在
                        if download_path and os.path.exists(download_path):
                            # 检查文件大小是否稳定（确保下载完成）
                            file_size = os.path.getsize(download_path)
                            if file_size > 0:
                                self.log_message.emit(f"📁 检测到下载文件，大小: {file_size} 字节")
                                # 再等待2秒确保文件写入完成
                                try:
                                    await page.wait_for_timeout(2000)
                                except:
                                    pass  # 如果页面已关闭，忽略错误
                                break

                        # 每5秒提供一次进度反馈
                        if elapsed_time % 5 == 0:
                            self.log_message.emit(f"⏳ 等待下载中... ({elapsed_time}/{download_timeout}秒)")

                    if not browser_alive:
                        self.log_message.emit("❌ 浏览器进程意外关闭，可能是锁屏导致")
                    elif elapsed_time >= download_timeout:
                        self.log_message.emit(f"⏰ 下载等待超时 ({download_timeout}秒)")
                    
                    await browser.close()
                    
                    # 自动清理Chrome调试进程
                    self.cleanup_chrome_processes()
                    
                    if download_path and os.path.exists(download_path):
                        final_size = os.path.getsize(download_path)
                        self.log_message.emit(f"✅ 素材数据下载成功，文件大小: {final_size} 字节")
                        return await self.process_downloaded_data(download_path)
                    else:
                        self.log_message.emit("❌ 下载失败，未找到下载文件或文件为空")
                        self.log_message.emit(f"📋 下载路径: {download_path}")
                        self.log_message.emit(f"📋 文件存在: {os.path.exists(download_path) if download_path else False}")
                        return False
                        
                except Exception as e:
                    self.log_message.emit(f"❌ 导出失败: {str(e)}")
                    # 确保清理浏览器和调试进程
                    try:
                        await browser.close()
                    except:
                        pass
                    self.kill_debug_chrome_processes()
                    return False

        except Exception as e:
            self.log_message.emit(f"❌ 下载素材数据失败: {str(e)}")
            # 即使出错也要清理调试进程
            self.kill_debug_chrome_processes()
            return False
        finally:
            # 确保最终清理调试进程
            self.kill_debug_chrome_processes()
            self.log_message.emit("🧹 调试浏览器进程清理完成")
    
    async def process_downloaded_data(self, downloaded_file_path: str) -> bool:
        """处理下载的数据文件"""
        try:
            self.log_message.emit("🔄 开始处理下载的数据...")
            
            # 读取下载的文件
            new_df = pd.read_excel(downloaded_file_path)
            self.log_message.emit(f"📊 下载文件包含 {len(new_df)} 条记录")
            
            # 根据A列（素材ID）进行去重
            if '素材ID' in new_df.columns:
                original_count = len(new_df)
                new_df = new_df.drop_duplicates(subset=['素材ID'], keep='first')
                dedup_count = len(new_df)
                self.log_message.emit(f"🔄 去重处理: {original_count} -> {dedup_count} 条记录")
            else:
                self.log_message.emit("⚠️ 未找到素材ID列，跳过去重")
            
            # 处理现有的avatar_list.xlsx文件
            return await self.merge_with_existing_data(new_df)
            
        except Exception as e:
            self.log_message.emit(f"❌ 处理下载数据失败: {str(e)}")
            return False
    
    async def merge_with_existing_data(self, new_df: pd.DataFrame) -> bool:
        """与现有数据合并"""
        try:
            self.log_message.emit("🔄 开始与现有数据合并...")
            
            # 读取现有数据
            if os.path.exists(self.avatar_list_path):
                existing_df = pd.read_excel(self.avatar_list_path)
                self.log_message.emit(f"📊 现有数据包含 {len(existing_df)} 条记录")
            else:
                existing_df = pd.DataFrame()
                self.log_message.emit("📊 创建新的数据文件")
            
            # 列名映射
            column_mapping = {
                '素材ID': 'ID',
                '外链BOS地址': '视频URL'
            }
            
            # 重命名新数据的列
            new_df_renamed = new_df.rename(columns=column_mapping)
            
            # 添加默认列
            if '是否上传飞影' not in new_df_renamed.columns:
                new_df_renamed['是否上传飞影'] = ''
            
            # 添加更新日期
            new_df_renamed['更新日期'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 如果现有数据为空，直接使用新数据
            if existing_df.empty:
                result_df = new_df_renamed
                new_count = len(result_df)
            else:
                # 找出新增的记录（ID不存在于现有数据中）
                if 'ID' in existing_df.columns and 'ID' in new_df_renamed.columns:
                    existing_ids = set(existing_df['ID'].astype(str))
                    new_records = new_df_renamed[~new_df_renamed['ID'].astype(str).isin(existing_ids)]
                    new_count = len(new_records)
                    
                    if new_count > 0:
                        # 将新记录添加到现有数据的最上面
                        result_df = pd.concat([new_records, existing_df], ignore_index=True)
                        self.log_message.emit(f"✅ 新增 {new_count} 条记录")
                    else:
                        result_df = existing_df
                        self.log_message.emit("ℹ️ 没有新增记录")
                else:
                    self.log_message.emit("⚠️ 缺少ID列，无法进行增量更新")
                    return False
            
            # 对最终数据进行去重处理
            if 'ID' in result_df.columns:
                original_count = len(result_df)
                # 按ID去重，保留最新的记录（如果有更新日期的话）
                if '更新日期' in result_df.columns:
                    # 按更新日期降序排序，然后去重，这样保留的是最新的记录
                    try:
                        # 尝试使用新版本pandas的参数
                        result_df = result_df.sort_values('更新日期', ascending=False, na_position='last')
                    except TypeError:
                        # 兼容旧版本pandas
                        result_df = result_df.sort_values('更新日期', ascending=False, na_last=True)
                    result_df = result_df.drop_duplicates(subset=['ID'], keep='first')
                    self.log_message.emit(f"🔄 按ID去重（保留最新记录）: {original_count} -> {len(result_df)} 条记录")
                else:
                    # 没有更新日期，直接去重
                    result_df = result_df.drop_duplicates(subset=['ID'], keep='first')
                    self.log_message.emit(f"🔄 按ID去重: {original_count} -> {len(result_df)} 条记录")

            # 保存更新后的数据
            result_df.to_excel(self.avatar_list_path, index=False)
            self.log_message.emit(f"💾 数据已保存，总计 {len(result_df)} 条记录")
            
            # 清理临时文件 - 修复变量名错误
            self._cleanup_temp_file()

            # 素材更新完成后，清理Chrome调试进程，释放资源供用户正常使用Chrome
            self.log_message.emit("🧹 素材更新完成，开始清理Chrome调试进程...")
            self.cleanup_chrome_processes()

            self.update_completed.emit(True, f"素材更新完成，新增 {new_count} 条记录")
            return True
            
        except Exception as e:
            self.log_message.emit(f"❌ 数据合并失败: {str(e)}")

            # 即使失败也要清理Chrome调试进程
            self.log_message.emit("🧹 清理Chrome调试进程...")
            self.cleanup_chrome_processes()

            self.update_completed.emit(False, f"数据合并失败: {str(e)}")
            return False
    
    def _cleanup_temp_file(self):
        """清理临时文件"""
        try:
            # 清理下载的临时文件
            temp_files = []
            if hasattr(self, '_current_download_file') and self._current_download_file:
                temp_files.append(self._current_download_file)
            
            # 清理所有临时Excel文件
            import glob
            temp_pattern = os.path.join(self.temp_dir, "material_data_*.xlsx")
            temp_files.extend(glob.glob(temp_pattern))
            
            cleaned_count = 0
            for temp_file in temp_files:
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        cleaned_count += 1
                        self.log_message.emit(f"🗑️ 已清理临时文件: {os.path.basename(temp_file)}")
                except Exception as e:
                    self.log_message.emit(f"⚠️ 清理文件失败 {os.path.basename(temp_file)}: {str(e)}")
            
            if cleaned_count == 0:
                self.log_message.emit("📋 无需清理的临时文件")
            else:
                self.log_message.emit(f"✅ 已清理 {cleaned_count} 个临时文件")
                
        except Exception as e:
            self.log_message.emit(f"⚠️ 临时文件清理过程异常: {str(e)}")
    
    def cleanup_chrome_processes(self):
        """清理Chrome调试进程（程序结束时调用）"""
        try:
            self.log_message.emit("🧹 正在清理Chrome调试进程...")
            
            # 只关闭调试端口的Chrome进程，不影响用户的正常Chrome
            self.kill_debug_chrome_processes()
            
            # 等待进程完全关闭
            import time
            time.sleep(2)
            
            self.log_message.emit("✅ Chrome调试进程清理完成")
            
        except Exception as e:
            self.log_message.emit(f"⚠️ Chrome进程清理异常: {str(e)}")


class MaterialUpdateWorker(QThread):
    """素材更新工作线程"""
    
    def __init__(self, material_manager):
        super().__init__()
        self.material_manager = material_manager
    
    def run(self):
        """运行素材更新"""
        try:
            # 在新的事件循环中运行异步任务
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            success = loop.run_until_complete(
                self.material_manager.download_material_data()
            )
            
            loop.close()
            
        except Exception as e:
            self.material_manager.log_message.emit(f"❌ 素材更新线程异常: {str(e)}")

            # 线程异常时也要清理Chrome调试进程
            self.material_manager.log_message.emit("🧹 清理Chrome调试进程...")
            self.material_manager.cleanup_chrome_processes()

            self.material_manager.update_completed.emit(False, f"更新失败: {str(e)}")
