"""
调试定时器状态
"""

import sys
import os
sys.path.append('src')

from PySide6.QtCore import QTimer, QObject, Signal
from ui.schedule_manager import VideoManagementScheduleManager, ScheduleTask
import json


def debug_timer_status():
    """调试定时器状态"""
    print("=" * 60)
    print("🔍 调试定时器状态")
    print("=" * 60)
    
    # 创建管理器实例
    try:
        manager = VideoManagementScheduleManager()
        print("✅ VideoManagementScheduleManager 创建成功")
        
        # 检查配置文件
        config_file = "video_management_schedule_config.json"
        if os.path.exists(config_file):
            print(f"✅ 配置文件存在: {config_file}")
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"📋 配置文件内容:")
            print(json.dumps(config, indent=2, ensure_ascii=False))
            
            # 检查任务
            if config:
                print(f"\n📊 任务统计:")
                print(f"  总任务数: {len(config)}")
                
                for task_id, task_data in config.items():
                    print(f"\n🔧 任务: {task_id}")
                    print(f"  名称: {task_data.get('name', 'N/A')}")
                    print(f"  类型: {task_data.get('task_type', 'N/A')}")
                    print(f"  启用: {task_data.get('enabled', False)}")
                    print(f"  间隔: {task_data.get('interval_minutes', 0)} 分钟")
                    print(f"  开始时间: {task_data.get('start_time', 'N/A')}")
                    
                    # 检查管理器中的任务
                    if task_id in manager.tasks:
                        task = manager.tasks[task_id]
                        print(f"  ✅ 任务已加载到管理器")
                        print(f"  任务对象类型: {type(task)}")
                        print(f"  任务启用状态: {task.enabled}")
                        print(f"  任务类型: {task.task_type}")
                        print(f"  间隔分钟: {task.interval_minutes}")
                        
                        # 检查定时器状态
                        if task_id in manager.internal_timers:
                            timer = manager.internal_timers[task_id]
                            print(f"  ✅ 定时器已创建")
                            print(f"  定时器类型: {type(timer)}")
                            print(f"  定时器活跃: {timer.isActive()}")
                            print(f"  定时器间隔: {timer.interval()} 毫秒")
                            print(f"  定时器剩余时间: {timer.remainingTime()} 毫秒")
                        else:
                            print(f"  ❌ 定时器未创建")
                            
                            # 尝试手动启动定时器
                            if task.task_type == "internal" and task.enabled:
                                print(f"  🔧 尝试手动启动定时器...")
                                try:
                                    manager.start_internal_timer(task)
                                    if task_id in manager.internal_timers:
                                        timer = manager.internal_timers[task_id]
                                        print(f"  ✅ 定时器启动成功")
                                        print(f"  定时器活跃: {timer.isActive()}")
                                        print(f"  定时器间隔: {timer.interval()} 毫秒")
                                    else:
                                        print(f"  ❌ 定时器启动失败")
                                except Exception as e:
                                    print(f"  ❌ 启动定时器异常: {e}")
                    else:
                        print(f"  ❌ 任务未加载到管理器")
            else:
                print("⚠️ 配置文件为空")
        else:
            print(f"❌ 配置文件不存在: {config_file}")
            
    except Exception as e:
        print(f"❌ 创建管理器失败: {e}")
        import traceback
        traceback.print_exc()


def check_timer_creation_logic():
    """检查定时器创建逻辑"""
    print("\n" + "=" * 40)
    print("🔧 检查定时器创建逻辑")
    print("=" * 40)
    
    print("定时器创建的条件:")
    print("1. task.task_type == 'internal'")
    print("2. task.enabled == True")
    print("3. task.interval_minutes > 0")
    print("")
    
    print("可能的问题:")
    print("1. 任务类型不是 'internal'")
    print("2. 任务未启用 (enabled=False)")
    print("3. 间隔时间为0或负数")
    print("4. VideoManagementScheduleManager 没有正确继承父类方法")
    print("5. 信号连接有问题")
    print("")
    
    print("调试建议:")
    print("1. 检查任务创建时的参数")
    print("2. 检查 start_internal_timer 方法是否被调用")
    print("3. 检查 QTimer 是否正确创建和启动")
    print("4. 检查信号连接是否正确")


def test_simple_timer():
    """测试简单定时器"""
    print("\n" + "=" * 40)
    print("⏰ 测试简单定时器")
    print("=" * 40)
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # 确保有QApplication实例
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建简单定时器测试
        timer = QTimer()
        
        def test_timeout():
            print(f"⏰ 定时器触发: {timer.interval()} 毫秒")
        
        timer.timeout.connect(test_timeout)
        timer.start(5000)  # 5秒
        
        print(f"✅ 测试定时器创建成功")
        print(f"定时器活跃: {timer.isActive()}")
        print(f"定时器间隔: {timer.interval()} 毫秒")
        print(f"定时器剩余时间: {timer.remainingTime()} 毫秒")
        
        # 停止定时器
        timer.stop()
        print(f"🛑 定时器已停止")
        
    except Exception as e:
        print(f"❌ 测试定时器失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_timer_status()
    check_timer_creation_logic()
    test_simple_timer()
