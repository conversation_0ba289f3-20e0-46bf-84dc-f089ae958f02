[2025-08-06 07:22:01] 日志文件: d:\project\guangliu02\feiyingshuziren\log\2025-08-06\video_management_controller_07-22-01.log
[2025-08-06 07:22:01] 视频管理自动化控制器启动
[2025-08-06 07:22:01] 任务类型: full_process
[2025-08-06 07:22:01] 无头模式: False
[2025-08-06 07:22:01] 开始时间: 2025-08-06 07:22:01
[2025-08-06 07:22:01] 控制器初始化完成
[2025-08-06 07:22:01] 任务类型: full_process
[2025-08-06 07:22:01] 无头模式: False
[2025-08-06 07:22:01] 工作目录: d:\project\guangliu02
[2025-08-06 07:22:01] 源码目录: d:\project\guangliu02\src
[2025-08-06 07:22:01] [CONTROLLER] 将执行 3 个任务: material, upload, rename
[2025-08-06 07:22:01] 
[CONTROLLER] 第1步：素材更新
[2025-08-06 07:22:01] ============================================================
[2025-08-06 07:22:01] [MATERIAL] 开始执行素材更新任务
[2025-08-06 07:22:01] ============================================================
[2025-08-06 07:22:02] [MATERIAL] 开始下载素材数据...
[2025-08-06 07:22:02] [MATERIAL] 🚀 开始下载素材数据...
[2025-08-06 07:22:02] [MATERIAL] 🔍 检查Chrome调试端口 9222 是否可用...
[2025-08-06 07:22:05] [MATERIAL] 🔧 调试端口浏览器未运行，正在自动启动...
[2025-08-06 07:22:05] [MATERIAL] 💡 程序将自动打开Chrome浏览器窗口
[2025-08-06 07:22:07] [MATERIAL] 🔄 未检测到Chrome进程，直接启动
[2025-08-06 07:22:07] [MATERIAL] 🔄 使用默认Chrome配置启动调试实例
[2025-08-06 07:22:07] [MATERIAL] 🔄 确保调试端口可用...
[2025-08-06 07:22:07] [MATERIAL] ✅ Chrome调试目录已创建: C:\Users\<USER>\Program Files\Google\Chrome\Application\chrome.exe --remote-debugging-port=9222 --remote-debugging-address=0.0.0.0 --user-data-dir=C:\Users\<USER>