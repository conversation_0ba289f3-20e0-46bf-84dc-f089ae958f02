"""
测试集成后的重命名功能
"""

import os
import sys
import pandas as pd

# 添加src路径
sys.path.append('src')

def test_integration():
    """测试集成功能"""
    print("=" * 60)
    print("🧪 测试集成后的重命名功能")
    print("=" * 60)
    
    # 1. 检查重命名模块是否正确导入
    try:
        from core.hifly_rename_automation import batch_rename_videos, HiflyRenameAutomation
        print("✅ 重命名模块导入成功")
    except ImportError as e:
        print(f"❌ 重命名模块导入失败: {e}")
        return
    
    # 2. 检查认证文件
    auth_file = "feiyingshuziren/essential_auth_data.json"
    if os.path.exists(auth_file):
        print("✅ 认证文件存在")
    else:
        print("❌ 认证文件不存在")
        return
    
    # 3. 检查数据文件
    avatar_list_path = "data/avatar_list.xlsx"
    if os.path.exists(avatar_list_path):
        print("✅ 数据文件存在")
        
        # 检查是否有"是否重命名"列
        try:
            df = pd.read_excel(avatar_list_path)
            if "是否重命名" in df.columns:
                print("✅ '是否重命名'列已存在")
            else:
                print("⚠️ '是否重命名'列不存在，将自动添加")
                df["是否重命名"] = ""
                df.to_excel(avatar_list_path, index=False)
                print("✅ 已添加'是否重命名'列")
            
            # 检查需要重命名的数据
            if "是否上传飞影" in df.columns:
                mask = (df["是否上传飞影"] == "是") & (df["是否重命名"] != "是")
                need_rename_count = mask.sum()
                print(f"📊 需要重命名的视频数量: {need_rename_count}")
                
                if need_rename_count > 0:
                    print("📋 需要重命名的视频:")
                    need_rename_df = df[mask]
                    for _, row in need_rename_df.head(5).iterrows():  # 只显示前5个
                        actor_name = str(row.get("拍摄演员名称", "")).strip()
                        video_id = str(row.get("ID", "")).strip()
                        if actor_name and video_id:
                            print(f"  - {actor_name}-{video_id}-演员")
            else:
                print("⚠️ '是否上传飞影'列不存在")
                
        except Exception as e:
            print(f"❌ 检查数据文件失败: {e}")
    else:
        print("❌ 数据文件不存在")
        return
    
    # 4. 测试重命名自动化类初始化
    try:
        automation = HiflyRenameAutomation(auth_file, avatar_list_path)
        if automation.load_auth_data():
            print("✅ 重命名自动化初始化成功")
        else:
            print("❌ 认证数据加载失败")
    except Exception as e:
        print(f"❌ 重命名自动化初始化失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 集成测试完成")
    print("=" * 60)
    print("\n📋 下一步操作:")
    print("1. 运行主程序: python src/main.py")
    print("2. 在视频管理界面点击'飞影上传'")
    print("3. 上传完成后选择'是'进行自动重命名")
    print("4. 或者直接点击'自动重命名'按钮")


if __name__ == "__main__":
    test_integration()
