#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试搜索优化和ID排序功能
"""

import os
import sys
import time

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_file_integrity():
    """测试文件完整性检查和修复功能"""
    print("=== 测试文件完整性检查 ===")
    
    try:
        from core.config_manager import ConfigManager
        from core.video_material_manager import VideoMaterialManager
        
        # 创建视频素材管理器
        config_manager = ConfigManager()
        video_manager = VideoMaterialManager(config_manager)
        
        print("✅ 视频素材管理器创建成功")
        
        # 检查文件完整性检查方法
        if hasattr(video_manager, '_check_and_repair_file'):
            print("✅ 文件完整性检查方法存在")
            
            # 测试文件检查
            result = video_manager._check_and_repair_file()
            if result:
                print("✅ 文件完整性检查通过")
            else:
                print("⚠️ 文件需要修复")
        else:
            print("❌ 文件完整性检查方法不存在")
            return False
        
        # 检查备份恢复方法
        if hasattr(video_manager, '_restore_from_backup'):
            print("✅ 备份恢复方法存在")
        else:
            print("❌ 备份恢复方法不存在")
            return False
        
        # 测试数据读取
        df = video_manager._get_cached_data()
        if not df.empty:
            print(f"✅ 数据读取成功，包含 {len(df)} 条记录")
        else:
            print("❌ 数据读取失败或为空")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 文件完整性测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_search_optimization():
    """测试搜索优化"""
    print("\n=== 测试搜索优化 ===")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.main_window import MainWindow
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        
        print("✅ 主窗口创建成功")
        
        # 检查搜索相关方法
        search_methods = [
            'vm_search_table_async',
            'vm_search_in_dataframe',
            'vm_search_completed',
            'on_vm_search_text_changed'
        ]
        
        missing_methods = []
        for method in search_methods:
            if hasattr(main_window, method):
                print(f"✅ 搜索方法存在: {method}")
            else:
                print(f"❌ 搜索方法不存在: {method}")
                missing_methods.append(method)
        
        if missing_methods:
            return False
        
        # 检查搜索框是否存在
        if hasattr(main_window, 'vm_search_box') and main_window.vm_search_box:
            print("✅ 搜索框存在")
        else:
            print("❌ 搜索框不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 搜索优化测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_numeric_sorting():
    """测试数值排序功能"""
    print("\n=== 测试数值排序功能 ===")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.main_window import NumericTableWidgetItem
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ 数值排序类导入成功")
        
        # 测试数值排序项
        item1 = NumericTableWidgetItem("100")
        item2 = NumericTableWidgetItem("20")
        item3 = NumericTableWidgetItem("5")
        
        # 测试比较
        if item3 < item2 < item1:
            print("✅ 数值排序比较正确 (5 < 20 < 100)")
        else:
            print("❌ 数值排序比较错误")
            return False
        
        # 测试字符串数字
        item4 = NumericTableWidgetItem("1000")
        item5 = NumericTableWidgetItem("999")
        
        if item5 < item4:
            print("✅ 字符串数字排序正确 (999 < 1000)")
        else:
            print("❌ 字符串数字排序错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数值排序测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_improvement():
    """测试性能改进"""
    print("\n=== 测试性能改进 ===")
    
    try:
        # 检查搜索优化代码
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键优化点
        optimizations = [
            "vm_search_in_dataframe",  # 数据层搜索
            "QApplication.processEvents()",  # UI响应性
            "batch_size = 200",  # 大批次处理
            "NumericTableWidgetItem",  # 数值排序
            "Qt.DescendingOrder"  # 降序排序
        ]
        
        missing_optimizations = []
        for opt in optimizations:
            if opt in content:
                print(f"✅ 优化点存在: {opt}")
            else:
                print(f"❌ 优化点缺失: {opt}")
                missing_optimizations.append(opt)
        
        if missing_optimizations:
            return False
        
        # 检查防抖机制
        if "vm_search_timer" in content and "setSingleShot(True)" in content:
            print("✅ 搜索防抖机制存在")
        else:
            print("❌ 搜索防抖机制缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 性能改进测试失败: {str(e)}")
        return False

def test_sorting_configuration():
    """测试排序配置"""
    print("\n=== 测试排序配置 ===")
    
    try:
        # 检查排序相关代码
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查排序配置
        sorting_features = [
            "setSortingEnabled(True)",  # 启用排序
            "sortItems(1, Qt.DescendingOrder)",  # ID列降序
            "NumericTableWidgetItem",  # 数值排序项
            "__lt__",  # 自定义比较方法
        ]
        
        missing_features = []
        for feature in sorting_features:
            if feature in content:
                print(f"✅ 排序功能存在: {feature}")
            else:
                print(f"❌ 排序功能缺失: {feature}")
                missing_features.append(feature)
        
        if missing_features:
            return False
        
        # 检查ID列特殊处理
        if 'original_col_name == "ID"' in content and "NumericTableWidgetItem" in content:
            print("✅ ID列数值排序配置正确")
        else:
            print("❌ ID列数值排序配置错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 排序配置测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 搜索优化和ID排序功能测试")
    print("=" * 60)
    
    # 测试文件完整性
    if not test_file_integrity():
        print("❌ 文件完整性测试失败")
        return False
    
    # 测试搜索优化
    if not test_search_optimization():
        print("❌ 搜索优化测试失败")
        return False
    
    # 测试数值排序
    if not test_numeric_sorting():
        print("❌ 数值排序测试失败")
        return False
    
    # 测试性能改进
    if not test_performance_improvement():
        print("❌ 性能改进测试失败")
        return False
    
    # 测试排序配置
    if not test_sorting_configuration():
        print("❌ 排序配置测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！")
    print("\n📋 修复总结:")
    print("✅ 搜索性能优化:")
    print("  - 使用更大的批次处理 (200行/批)")
    print("  - 添加搜索防抖机制 (500ms延迟)")
    print("  - 减少UI更新频率")
    print("  - 添加进度提示和取消机制")
    
    print("\n✅ ID列排序优化:")
    print("  - 实现数值排序而非字符串排序")
    print("  - 默认按ID降序排列 (大的在前)")
    print("  - 自定义NumericTableWidgetItem类")
    print("  - 支持点击列标题重新排序")
    
    print("\n✅ 文件完整性保障:")
    print("  - 自动检测文件损坏")
    print("  - 从备份自动恢复")
    print("  - ZIP文件错误修复")
    print("  - 完善的错误处理")
    
    print("\n🎯 解决的问题:")
    print("  - 搜索时程序卡住 → 优化为分批异步搜索")
    print("  - ID排序不正确 → 实现数值排序")
    print("  - 文件损坏问题 → 自动检测和修复")
    print("  - 用户体验差 → 添加进度提示")
    
    print("\n🚀 预期效果:")
    print("  - 搜索响应更快，不再卡顿")
    print("  - ID按数值大小正确排序")
    print("  - 文件损坏自动修复")
    print("  - 更好的用户反馈")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
