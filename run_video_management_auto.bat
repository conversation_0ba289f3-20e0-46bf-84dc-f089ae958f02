@echo off
:: 视频管理自动化任务批处理文件
:: 设置UTF-8编码支持中文
chcp 65001 > nul

echo ========================================
echo 视频管理自动化任务
echo ========================================
echo 开始时间: %date% %time%
echo.

:: 切换到脚本所在目录
cd /d "%~dp0"

:: 检查Python环境
echo 检查Python环境...
python --version > nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请确保Python已安装并添加到PATH环境变量
    pause
    exit /b 1
)

:: 检查src目录
if not exist "src" (
    echo 错误: 未找到src目录
    echo 请确保在项目根目录下运行此脚本
    pause
    exit /b 1
)

:: 检查主程序文件
if not exist "src\video_management_runner.py" (
    echo 错误: 未找到video_management_runner.py
    echo 请确保文件存在于src目录下
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

:: 执行视频管理自动化任务
echo ========================================
echo 开始执行视频管理自动化任务
echo ========================================
echo 任务流程:
echo 1. 素材更新
echo 2. 飞影上传
echo 3. 自动重命名
echo.

:: 执行Python控制器（从项目根目录）
python src/video_management_controller.py %*

:: 保存退出代码
set exit_code=%errorlevel%

::  控制器已在正确目录执行，无需切换

echo.
echo ========================================
echo 任务执行完成
echo ========================================
echo 结束时间: %date% %time%

:: 检查执行结果
if %exit_code% equ 0 (
    echo 状态: 成功
    echo 所有任务已完成，程序将自动关闭
) else (
    echo 状态: 失败 ^(错误代码: %exit_code%^)
    echo 请检查日志文件获取详细信息
)

echo.
echo 日志文件位置: feiyingshuziren\log\%date:~0,10%\
echo.

:: 如果是定时任务调用，自动退出
if "%1"=="scheduled" (
    echo 定时任务模式，自动退出...
    exit /b %exit_code%
)

:: 手动运行时暂停，让用户查看结果
echo 按任意键退出...
pause > nul
exit /b %exit_code%
