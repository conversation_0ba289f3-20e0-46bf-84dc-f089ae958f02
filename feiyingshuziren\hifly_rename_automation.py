"""
飞影数字人重命名自动化模块
用于自动删除飞影平台上传后视频名称中的多余字母
"""

import asyncio
import json
import os
import pandas as pd
import time
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError


class HiflyRenameAutomation:
    def __init__(self, auth_file_path="feiyingshuziren/essential_auth_data.json", 
                 avatar_list_path="data/avatar_list.xlsx"):
        """
        初始化飞影重命名自动化
        
        Args:
            auth_file_path: 认证数据文件路径
            avatar_list_path: 素材列表文件路径
        """
        self.auth_file_path = auth_file_path
        self.avatar_list_path = avatar_list_path
        self.browser = None
        self.page = None
        self.auth_data = None
        
    def load_auth_data(self):
        """加载认证数据"""
        try:
            with open(self.auth_file_path, 'r', encoding='utf-8') as f:
                self.auth_data = json.load(f)
            print(f"✓ 成功加载认证数据，包含 {len(self.auth_data.get('cookies', []))} 个cookie")
            return True
        except Exception as e:
            print(f"❌ 加载认证数据失败: {e}")
            return False
    
    async def init_browser(self, headless=False):
        """初始化浏览器并应用认证数据"""
        try:
            playwright = await async_playwright().start()
            
            # 启动浏览器
            self.browser = await playwright.chromium.launch(
                headless=headless,
                args=[
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-blink-features=AutomationControlled',
                    '--no-first-run',
                    '--no-default-browser-check'
                ]
            )
            
            # 创建上下文
            context = await self.browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36"
            )
            
            # 应用cookies
            if self.auth_data and 'cookies' in self.auth_data:
                await context.add_cookies(self.auth_data['cookies'])
                print(f"✓ 已应用 {len(self.auth_data['cookies'])} 个cookie")
            
            # 创建页面
            self.page = await context.new_page()
            self.page.set_default_timeout(30000)  # 30秒超时
            
            # 导航到飞影avatar页面
            await self.page.goto("https://hifly.cc/avatar")
            print("✓ 已导航到飞影avatar页面")

            # 等待页面加载
            await self.page.wait_for_load_state('networkidle')

            # 应用localStorage数据
            if self.auth_data and 'localStorage' in self.auth_data:
                for key, value in self.auth_data['localStorage'].items():
                    await self.page.evaluate(f"localStorage.setItem('{key}', '{value}')")
                print("✓ 已应用localStorage数据")

                # 刷新页面以应用localStorage
                await self.page.reload()
                await self.page.wait_for_load_state('networkidle')

            # 点击"我的数字人"进入数字人列表页面
            await self.navigate_to_my_avatars()
            
            return True
            
        except Exception as e:
            print(f"❌ 初始化浏览器失败: {e}")
            return False

    async def navigate_to_my_avatars(self):
        """导航到我的数字人页面"""
        try:
            # 查找并点击"我的数字人"链接
            my_avatars_selectors = [
                'text=我的数字人',
                'a:has-text("我的数字人")',
                '[href*="avatar"]',
                '.nav-item:has-text("我的数字人")'
            ]

            for selector in my_avatars_selectors:
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        print(f"✓ 找到我的数字人链接: {selector}")
                        await element.click()
                        await self.page.wait_for_load_state('networkidle')
                        await asyncio.sleep(2)
                        return True
                except:
                    continue

            print("⚠️ 未找到我的数字人链接，可能已经在正确页面")
            return True

        except Exception as e:
            print(f"❌ 导航到我的数字人页面失败: {e}")
            return False

    async def search_card_by_name(self, target_name, max_show_more=5):
        """
        根据名称搜索卡片

        Args:
            target_name: 目标名称（如：张卓铭-38050-演员）
            max_show_more: 最大点击"显示更多"次数

        Returns:
            找到的卡片元素，如果未找到返回None
        """
        try:
            show_more_clicks = 0

            while show_more_clicks <= max_show_more:
                print(f"🔍 搜索卡片: {target_name} (尝试 {show_more_clicks + 1}/{max_show_more + 1})")

                # 等待页面加载完成
                await self.page.wait_for_load_state('networkidle')
                await asyncio.sleep(2)

                # 使用正确的卡片选择器
                possible_selectors = [
                    '.list-image-box',  # 主要的卡片容器
                    'div[class*="list-image"]',
                    'div[class*="box-main"]',
                    '.title.ellipsis',  # 标题元素
                    'div[class*="card"]',
                    'div[class*="item"]'
                ]

                cards_found = []
                for selector in possible_selectors:
                    try:
                        cards = await self.page.query_selector_all(selector)
                        if cards:
                            cards_found.extend(cards)
                            print(f"🔍 使用选择器 {selector} 找到 {len(cards)} 个元素")
                    except:
                        continue

                # 去重
                unique_cards = []
                for card in cards_found:
                    if card not in unique_cards:
                        unique_cards.append(card)

                print(f"🔍 总共找到 {len(unique_cards)} 个候选卡片元素")

                # 检查每个卡片的文本内容
                for i, card in enumerate(unique_cards):
                    try:
                        # 获取卡片文本内容
                        text_content = await card.text_content()
                        if text_content:
                            # 打印前几个卡片的内容用于调试
                            if i < 5:
                                print(f"🔍 卡片 {i+1} 内容预览: {text_content[:50]}...")

                            # 检查是否包含目标名称或ID
                            if target_name in text_content:
                                print(f"✅ 找到完全匹配的卡片: {target_name}")
                                return card

                            # 尝试部分匹配（只匹配ID部分）
                            target_id = target_name.split('-')[1] if '-' in target_name else target_name
                            if target_id in text_content and len(target_id) > 3:
                                print(f"✅ 找到ID匹配的卡片: {target_id}")
                                print(f"📝 卡片内容: {text_content[:100]}...")
                                return card

                    except Exception as e:
                        print(f"⚠️ 检查卡片 {i+1} 时出错: {e}")
                        continue

                # 如果没找到，尝试点击"显示更多"
                if show_more_clicks < max_show_more:
                    show_more_button = await self.page.query_selector('.view-more')
                    if show_more_button:
                        print("📄 点击显示更多...")
                        await show_more_button.click()
                        await asyncio.sleep(3)  # 等待加载
                        show_more_clicks += 1
                    else:
                        print("⚠️ 未找到显示更多按钮")
                        break
                else:
                    break

            print(f"❌ 未找到卡片: {target_name}")
            return None

        except Exception as e:
            print(f"❌ 搜索卡片时出错: {e}")
            return None
    
    async def rename_card(self, card_element, new_name):
        """
        重命名卡片

        Args:
            card_element: 卡片元素
            new_name: 新名称

        Returns:
            是否重命名成功
        """
        try:
            print(f"🖱️ 悬浮到卡片上...")

            # 如果传入的是标题元素，需要找到父级卡片容器
            if await card_element.get_attribute('class') and 'title' in await card_element.get_attribute('class'):
                # 向上查找卡片容器
                card_container = await card_element.query_selector('xpath=ancestor::div[contains(@class, "list-image-box")]')
                if card_container:
                    card_element = card_container
                    print("✓ 找到卡片容器")

            # 悬浮到卡片上
            await card_element.hover()
            await asyncio.sleep(1)

            # 查找三个点按钮 - 使用更精确的选择器
            three_dots_button = await card_element.query_selector('.ant-dropdown-trigger.op button')
            if not three_dots_button:
                # 尝试在整个页面查找（悬浮后可能出现）
                three_dots_button = await self.page.query_selector('.ant-dropdown-trigger.op button')

            if not three_dots_button:
                print("❌ 未找到三个点按钮")
                # 调试：打印卡片HTML结构
                card_html = await card_element.inner_html()
                print(f"🔍 卡片HTML结构: {card_html[:200]}...")
                return False

            print("🔘 点击三个点按钮...")
            await three_dots_button.click()
            await asyncio.sleep(2)  # 增加等待时间

            # 查找重命名选项 - 尝试多种可能的文本
            rename_selectors = [
                'text=重命名',
                'text=编辑',
                'text=修改名称',
                '[role="menuitem"]:has-text("重命名")',
                '.ant-dropdown-menu-item:has-text("重命名")'
            ]

            rename_option = None
            for selector in rename_selectors:
                try:
                    rename_option = await self.page.query_selector(selector)
                    if rename_option:
                        print(f"✓ 找到重命名选项: {selector}")
                        break
                except:
                    continue

            if not rename_option:
                print("❌ 未找到重命名选项")
                # 调试：打印页面上的菜单项
                menu_items = await self.page.query_selector_all('.ant-dropdown-menu-item, [role="menuitem"]')
                print(f"🔍 找到 {len(menu_items)} 个菜单项")
                for i, item in enumerate(menu_items[:5]):
                    try:
                        text = await item.text_content()
                        print(f"  菜单项 {i+1}: {text}")
                    except:
                        pass
                return False

            print("✏️ 点击重命名选项...")
            await rename_option.click()
            await asyncio.sleep(2)

            # 查找输入框 - 尝试多种选择器
            input_selectors = [
                'input[type="text"]',
                'textarea',
                '.ant-input',
                'input[placeholder*="名称"]',
                'input[placeholder*="标题"]'
            ]

            input_field = None
            for selector in input_selectors:
                try:
                    input_field = await self.page.query_selector(selector)
                    if input_field:
                        print(f"✓ 找到输入框: {selector}")
                        break
                except:
                    continue

            if not input_field:
                print("❌ 未找到输入框")
                return False

            print(f"📝 输入新名称: {new_name}")
            # 清空输入框并输入新名称
            await input_field.click()
            await asyncio.sleep(0.5)
            # 使用键盘快捷键全选
            await input_field.press('Control+a')
            await asyncio.sleep(0.2)
            await input_field.fill(new_name)
            await asyncio.sleep(0.5)

            # 查找确认按钮
            confirm_selectors = [
                'button:has-text("确定")',
                'button:has-text("保存")',
                'button:has-text("确认")',
                '.ant-btn-primary:has-text("确定")',
                '.ant-btn-primary:has-text("保存")'
            ]

            confirm_button = None
            for selector in confirm_selectors:
                try:
                    confirm_button = await self.page.query_selector(selector)
                    if confirm_button:
                        print(f"✓ 找到确认按钮: {selector}")
                        break
                except:
                    continue

            if confirm_button:
                print("✅ 点击确认按钮...")
                await confirm_button.click()
                await asyncio.sleep(3)
                return True
            else:
                # 尝试按Enter键
                print("⌨️ 按Enter键确认...")
                await input_field.press('Enter')
                await asyncio.sleep(3)
                return True

        except Exception as e:
            print(f"❌ 重命名操作失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def debug_page_content(self):
        """调试页面内容，截图并分析页面结构"""
        try:
            # 截图保存
            screenshot_path = "debug_hifly_page.png"
            await self.page.screenshot(path=screenshot_path, full_page=True)
            print(f"📸 页面截图已保存: {screenshot_path}")

            # 获取页面标题
            title = await self.page.title()
            print(f"📄 页面标题: {title}")

            # 检查页面是否正确加载
            url = self.page.url
            print(f"🌐 当前URL: {url}")

            # 查找所有可能的卡片元素并分析
            all_elements = await self.page.query_selector_all('div')
            print(f"🔍 页面总共有 {len(all_elements)} 个div元素")

            # 查找包含数字的元素（可能是ID）
            elements_with_numbers = []
            for element in all_elements[:50]:  # 只检查前50个
                try:
                    text = await element.text_content()
                    if text and any(char.isdigit() for char in text) and len(text.strip()) > 0:
                        elements_with_numbers.append(text.strip()[:100])
                except:
                    continue

            print(f"🔢 找到 {len(elements_with_numbers)} 个包含数字的元素:")
            for i, text in enumerate(elements_with_numbers[:10]):  # 只显示前10个
                print(f"  {i+1}. {text}")

        except Exception as e:
            print(f"❌ 调试页面内容时出错: {e}")

    async def process_rename_task(self, actor_name, video_id):
        """
        处理单个重命名任务

        Args:
            actor_name: 演员名称
            video_id: 视频ID

        Returns:
            重命名结果状态
        """
        try:
            # 构建正确的名称格式
            correct_name = f"{actor_name}-{video_id}-演员"
            print(f"\n🎯 开始处理重命名任务: {correct_name}")

            # 先调试页面内容
            await self.debug_page_content()

            # 搜索卡片（可能包含多余字母的名称）
            card = await self.search_card_by_name(correct_name)

            if not card:
                # 如果直接搜索不到，尝试搜索包含ID的卡片
                print(f"🔄 尝试搜索ID: {video_id}")
                card = await self.search_card_by_name(str(video_id))

            if not card:
                print(f"❌ 未找到对应的卡片: {correct_name}")
                return "未找到卡片"

            # 执行重命名操作
            success = await self.rename_card(card, correct_name)

            if success:
                print(f"🎉 重命名成功: {correct_name}")
                return "重命名成功"
            else:
                print(f"💔 重命名失败: {correct_name}")
                return "重命名失败"

        except Exception as e:
            print(f"❌ 处理重命名任务时出错: {e}")
            return f"错误: {str(e)}"
    
    async def close_browser(self):
        """关闭浏览器"""
        try:
            if self.browser:
                await self.browser.close()
                print("✓ 浏览器已关闭")
        except Exception as e:
            print(f"⚠️ 关闭浏览器时出错: {e}")


async def test_rename_function():
    """测试重命名功能"""
    print("🚀 开始测试飞影重命名功能...")
    
    # 测试数据
    test_data = [
        {"actor_name": "张卓铭", "video_id": "38050"},
        {"actor_name": "韩卫军", "video_id": "48074"}
    ]
    
    automation = HiflyRenameAutomation()
    
    try:
        # 加载认证数据
        if not automation.load_auth_data():
            return
        
        # 初始化浏览器
        if not await automation.init_browser(headless=False):
            return
        
        # 处理测试数据
        for data in test_data:
            result = await automation.process_rename_task(
                data["actor_name"], 
                data["video_id"]
            )
            print(f"📊 {data['actor_name']}-{data['video_id']}-演员: {result}")
            
            # 等待一段时间再处理下一个
            await asyncio.sleep(3)
        
        print("\n🎯 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
    
    finally:
        await automation.close_browser()


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_rename_function())
