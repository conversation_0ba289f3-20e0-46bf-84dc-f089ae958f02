"""
检查文件路径问题
"""

import os


def check_file_paths():
    """检查文件路径问题"""
    print("=" * 60)
    print("🔍 检查文件路径问题")
    print("=" * 60)
    
    print(f"📂 当前工作目录: {os.getcwd()}")
    
    # 检查各种可能的路径
    paths_to_check = [
        "data/avatar_list.xlsx",
        os.path.join(os.getcwd(), "data", "avatar_list.xlsx"),
        os.path.join(os.path.dirname(__file__), "data", "avatar_list.xlsx"),
        "avatar_list.xlsx",
        "./data/avatar_list.xlsx",
        "../data/avatar_list.xlsx",
        "../../data/avatar_list.xlsx"
    ]
    
    print(f"\n🔍 检查各种可能的路径:")
    for i, path in enumerate(paths_to_check, 1):
        exists = os.path.exists(path)
        abs_path = os.path.abspath(path)
        print(f"  {i}. {path}")
        print(f"     绝对路径: {abs_path}")
        print(f"     存在: {'✅' if exists else '❌'}")
        if exists:
            try:
                import pandas as pd
                df = pd.read_excel(path)
                print(f"     可读取: ✅ ({len(df)} 条记录)")
            except Exception as e:
                print(f"     可读取: ❌ ({e})")
        print()
    
    # 检查data目录
    data_dirs = [
        "data",
        "./data",
        "../data",
        "../../data",
        os.path.join(os.getcwd(), "data")
    ]
    
    print(f"🔍 检查data目录:")
    for i, dir_path in enumerate(data_dirs, 1):
        exists = os.path.exists(dir_path)
        is_dir = os.path.isdir(dir_path) if exists else False
        abs_path = os.path.abspath(dir_path)
        print(f"  {i}. {dir_path}")
        print(f"     绝对路径: {abs_path}")
        print(f"     存在: {'✅' if exists else '❌'}")
        print(f"     是目录: {'✅' if is_dir else '❌'}")
        
        if exists and is_dir:
            try:
                files = os.listdir(dir_path)
                excel_files = [f for f in files if f.endswith('.xlsx')]
                print(f"     Excel文件: {excel_files}")
            except Exception as e:
                print(f"     列表失败: {e}")
        print()
    
    print(f"💡 建议:")
    print(f"  1. 确认avatar_list.xlsx文件的实际位置")
    print(f"  2. 检查程序运行时的工作目录")
    print(f"  3. 使用绝对路径或相对于脚本的路径")


if __name__ == "__main__":
    check_file_paths()
