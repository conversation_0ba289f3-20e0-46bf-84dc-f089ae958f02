#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
剪贴板管理器，用于监听和处理剪贴板内容
"""

import io
import pandas as pd
import config
from PySide6.QtCore import QObject, Signal, Slot, QMimeData, Qt
from PySide6.QtGui import QClipboard
from core.text_converter import TextConverter

class ClipboardManager(QObject):
    """剪贴板管理器类，用于监听和处理剪贴板内容"""
    
    # 定义信号
    table_data_detected = Signal(pd.DataFrame)  # 检测到表格数据信号
    
    def __init__(self, clipboard=None):
        """初始化剪贴板管理器"""
        super().__init__()
        
        # 如果未提供剪贴板，则使用应用程序的剪贴板
        self.clipboard = clipboard
        
        # 是否启用监听
        self.monitoring_enabled = False
        
        # 创建文本转换器
        self.text_converter = TextConverter()
        
        # 文本转换设置
        self.enable_text_conversion = True
        self.convert_numbers = True
        
        # 跟踪上次检测到的列
        self.last_columns_found = []
        
        # 已知的表头列表（用于识别和移除表头）
        self.known_headers = [
            config.SCRIPT_TEXT_COLUMN,   # 克隆文案
            config.ACTOR_COLUMN,         # 演员形象
            config.NAMING_COLUMN,        # 命名
            config.WORK_ORDER_COLUMN,    # 工单号
            config.SECONDARY_ORDER_COLUMN,  # 副单号
            config.MATERIAL_ID_COLUMN,   # 素材ID
            config.MATCHER_COLUMN        # 匹配人
        ]
    
    def set_clipboard(self, clipboard):
        """设置剪贴板对象"""
        self.clipboard = clipboard
    
    def start_monitoring(self):
        """开始监听剪贴板"""
        if self.clipboard and not self.monitoring_enabled:
            self.clipboard.dataChanged.connect(self.on_clipboard_change)
            self.monitoring_enabled = True
            return True
        return False
    
    def stop_monitoring(self):
        """停止监听剪贴板"""
        if self.clipboard and self.monitoring_enabled:
            self.clipboard.dataChanged.disconnect(self.on_clipboard_change)
            self.monitoring_enabled = False
            return True
        return False
    
    def is_monitoring(self):
        """返回是否正在监听剪贴板"""
        return self.monitoring_enabled
    
    def set_text_conversion_settings(self, enable_conversion, convert_numbers):
        """设置文本转换参数"""
        self.enable_text_conversion = enable_conversion
        self.convert_numbers = convert_numbers
        self.text_converter.convert_numbers = convert_numbers
    
    @Slot()
    def on_clipboard_change(self):
        """剪贴板内容变化时的处理函数"""
        if not self.monitoring_enabled or not self.clipboard:
            return
        
        # 获取剪贴板内容
        mime_data = self.clipboard.mimeData()
        
        # 检查是否包含表格数据
        if mime_data.hasHtml() or mime_data.hasText():
            # 尝试将内容解析为DataFrame
            df = self.extract_dataframe_from_clipboard(mime_data)
            if df is not None and not df.empty:
                print(f"剪贴板变化: 检测到表格数据 {len(df)}行, {len(df.columns)}列")
                
                # 处理DataFrame，规范化列名
                normalized_df = self.normalize_column_names(df)
                
                # 检查是否成功规范化 - 如果返回None说明缺少必要列
                if normalized_df is None:
                    print("剪贴板监听: 列名规范化失败，可能缺少必要列（工单号、克隆文案、演员形象）")
                    return
                
                # 如果启用了文本转换，处理克隆文案列
                if self.enable_text_conversion and normalized_df is not None and not normalized_df.empty:
                    print(f"文本转换已启用，转换数字: {self.convert_numbers}")
                    normalized_df = self.apply_text_conversion(normalized_df)
                else:
                    print(f"文本转换已禁用或数据为空，跳过转换")
                
                # 确认所有必需列都存在
                required_columns = [
                    config.SCRIPT_TEXT_COLUMN,  # 克隆文案
                    config.ACTOR_COLUMN,        # 演员形象
                    config.WORK_ORDER_COLUMN    # 工单号
                ]
                
                missing_columns = [col for col in required_columns if col not in normalized_df.columns]
                
                if missing_columns:
                    missing_names = {
                        config.SCRIPT_TEXT_COLUMN: "克隆文案",
                        config.ACTOR_COLUMN: "演员形象",
                        config.WORK_ORDER_COLUMN: "工单号"
                    }
                    missing_readable = [missing_names.get(col, col) for col in missing_columns]
                    print(f"剪贴板监听: 缺少必要列 {', '.join(missing_readable)}")
                    return

                # 过滤空白行
                original_count = len(normalized_df)
                key_columns = [config.SCRIPT_TEXT_COLUMN, config.ACTOR_COLUMN, config.WORK_ORDER_COLUMN]

                # 创建一个掩码，标识非空行
                non_empty_mask = normalized_df[key_columns].notna().any(axis=1)

                # 同时检查字符串是否为空或只包含空白字符
                for col in key_columns:
                    if col in normalized_df.columns:
                        # 将NaN转换为空字符串，然后检查是否为空白
                        non_empty_mask = non_empty_mask | (
                            normalized_df[col].fillna('').astype(str).str.strip() != ''
                        )

                # 过滤掉空白行
                filtered_df = normalized_df[non_empty_mask].copy()

                if len(filtered_df) != original_count:
                    print(f"剪贴板监听过滤空白行: 原始{original_count}行 -> 有效{len(filtered_df)}行")

                # 只有成功规范化且包含所有必要列且有有效数据才发送信号
                if filtered_df is not None and not filtered_df.empty:
                    # 发送信号，通知检测到表格数据
                    self.table_data_detected.emit(filtered_df)
                    print(f"剪贴板监听检测到并处理了{len(filtered_df)}条有效数据")
                else:
                    print("剪贴板数据检测到表格，但过滤后没有有效数据")
            else:
                if df is None:
                    print("剪贴板监听: 无法从剪贴板内容提取数据")
                else:
                    print("剪贴板监听: 提取的数据为空")
    
    def apply_text_conversion(self, df):
        """应用文本转换到DataFrame"""
        if df is None or df.empty:
            print("警告: DataFrame为空，无法应用文本转换")
            return df
            
        if config.SCRIPT_TEXT_COLUMN not in df.columns:
            print(f"警告: 未找到{config.SCRIPT_TEXT_COLUMN}列，无法应用文本转换")
            print(f"可用的列: {list(df.columns)}")
            return df
            
        print(f"正在应用文本转换到{len(df)}行数据...")
        print(f"文本转换设置: 启用={self.enable_text_conversion}, 转换数字={self.convert_numbers}")
        
        # 遍历每一行，处理克隆文案列
        conversion_count = 0
        for idx, row in df.iterrows():
            original_text = row[config.SCRIPT_TEXT_COLUMN]
            if original_text and isinstance(original_text, str):
                # 应用文本转换
                converted_text = self.text_converter.convert_text(
                    original_text, 
                    apply_rules=True, 
                    apply_number_conversion=self.convert_numbers
                )
                # 如果文本有变化，打印出来
                if original_text != converted_text:
                    conversion_count += 1
                    print(f"行 {idx+1} 文本转换:")
                    print(f"原文: {original_text[:50]}..." if len(original_text) > 50 else f"原文: {original_text}")
                    print(f"转换: {converted_text[:50]}..." if len(converted_text) > 50 else f"转换: {converted_text}")
                
                # 更新DataFrame
                df.at[idx, config.SCRIPT_TEXT_COLUMN] = converted_text
        
        print(f"文本转换完成，共转换了{conversion_count}行文本")
        return df
    
    def extract_dataframe_from_clipboard(self, mime_data):
        """从剪贴板提取DataFrame"""
        print("开始从剪贴板提取数据...")

        try:
            # 检查剪贴板内容类型
            has_html = mime_data.hasHtml()
            has_text = mime_data.hasText()
            print(f"剪贴板内容类型: HTML={has_html}, Text={has_text}")

            # 尝试从HTML读取表格数据
            if has_html:
                html = mime_data.html()
                print(f"HTML内容长度: {len(html)}")
                print(f"HTML内容预览: {html[:200]}...")

                try:
                    # 尝试从HTML中读取表格 - 使用StringIO避免FutureWarning
                    from io import StringIO
                    print("尝试使用pandas.read_html解析HTML...")
                    dfs = pd.read_html(StringIO(html))
                    if dfs and len(dfs) > 0:
                        print(f"成功从HTML解析到{len(dfs)}个表格，使用第一个")
                        return dfs[0]  # 返回第一个表格
                except ImportError as e:
                    print(f"pandas.read_html依赖缺失: {e}")
                    print("尝试使用备用HTML解析方法...")
                    # 使用备用的HTML解析方法
                    return self.parse_html_table_manually(html)
                except Exception as e:
                    print(f"从HTML读取表格失败: {e}")
                    print("尝试使用备用HTML解析方法...")
                    # 使用备用的HTML解析方法
                    return self.parse_html_table_manually(html)

            # 如果HTML读取失败，尝试从文本读取
            if has_text:
                text = mime_data.text()
                print(f"文本内容长度: {len(text)}")
                print(f"文本内容预览: {text[:200]}...")

                # 检查是否是Excel格式的表格（制表符分隔）
                if '\t' in text:
                    try:
                        print("检测到制表符，尝试解析为表格...")
                        # 尝试从文本中读取表格（制表符分隔）
                        df = pd.read_csv(io.StringIO(text), sep='\t')
                        print(f"成功解析制表符分隔的表格: {len(df)}行, {len(df.columns)}列")
                        return df
                    except Exception as e:
                        print(f"从制表符分隔文本读取表格失败: {e}")

                # 检查是否是CSV格式的表格（逗号分隔）
                elif ',' in text:
                    try:
                        # 尝试从文本中读取表格（逗号分隔）
                        df = pd.read_csv(io.StringIO(text), sep=',')
                        return df
                    except Exception as e:
                        print(f"从逗号分隔文本读取表格失败: {e}")
        
        except Exception as e:
            print(f"从剪贴板提取DataFrame失败: {e}")

        return None

    def parse_html_table_manually(self, html):
        """手动解析HTML表格，作为pandas.read_html的备用方案"""
        try:
            print("使用手动HTML解析方法...")
            import re

            # 查找表格标签
            table_pattern = r'<table[^>]*>(.*?)</table>'
            table_match = re.search(table_pattern, html, re.DOTALL | re.IGNORECASE)

            if not table_match:
                print("未找到HTML表格标签")
                return None

            table_content = table_match.group(1)

            # 查找所有行
            row_pattern = r'<tr[^>]*>(.*?)</tr>'
            rows = re.findall(row_pattern, table_content, re.DOTALL | re.IGNORECASE)

            if not rows:
                print("未找到表格行")
                return None

            # 解析每一行的单元格
            data = []
            for row in rows:
                # 查找单元格（th或td）
                cell_pattern = r'<(?:th|td)[^>]*>(.*?)</(?:th|td)>'
                cells = re.findall(cell_pattern, row, re.DOTALL | re.IGNORECASE)

                # 清理单元格内容（移除HTML标签）
                clean_cells = []
                for cell in cells:
                    # 移除HTML标签
                    clean_cell = re.sub(r'<[^>]+>', '', cell)
                    # 解码HTML实体
                    clean_cell = clean_cell.replace('&nbsp;', ' ').replace('&amp;', '&')
                    clean_cell = clean_cell.replace('&lt;', '<').replace('&gt;', '>')
                    clean_cell = clean_cell.strip()
                    clean_cells.append(clean_cell)

                if clean_cells:  # 只添加非空行
                    data.append(clean_cells)

            if not data:
                print("解析后没有有效数据")
                return None

            # 创建DataFrame
            if len(data) > 1:
                # 第一行作为列名
                df = pd.DataFrame(data[1:], columns=data[0])
            else:
                # 只有一行数据，使用默认列名
                df = pd.DataFrame(data)

            print(f"手动HTML解析成功: {len(df)}行, {len(df.columns)}列")
            return df

        except Exception as e:
            print(f"手动HTML解析失败: {e}")
            return None
    
    def normalize_column_names(self, df):
        """规范化列名，处理表头行"""
        if df is None or df.empty:
            return None
        
        # 重置上次检测到的列
        self.last_columns_found = list(df.columns)
        
        # 检查是否所有列名都是数字索引（0, 1, 2...）
        numeric_columns = True
        for col in df.columns:
            if not (isinstance(col, int) or (isinstance(col, str) and col.isdigit())):
                numeric_columns = False
                break
        
        # 如果所有列名都是数字索引，很可能第一行是真正的表头
        first_row_is_header = False
        
        if numeric_columns and len(df) > 0:
            print("检测到所有列名为数字索引，尝试使用第一行作为表头")
            # 检查第一行是否包含可能的表头关键词
            header_keywords = ["工单", "单号", "文案", "演员", "形象", "命名", "素材", "匹配"]
            
            # 统计第一行中包含关键词的单元格数量
            keyword_count = 0
            for value in df.iloc[0]:
                if value is not None and isinstance(value, str):
                    value_str = str(value).strip().lower()
                    for keyword in header_keywords:
                        if keyword in value_str:
                            keyword_count += 1
                            break
            
            # 如果第一行有足够多的单元格包含关键词，认为它是表头
            if keyword_count >= 2:  # 至少包含2个关键词才认为是表头
                print(f"第一行包含 {keyword_count} 个表头关键词，将其作为表头")
                # 将第一行设为列名
                header_row = df.iloc[0].astype(str).tolist()
                
                # 确保列名唯一
                unique_headers = []
                header_counts = {}
                
                for h in header_row:
                    h_str = str(h).strip()
                    if h_str in header_counts:
                        header_counts[h_str] += 1
                        unique_headers.append(f"{h_str}_{header_counts[h_str]}")
                    else:
                        header_counts[h_str] = 0
                        unique_headers.append(h_str)
                
                # 更新DataFrame的列名并移除第一行
                df.columns = unique_headers
                df = df.iloc[1:].reset_index(drop=True)
                first_row_is_header = True
                
                # 更新检测到的列
                self.last_columns_found = list(df.columns)
                print(f"使用第一行作为表头后的列名: {self.last_columns_found}")
        
        # 如果第一行不是表头，继续使用原来的检测逻辑
        if not first_row_is_header:
            # 检查列名中是否包含已知表头
            for col in df.columns:
                col_str = str(col).strip()
                for header in self.known_headers:
                    if col_str.lower() == header.lower() or header.lower() in col_str.lower():
                        first_row_is_header = True
                        break
                if first_row_is_header:
                    break
            
            # 如果列名中没有找到已知表头，检查第一行数据
            if not first_row_is_header and len(df) > 0:
                for idx, value in enumerate(df.iloc[0]):
                    if value is not None:
                        value_str = str(value).strip()
                        for header in self.known_headers:
                            if value_str.lower() == header.lower() or header.lower() in value_str.lower():
                                # 第一行是表头，将其设为列名
                                # 确保列名唯一，添加序号后缀
                                header_row = df.iloc[0].astype(str).tolist()
                                unique_headers = []
                                header_counts = {}
                                
                                for h in header_row:
                                    h_str = str(h).strip()
                                    if h_str in header_counts:
                                        header_counts[h_str] += 1
                                        unique_headers.append(f"{h_str}_{header_counts[h_str]}")
                                    else:
                                        header_counts[h_str] = 0
                                        unique_headers.append(h_str)
                                
                                df.columns = unique_headers
                                df = df.iloc[1:].reset_index(drop=True)
                                first_row_is_header = True
                                
                                # 更新检测到的列
                                self.last_columns_found = list(df.columns)
                                break
                    if first_row_is_header:
                        break
        
        # 检查列名是否有重复，如果有则添加后缀
        if df.columns.duplicated().any():
            # 创建新的唯一列名
            new_cols = []
            seen = {}
            for col in df.columns:
                col_str = str(col).strip()
                if col_str in seen:
                    seen[col_str] += 1
                    new_cols.append(f"{col_str}_{seen[col_str]}")
                else:
                    seen[col_str] = 0
                    new_cols.append(col_str)
            df.columns = new_cols
            
            # 更新检测到的列
            self.last_columns_found = list(df.columns)
        
        # 打印当前列名，用于调试
        print(f"规范化前的列名: {list(df.columns)}")
        
        # 详细记录所有列
        print(f"所有列: {list(df.columns)}")
        
        # 规范化列名
        new_columns = {}
        
        # 处理克隆文案列 - 按照优先级顺序
        clone_script_found = False
        
        # 优先级1: 精确匹配"克隆文案"
        for col in df.columns:
            col_str = str(col).strip()
            if col_str == "克隆文案":
                new_columns[col] = config.SCRIPT_TEXT_COLUMN
                print(f"找到精确匹配的克隆文案列: {col}")
                clone_script_found = True
                break
        
        # 优先级2: 如果没有找到精确匹配的"克隆文案"，则查找"输出文案"
        if not clone_script_found:
            for col in df.columns:
                col_str = str(col).strip()
                if col_str == "输出文案":
                    new_columns[col] = config.SCRIPT_TEXT_COLUMN
                    print(f"找到替代的输出文案列: {col}")
                    clone_script_found = True
                    break
        
        # 优先级3: 如果依然没找到，模糊匹配包含"克隆文案"或"输出文案"的列
        if not clone_script_found:
            for col in df.columns:
                col_str = str(col).strip().lower()
                if "克隆文案" in col_str:
                    new_columns[col] = config.SCRIPT_TEXT_COLUMN
                    print(f"找到包含'克隆文案'的列: {col}")
                    clone_script_found = True
                    break
                elif "输出文案" in col_str:
                    new_columns[col] = config.SCRIPT_TEXT_COLUMN
                    print(f"找到包含'输出文案'的列: {col}")
                    clone_script_found = True
                    break
        
        # 优先级4: 如果还是没找到，尝试一般的"文案"列
        if not clone_script_found:
            for col in df.columns:
                col_str = str(col).strip().lower()
                if "文案" in col_str:
                    new_columns[col] = config.SCRIPT_TEXT_COLUMN
                    print(f"找到包含'文案'的列: {col}")
                    clone_script_found = True
                    break
        
        # 处理演员形象列 - 按照优先级顺序
        actor_found = False
        
        # 优先级1: 精确匹配"演员形象"
        for col in df.columns:
            col_str = str(col).strip()
            if col_str == "演员形象":
                new_columns[col] = config.ACTOR_COLUMN
                print(f"找到精确匹配的演员形象列: {col}")
                actor_found = True
                break
        
        # 优先级2: 如果没有找到精确匹配的"演员形象"，则查找"演员姓名"
        if not actor_found:
            for col in df.columns:
                col_str = str(col).strip()
                if col_str == "演员姓名":
                    new_columns[col] = config.ACTOR_COLUMN
                    print(f"找到替代的演员姓名列: {col}")
                    actor_found = True
                    break
        
        # 优先级3: 如果依然没找到，模糊匹配包含"演员"的列
        if not actor_found:
            for col in df.columns:
                col_str = str(col).strip().lower()
                if "演员" in col_str:
                    new_columns[col] = config.ACTOR_COLUMN
                    print(f"找到包含'演员'的列: {col}")
                    actor_found = True
                    break
        
        # 处理工单号列
        order_found = False
        
        # 优先级1: 精确匹配"工单号"
        for col in df.columns:
            col_str = str(col).strip()
            if col_str == "工单号":
                new_columns[col] = config.WORK_ORDER_COLUMN
                print(f"找到精确匹配的工单号列: {col}")
                order_found = True
                break
        
        # 优先级2: 如果没有找到精确匹配的"工单号"，模糊匹配包含"工单"或"单号"的列
        if not order_found:
            for col in df.columns:
                col_str = str(col).strip().lower()
                if "工单" in col_str or "单号" in col_str:
                    new_columns[col] = config.WORK_ORDER_COLUMN
                    print(f"找到包含'工单'或'单号'的列: {col}")
                    order_found = True
                    break
        
        # 处理其他列
        for col in df.columns:
            # 跳过已经处理过的列
            if col in new_columns:
                continue
                
            col_str = str(col).strip().lower()
            
            # 命名列
            if "命名" in col_str or "名称" in col_str or "name" in col_str:
                new_columns[col] = config.NAMING_COLUMN
            
            # 副单号列
            elif "副单" in col_str or "secondary" in col_str:
                new_columns[col] = config.SECONDARY_ORDER_COLUMN
            
            # 素材ID列
            elif "素材" in col_str or "id" in col_str:
                new_columns[col] = config.MATERIAL_ID_COLUMN
            
            # 匹配人列
            elif "匹配" in col_str or "matcher" in col_str:
                new_columns[col] = config.MATCHER_COLUMN
        
        # 重命名列
        df = df.rename(columns=new_columns)
        print(f"规范化后的列名: {list(df.columns)}")
        
        # 更新上次检测到的列为规范化后的列名
        self.last_columns_found = list(df.columns)
        
        # 检查必须的三列是否都存在
        required_columns = [
            config.SCRIPT_TEXT_COLUMN,  # 克隆文案
            config.ACTOR_COLUMN,        # 演员形象
            config.WORK_ORDER_COLUMN    # 工单号
        ]
        
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            missing_names = {
                config.SCRIPT_TEXT_COLUMN: "克隆文案",
                config.ACTOR_COLUMN: "演员形象",
                config.WORK_ORDER_COLUMN: "工单号"
            }
            missing_readable = [missing_names.get(col, col) for col in missing_columns]
            print(f"错误: 缺少必要的列: {missing_readable}")
            print(f"可用的列: {list(df.columns)}")
            
            # 打印前几行数据，帮助识别可能的列
            if len(df) > 0:
                print("数据预览:")
                for i, row in df.head(2).iterrows():
                    print(f"行 {i+1}:")
                    for col in df.columns:
                        val = row[col]
                        if isinstance(val, str) and len(val) > 50:
                            val = val[:50] + "..."
                        print(f"  {col}: {val}")
            
            return None
        
        # 再次检查是否有重复列名，如果有则合并内容
        if df.columns.duplicated().any():
            # 获取重复的列名
            duplicated_cols = df.columns[df.columns.duplicated()].unique()
            
            # 创建一个新的DataFrame，只包含非重复列
            unique_cols = [col for col in df.columns if col not in duplicated_cols]
            new_df = df[unique_cols].copy()
            
            # 处理每个重复的列
            for col in duplicated_cols:
                # 获取所有同名列
                same_name_cols = [c for c in df.columns if c == col]
                
                # 合并这些列的内容（使用第一个非空值）
                new_df[col] = df[same_name_cols].apply(
                    lambda row: next((val for val in row if pd.notna(val) and str(val).strip()), ""),
                    axis=1
                )
            
            df = new_df
            
            # 更新上次检测到的列
            self.last_columns_found = list(df.columns)
        
        return df
    
    def process_clipboard_data(self):
        """
        手动处理剪贴板数据
        返回解析的DataFrame或None
        """
        if not self.clipboard:
            print("无法处理剪贴板数据: 剪贴板对象未初始化")
            return None
        
        # 获取剪贴板内容
        mime_data = self.clipboard.mimeData()
        
        # 尝试提取DataFrame
        df = self.extract_dataframe_from_clipboard(mime_data)
        
        # 如果成功提取到数据，进行规范化处理
        if df is not None and not df.empty:
            print(f"从剪贴板提取到数据: {len(df)}行, {len(df.columns)}列")
            
            # 规范化列名，使用第一行作为列名对照
            normalized_df = self.normalize_column_names(df)
            
            # 检查是否成功规范化
            if normalized_df is None:
                print("无法处理剪贴板数据: 列名规范化失败，可能缺少必要列（工单号、克隆文案、演员形象）")
                return None
                
            # 应用文本转换（如果启用）
            if self.enable_text_conversion and normalized_df is not None and not normalized_df.empty:
                print(f"手动粘贴: 文本转换已启用，转换数字: {self.convert_numbers}")
                normalized_df = self.apply_text_conversion(normalized_df)
            else:
                print(f"手动粘贴: 文本转换已禁用或数据为空，跳过转换")
            
            # 确认所有必需列都存在
            required_columns = [
                config.SCRIPT_TEXT_COLUMN,  # 克隆文案
                config.ACTOR_COLUMN,        # 演员形象
                config.WORK_ORDER_COLUMN    # 工单号
            ]
            
            missing_columns = [col for col in required_columns if col not in normalized_df.columns]
            
            if missing_columns:
                missing_names = {
                    config.SCRIPT_TEXT_COLUMN: "克隆文案",
                    config.ACTOR_COLUMN: "演员形象",
                    config.WORK_ORDER_COLUMN: "工单号"
                }
                missing_readable = [missing_names.get(col, col) for col in missing_columns]
                print(f"无法处理剪贴板数据: 缺少必要列 {', '.join(missing_readable)}")
                return None

            # 过滤空白行 - 检查关键列是否都为空
            original_count = len(normalized_df)
            key_columns = [config.SCRIPT_TEXT_COLUMN, config.ACTOR_COLUMN, config.WORK_ORDER_COLUMN]

            # 创建一个掩码，标识非空行
            non_empty_mask = normalized_df[key_columns].notna().any(axis=1)

            # 同时检查字符串是否为空或只包含空白字符
            for col in key_columns:
                if col in normalized_df.columns:
                    # 将NaN转换为空字符串，然后检查是否为空白
                    non_empty_mask = non_empty_mask | (
                        normalized_df[col].fillna('').astype(str).str.strip() != ''
                    )

            # 过滤掉空白行
            filtered_df = normalized_df[non_empty_mask].copy()

            if len(filtered_df) != original_count:
                print(f"过滤空白行: 原始{original_count}行 -> 有效{len(filtered_df)}行")

            return filtered_df if not filtered_df.empty else None
        else:
            if df is None:
                print("无法处理剪贴板数据: 未能从剪贴板内容提取数据")
            else:
                print("无法处理剪贴板数据: 提取的数据为空")
            return None 