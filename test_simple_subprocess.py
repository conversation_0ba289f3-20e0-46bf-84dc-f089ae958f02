"""
测试最简单的subprocess
"""

import subprocess
import sys
import time
import json


def test_simple_subprocess():
    """测试最简单的subprocess"""
    print("=" * 60)
    print("🧪 测试最简单的subprocess")
    print("=" * 60)
    
    # 创建一个最简单的测试脚本
    simple_script = '''
import sys
import json
import time

print("Hello from subprocess!")
print("Arguments:", sys.argv)

# 模拟一些工作
for i in range(3):
    print(f"Working... {i+1}/3")
    time.sleep(1)

# 输出结果
result = {"success": True, "message": "Test completed"}
print("RESULT_JSON:" + json.dumps(result))
print("Subprocess exiting...")
sys.exit(0)
'''
    
    # 写入临时脚本文件
    with open("temp_test_script.py", "w", encoding="utf-8") as f:
        f.write(simple_script)
    
    try:
        print("🚀 启动简单的subprocess测试...")
        
        # 启动子进程
        process = subprocess.Popen(
            [sys.executable, "temp_test_script.py", "test_arg"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace'
        )
        
        print(f"✅ 进程已启动，PID: {process.pid}")
        
        # 等待进程完成
        start_time = time.time()
        while True:
            poll_result = process.poll()
            elapsed = time.time() - start_time
            
            if poll_result is not None:
                print(f"🔄 进程完成，退出码: {poll_result}，耗时: {elapsed:.1f}秒")
                break
            elif elapsed > 10:
                print(f"⚠️ 进程超时（{elapsed:.1f}秒），强制终止")
                process.terminate()
                time.sleep(2)
                if process.poll() is None:
                    process.kill()
                break
            else:
                print(f"🔄 进程运行中... ({elapsed:.1f}秒)")
                time.sleep(1)
        
        # 读取输出
        stdout, stderr = process.communicate()
        
        print("\n📋 标准输出:")
        if stdout:
            for i, line in enumerate(stdout.split('\n')):
                if line.strip():
                    print(f"  {i+1:2d}: {line}")
        else:
            print("  (无输出)")
        
        print("\n⚠️ 错误输出:")
        if stderr:
            for i, line in enumerate(stderr.split('\n')):
                if line.strip():
                    print(f"  {i+1:2d}: {line}")
        else:
            print("  (无错误)")
        
        # 检查结果
        if "RESULT_JSON:" in stdout:
            print("\n✅ 找到结果JSON，subprocess工作正常")
        else:
            print("\n❌ 未找到结果JSON，subprocess可能有问题")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    
    finally:
        # 清理临时文件
        try:
            import os
            os.remove("temp_test_script.py")
            print("\n🧹 临时文件已清理")
        except:
            pass
    
    print("\n" + "=" * 60)
    print("🧪 简单subprocess测试完成")
    print("=" * 60)


if __name__ == "__main__":
    test_simple_subprocess()
