#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试搜索信号阻止修复
验证搜索时不再触发"准备保存"日志
"""

import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_signal_blocking():
    """测试信号阻止功能"""
    print("=== 测试搜索信号阻止修复 ===")
    
    try:
        # 检查修复代码
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键修复点
        fixes = [
            "self.vm_table.blockSignals(True)",  # 搜索时阻止信号
            "self.vm_table.blockSignals(False)",  # 恢复信号
            "was_blocked = self.vm_table.signalsBlocked()",  # 保存信号状态
            "finally:",  # 确保信号恢复
        ]
        
        missing_fixes = []
        for fix in fixes:
            if fix in content:
                print(f"✅ 修复点存在: {fix}")
            else:
                print(f"❌ 修复点缺失: {fix}")
                missing_fixes.append(fix)
        
        if missing_fixes:
            return False
        
        # 检查搜索方法中的信号阻止
        search_methods = [
            "vm_search_in_dataframe",
            "highlight_vm_search_result", 
            "clear_vm_search_highlights"
        ]
        
        for method in search_methods:
            if f"def {method}" in content:
                # 检查方法内是否有信号阻止
                method_start = content.find(f"def {method}")
                method_end = content.find("\n    def ", method_start + 1)
                if method_end == -1:
                    method_end = len(content)
                
                method_content = content[method_start:method_end]
                
                if "blockSignals(True)" in method_content:
                    print(f"✅ {method} 方法包含信号阻止")
                else:
                    print(f"❌ {method} 方法缺少信号阻止")
                    return False
        
        # 检查是否删除了重复代码
        duplicate_patterns = [
            "清除声音管理搜索高亮",  # 重复的方法注释
            "声音管理搜索按钮点击处理",  # 错误的方法注释
        ]
        
        for pattern in duplicate_patterns:
            if pattern in content:
                print(f"❌ 仍存在重复代码: {pattern}")
                return False
            else:
                print(f"✅ 已删除重复代码: {pattern}")
        
        return True
        
    except Exception as e:
        print(f"❌ 信号阻止测试失败: {str(e)}")
        return False

def test_search_optimization():
    """测试搜索优化"""
    print("\n=== 测试搜索优化 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查搜索优化
        optimizations = [
            "batch_size = 200",  # 大批次处理
            "QApplication.processEvents()",  # UI响应性
            "vm_search_in_dataframe",  # 数据层搜索
            "finally:",  # 异常安全
        ]
        
        for opt in optimizations:
            if opt in content:
                print(f"✅ 优化存在: {opt}")
            else:
                print(f"❌ 优化缺失: {opt}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 搜索优化测试失败: {str(e)}")
        return False

def test_sorting_features():
    """测试排序功能"""
    print("\n=== 测试排序功能 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查排序功能
        sorting_features = [
            "class NumericTableWidgetItem",  # 数值排序类
            "def __lt__(self, other):",  # 自定义比较
            "setSortingEnabled(True)",  # 启用排序
            "sortItems(1, Qt.DescendingOrder)",  # ID降序排序
            "NumericTableWidgetItem(value)",  # 使用数值排序项
        ]
        
        for feature in sorting_features:
            if feature in content:
                print(f"✅ 排序功能存在: {feature}")
            else:
                print(f"❌ 排序功能缺失: {feature}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 排序功能测试失败: {str(e)}")
        return False

def test_code_quality():
    """测试代码质量"""
    print("\n=== 测试代码质量 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查代码质量
        quality_checks = [
            ("try:", "异常处理"),
            ("except Exception as e:", "异常捕获"),
            ("finally:", "资源清理"),
            ("self.append_vm_log", "日志记录"),
        ]
        
        for pattern, description in quality_checks:
            count = content.count(pattern)
            if count > 0:
                print(f"✅ {description}: 找到 {count} 处")
            else:
                print(f"❌ {description}: 未找到")
                return False
        
        # 检查是否有未处理的信号
        signal_patterns = [
            "itemChanged.connect",
            "blockSignals",
        ]
        
        for pattern in signal_patterns:
            count = content.count(pattern)
            print(f"📊 {pattern}: {count} 处")
        
        return True
        
    except Exception as e:
        print(f"❌ 代码质量测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 搜索信号阻止修复测试")
    print("=" * 60)
    
    # 测试信号阻止
    if not test_signal_blocking():
        print("❌ 信号阻止测试失败")
        return False
    
    # 测试搜索优化
    if not test_search_optimization():
        print("❌ 搜索优化测试失败")
        return False
    
    # 测试排序功能
    if not test_sorting_features():
        print("❌ 排序功能测试失败")
        return False
    
    # 测试代码质量
    if not test_code_quality():
        print("❌ 代码质量测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！")
    print("\n📋 修复总结:")
    print("✅ 搜索信号阻止:")
    print("  - 搜索过程中阻止表格信号")
    print("  - 高亮操作时阻止信号")
    print("  - 清除高亮时阻止信号")
    print("  - 使用finally确保信号恢复")
    
    print("\n✅ 搜索性能优化:")
    print("  - 增大批次处理大小 (200行/批)")
    print("  - 添加UI响应性处理")
    print("  - 减少不必要的UI更新")
    print("  - 删除重复的搜索代码")
    
    print("\n✅ ID排序功能:")
    print("  - 实现NumericTableWidgetItem类")
    print("  - 自定义数值比较方法")
    print("  - 默认ID降序排列")
    print("  - 支持用户点击排序")
    
    print("\n✅ 代码质量改进:")
    print("  - 完善异常处理")
    print("  - 添加资源清理")
    print("  - 删除重复代码")
    print("  - 改进日志记录")
    
    print("\n🎯 解决的问题:")
    print("  - 搜索时出现大量'准备保存'日志 → 阻止信号触发")
    print("  - 搜索卡顿问题 → 优化批次处理")
    print("  - ID排序不正确 → 实现数值排序")
    print("  - 代码重复问题 → 删除重复方法")
    
    print("\n🚀 预期效果:")
    print("  - 搜索时不再出现'准备保存'日志")
    print("  - 搜索响应更快，不卡顿")
    print("  - ID按数值大小正确排序")
    print("  - 代码更简洁，维护性更好")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
