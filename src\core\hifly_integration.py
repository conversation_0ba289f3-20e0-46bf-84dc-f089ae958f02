#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HiFly数字人快速生成模块集成
整合hifly新API到项目的数字人管理系统中
"""

import os
import sys
import asyncio
import re
import pandas as pd
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime
import logging

# 添加hifly模块到路径 - 使用多种方法确保找到正确路径
hifly_paths = [
    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'hifly'),  # 相对于当前文件
    os.path.join(os.getcwd(), 'hifly'),  # 相对于当前工作目录
    os.path.join(os.path.dirname(os.getcwd()), 'hifly'),  # 相对于父目录（处理src子目录情况）
    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'hifly'),  # 绝对路径
    os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'hifly'))  # 绝对路径计算
]

hifly_path = None
for path in hifly_paths:
    if os.path.exists(path) and os.path.isdir(path):
        hifly_path = path
        break

if hifly_path and hifly_path not in sys.path:
    # 保存原始sys.path
    original_sys_path = sys.path.copy()
    # 临时添加hifly路径进行导入，但不放在最前面以避免覆盖项目的config模块
    sys.path.append(hifly_path)

    try:
        # 尝试通过包导入
        from hifly import HiflyAsyncGenerator, HiflyConfig
        print(f"✅ 成功导入hifly模块，路径: {hifly_path}")
        # 导入成功后，移除hifly路径以避免影响其他模块
        if hifly_path in sys.path:
            sys.path.remove(hifly_path)
    except ImportError as e:
        # 第一次导入失败是正常的，因为可能在早期阶段路径还没有正确设置
        # 只在调试模式下显示详细错误信息
        if os.environ.get('DEBUG_HIFLY'):
            print(f"⚠️ 无法通过包导入hifly模块: {e}")

        # 尝试直接导入单个模块
        try:
            from async_generator import HiflyAsyncGenerator
            # HiflyConfig不是必需的，设置为None
            HiflyConfig = None
            print(f"✅ 成功直接导入hifly模块，路径: {hifly_path}")
            # 导入成功后，移除hifly路径以避免影响其他模块
            if hifly_path in sys.path:
                sys.path.remove(hifly_path)
        except ImportError as e2:
            # 只在调试模式下显示详细错误信息
            if os.environ.get('DEBUG_HIFLY'):
                print(f"❌ 无法导入hifly模块: {e2}")
                print(f"尝试的路径: {hifly_paths}")
                print(f"当前工作目录: {os.getcwd()}")
                print(f"当前文件路径: {__file__}")
            HiflyAsyncGenerator = None
            HiflyConfig = None
            # 确保清理hifly路径
            if hifly_path in sys.path:
                sys.path.remove(hifly_path)
    finally:
        # 恢复原始sys.path，确保不影响其他模块的导入
        sys.path = original_sys_path
else:
    # 只在调试模式下显示详细错误信息
    if os.environ.get('DEBUG_HIFLY'):
        print(f"❌ 未找到hifly模块路径")
    HiflyAsyncGenerator = None
    HiflyConfig = None

# 确保导入项目的config模块，而不是hifly的config模块
# 通过绝对路径导入确保正确性
try:
    # 获取项目根目录下的config模块
    import sys
    import os
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    src_dir = os.path.join(project_root, 'src')
    if src_dir not in sys.path:
        sys.path.insert(0, src_dir)
    import config
except ImportError:
    # 如果绝对路径导入失败，尝试相对导入
    from .. import config

# 尝试导入视频处理器
try:
    from .video_processor import VideoProcessor
    VIDEO_PROCESSOR_AVAILABLE = True
except ImportError:
    VIDEO_PROCESSOR_AVAILABLE = False
    VideoProcessor = None

class HiflyQuickGenerator:
    """HiFly快速模式数字人生成器"""
    
    def __init__(self, config_manager=None, log_callback=None):
        """
        初始化快速生成器
        
        Args:
            config_manager: 配置管理器
            log_callback: 日志回调函数
        """
        self.config_manager = config_manager
        self.log_callback = log_callback or print
        
        # 初始化hifly生成器
        api_token = self._get_api_token()

        # 如果HiflyAsyncGenerator为None，尝试重新导入
        global HiflyAsyncGenerator, HiflyConfig
        if HiflyAsyncGenerator is None:
            self.log_callback("HiflyAsyncGenerator为None，尝试重新导入...")
            self._reimport_hifly_modules()

        if HiflyAsyncGenerator:
            try:
                # 创建临时输出目录
                temp_output_dir = os.path.join(os.getcwd(), "temp_hifly_output")
                os.makedirs(temp_output_dir, exist_ok=True)

                self.hifly_generator = HiflyAsyncGenerator(api_token=api_token, output_dir=temp_output_dir)
                self.log_callback(f"HiFly生成器初始化成功，API token: {api_token[:10]}...")
            except Exception as e:
                self.log_callback(f"HiFly生成器初始化失败: {e}")
                self.hifly_generator = None
        else:
            self.log_callback("HiflyAsyncGenerator类未导入，hifly_generator设置为None")
            self.hifly_generator = None
        
        # 加载avatar_list.xlsx
        self.avatar_df = self._load_avatar_list()
        
        # 并发控制
        self.max_concurrent = self._get_max_concurrent()
        self.semaphore = asyncio.Semaphore(self.max_concurrent)
        
        # 任务跟踪
        self.active_tasks = {}
        self.completed_tasks = {}

        # 视频处理器
        if VIDEO_PROCESSOR_AVAILABLE:
            self.video_processor = VideoProcessor(log_callback=self.log_callback)
        else:
            self.video_processor = None
            self.log_callback("警告: 视频处理器不可用，将跳过视频裁剪功能")

    def _reimport_hifly_modules(self):
        """重新导入hifly模块"""
        global HiflyAsyncGenerator, HiflyConfig, hifly_path, hifly_paths

        self.log_callback("尝试重新导入hifly模块...")

        # 重新计算hifly路径
        hifly_paths = [
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'hifly'),
            os.path.join(os.getcwd(), 'hifly'),
            os.path.join(os.path.dirname(os.getcwd()), 'hifly'),
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'hifly'),
            os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', 'hifly'))
        ]

        hifly_path = None
        for path in hifly_paths:
            if os.path.exists(path) and os.path.isdir(path):
                hifly_path = path
                break

        if hifly_path and hifly_path not in sys.path:
            # 保存原始sys.path
            original_sys_path = sys.path.copy()
            # 将hifly路径放在最前面，确保优先导入hifly的config
            sys.path.insert(0, hifly_path)

            try:
                # 尝试通过包导入
                from hifly import HiflyAsyncGenerator as HAG, HiflyConfig as HC
                HiflyAsyncGenerator = HAG
                HiflyConfig = HC
                self.log_callback(f"✅ 重新导入hifly模块成功，路径: {hifly_path}")
            except ImportError as e:
                self.log_callback(f"⚠️ 无法通过包导入hifly模块: {e}")
                # 尝试直接导入单个模块，避免config冲突
                try:
                    # 临时移除项目的config模块，避免冲突
                    original_config_module = None
                    if 'config' in sys.modules:
                        original_config_module = sys.modules['config']
                        del sys.modules['config']

                    # 现在尝试导入hifly的模块
                    from async_generator import HiflyAsyncGenerator as HAG
                    HiflyAsyncGenerator = HAG
                    HiflyConfig = None
                    self.log_callback(f"✅ 重新直接导入hifly模块成功，路径: {hifly_path}")

                    # 恢复原始的config模块
                    if original_config_module:
                        sys.modules['config'] = original_config_module

                except Exception as e2:
                    self.log_callback(f"❌ 重新导入hifly模块失败: {e2}")
                    HiflyAsyncGenerator = None
                    HiflyConfig = None

                    # 确保恢复原始的config模块
                    if original_config_module:
                        sys.modules['config'] = original_config_module
            finally:
                # 恢复原始sys.path
                sys.path = original_sys_path
        else:
            self.log_callback(f"❌ 未找到hifly模块路径")

    def _get_api_token(self) -> str:
        """获取API令牌"""
        if self.config_manager:
            token = self.config_manager.get("hifly_api_token", "")
            if token:
                return token

        # 使用免费试用令牌
        if HiflyConfig:
            return HiflyConfig.use_free_trial()

        return "2aeda3bcefac46a3"  # 默认免费试用令牌

    def update_max_concurrent(self, count: int):
        """更新最大并发数"""
        self.max_concurrent = count
        self.semaphore = asyncio.Semaphore(count)
        self.log_callback(f"视频并发数量已更新为: {count}")
    
    def _get_max_concurrent(self) -> int:
        """获取API批次大小"""
        if self.config_manager:
            return self.config_manager.get("api_batch_size", 6)
        return 6
    
    def _load_avatar_list(self) -> Optional[pd.DataFrame]:
        """加载avatar_list.xlsx文件"""
        try:
            # 尝试多个可能的路径
            possible_paths = [
                os.path.join("data", "avatar_list.xlsx"),  # 当前目录下的data
                os.path.join("..", "data", "avatar_list.xlsx"),  # 上级目录的data
                os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "avatar_list.xlsx"),  # 相对于当前文件的路径
            ]

            self.log_callback(f"当前工作目录: {os.getcwd()}")

            avatar_file = None
            for path in possible_paths:
                self.log_callback(f"检查路径: {path} - 存在: {os.path.exists(path)}")
                if os.path.exists(path):
                    avatar_file = path
                    break

            if avatar_file:
                df = pd.read_excel(avatar_file)
                self.log_callback(f"成功加载avatar_list.xlsx，路径: {avatar_file}，共{len(df)}条记录")

                # 显示列名信息，帮助调试
                self.log_callback(f"Excel文件列名: {list(df.columns)}")

                # 显示前几行数据的ID列信息
                if 'ID' in df.columns:
                    sample_ids = df['ID'].head(5).tolist()
                    self.log_callback(f"前5个ID示例: {sample_ids}")
                else:
                    self.log_callback("警告: 未找到'ID'列")

                return df
            else:
                self.log_callback(f"警告: 在所有路径中都未找到avatar_list.xlsx文件")
                return None
        except Exception as e:
            self.log_callback(f"加载avatar_list.xlsx失败: {e}")
            return None
    
    def extract_material_id(self, filename: str) -> Optional[str]:
        """
        从文件名中提取素材ID（最后一个连续的5位数字）
        
        Args:
            filename: 文件名
            
        Returns:
            素材ID字符串，如果未找到则返回None
        """
        # 使用正则表达式查找所有5位数字
        pattern = r'(?<!\d)\d{5}(?!\d)'
        matches = re.findall(pattern, filename)
        
        if matches:
            # 返回最后一个匹配的5位数字
            return matches[-1]
        
        return None
    
    def get_video_url_by_material_id(self, material_id: str) -> Optional[str]:
        """
        根据素材ID在avatar_list.xlsx中查找对应的视频URL
        
        Args:
            material_id: 素材ID
            
        Returns:
            视频URL，如果未找到则返回None
        """
        if self.avatar_df is None:
            return None
        
        try:
            self.log_callback(f"查找素材ID: {material_id} (类型: {type(material_id)})")

            # 检查必要的列是否存在
            if 'ID' not in self.avatar_df.columns:
                self.log_callback(f"错误: Excel文件中没有'ID'列，可用列: {list(self.avatar_df.columns)}")
                return None

            if '视频URL' not in self.avatar_df.columns:
                self.log_callback(f"错误: Excel文件中没有'视频URL'列，可用列: {list(self.avatar_df.columns)}")
                return None

            # 查找ID列匹配的行
            matching_rows = self.avatar_df[self.avatar_df['ID'].astype(str) == str(material_id)]

            self.log_callback(f"匹配的行数: {len(matching_rows)}")

            if not matching_rows.empty:
                video_url = matching_rows.iloc[0]['视频URL']
                self.log_callback(f"找到素材ID {material_id} 对应的原始视频URL: {video_url}")

                # 检查并修复不完整的URL
                fixed_url = self._fix_incomplete_video_url(video_url, material_id)
                if fixed_url != video_url:
                    self.log_callback(f"已修复视频URL: {fixed_url}")

                # 验证URL是否有效（以视频扩展名结尾，支持大小写）
                if not fixed_url.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                    self.log_callback(f"警告: 视频URL格式可能无效: {fixed_url}")
                    return None

                return fixed_url
            else:
                # 显示一些相近的ID，帮助调试
                all_ids = self.avatar_df['ID'].astype(str).tolist()
                similar_ids = [id for id in all_ids if material_id in id or id in material_id][:5]
                self.log_callback(f"未找到素材ID {material_id}，相似的ID: {similar_ids}")
                return None

        except Exception as e:
            self.log_callback(f"查找视频URL失败: {e}")
            import traceback
            self.log_callback(f"错误详情: {traceback.format_exc()}")
            return None

    def _fix_incomplete_video_url(self, video_url: str, material_id: str) -> str:
        """修复不完整的视频URL"""
        try:
            video_url = str(video_url).strip()

            # 检查URL是否完整（应该以视频扩展名结尾，支持大小写）
            if video_url.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                return video_url

            # 检查是否是不完整的URL（以ID-结尾）
            if video_url.endswith(f'{material_id}-'):
                # 尝试几种可能的文件名格式
                possible_formats = [
                    f'{material_id}.mp4',           # 37330.mp4
                    f'{material_id}-video.mp4',     # 37330-video.mp4
                    f'{material_id}_video.mp4',     # 37330_video.mp4
                ]

                # 使用第一种格式（最常见）
                fixed_url = video_url[:-1] + '.mp4'  # 移除末尾的'-'，添加'.mp4'
                self.log_callback(f"修复不完整URL: {video_url} -> {fixed_url}")
                return fixed_url

            # 检查是否只是缺少扩展名（支持大小写）
            video_url_lower = video_url.lower()
            if not video_url_lower.endswith(('.mp4', '.avi', '.mov', '.mkv')):
                # 如果URL看起来像是视频文件路径，添加.mp4扩展名
                if '/video/' in video_url_lower or material_id in video_url:
                    fixed_url = video_url + '.mp4'
                    self.log_callback(f"添加扩展名: {video_url} -> {fixed_url}")
                    return fixed_url

            # 如果无法修复，返回原URL
            return video_url

        except Exception as e:
            self.log_callback(f"修复URL时出错: {e}")
            return video_url

    def normalize_layout(self, layout: str) -> str:
        """
        规范化版型字段

        Args:
            layout: 原始版型字符串

        Returns:
            规范化后的版型（横/竖）
        """
        if not layout:
            return ""

        layout = layout.strip()
        if "横版" in layout or "横" in layout:
            return "横"
        elif "竖版" in layout or "竖" in layout:
            return "竖"
        else:
            return layout  # 保持原样

    def remove_layout_suffix(self, filename: str) -> str:
        """
        删除文件名末尾的版型后缀

        Args:
            filename: 原始文件名（不含扩展名）

        Returns:
            删除版型后缀后的文件名
        """
        if not filename:
            return filename

        # 定义需要删除的版型后缀模式
        layout_suffixes = [
            "-横竖版", "-横竖", "-横版", "-竖版", "-横", "-竖"
        ]

        # 按长度从长到短排序，确保优先匹配更长的后缀
        layout_suffixes.sort(key=len, reverse=True)

        # 删除匹配的版型后缀
        for suffix in layout_suffixes:
            if filename.endswith(suffix):
                return filename[:-len(suffix)]

        return filename
    
    async def process_audio_file(self, audio_file_path: str, output_dir: str) -> Dict[str, Any]:
        """
        处理单个音频文件，生成数字人视频

        Args:
            audio_file_path: 音频文件路径
            output_dir: 输出目录

        Returns:
            处理结果字典
        """
        if not self.hifly_generator:
            return {"error": "HiFly模块未正确初始化"}

        # 验证音频文件
        if not os.path.exists(audio_file_path):
            return {"error": f"音频文件不存在: {audio_file_path}"}

        # 检查文件大小（至少应该大于1KB）
        file_size = os.path.getsize(audio_file_path)
        if file_size < 1024:
            return {"error": f"音频文件太小 ({file_size}字节)，可能不是有效的音频文件"}

        # 检查文件扩展名
        if not audio_file_path.lower().endswith(('.mp3', '.wav', '.m4a')):
            return {"error": f"不支持的音频格式: {audio_file_path}"}

        try:
            # 从文件名提取信息
            filename = os.path.basename(audio_file_path)
            name_without_ext = os.path.splitext(filename)[0]
            
            # 提取素材ID
            material_id = self.extract_material_id(filename)
            if not material_id:
                return {"error": f"无法从文件名 {filename} 中提取素材ID"}
            
            # 获取视频URL
            video_url = self.get_video_url_by_material_id(material_id)
            if not video_url:
                return {"error": f"无法找到素材ID {material_id} 对应的视频URL"}
            
            # 使用信号量控制并发
            async with self.semaphore:
                self.log_callback(f"开始处理音频文件: {filename}")
                
                # 提交视频生成任务
                result = await self.hifly_generator.submit_video_generation(
                    reference_video=video_url,
                    audio_file=audio_file_path,
                    title=name_without_ext
                )
                
                if "error" in result:
                    return result
                
                task_id = result.get("task_id")
                if not task_id:
                    return {"error": "未获取到任务ID"}
                
                # 记录任务
                self.active_tasks[task_id] = {
                    "audio_file": audio_file_path,
                    "filename": filename,
                    "material_id": material_id,
                    "video_url": video_url,
                    "output_dir": output_dir,
                    "submit_time": datetime.now(),
                    "status": "submitted"
                }
                
                self.log_callback(f"任务已提交: {task_id} (文件: {filename})")
                
                return {
                    "task_id": task_id,
                    "filename": filename,
                    "material_id": material_id,
                    "status": "submitted"
                }
                
        except Exception as e:
            return {"error": f"处理音频文件失败: {str(e)}"}
    
    async def check_task_status(self, task_id: str) -> Dict[str, Any]:
        """检查任务状态"""
        if not self.hifly_generator:
            return {"error": "HiFly模块未正确初始化"}
        
        try:
            status_result = await self.hifly_generator.check_task_status(task_id)
            
            # 更新本地任务状态
            if task_id in self.active_tasks:
                self.active_tasks[task_id]["status"] = status_result.get("status", "unknown")
                self.active_tasks[task_id]["last_check"] = datetime.now()
            
            return status_result
            
        except Exception as e:
            return {"error": f"检查任务状态失败: {str(e)}"}
    
    async def download_completed_video(self, task_id: str) -> Dict[str, Any]:
        """下载完成的视频"""
        if not self.hifly_generator:
            return {"error": "HiFly模块未正确初始化"}
        
        try:
            if task_id not in self.active_tasks:
                return {"error": f"未找到任务 {task_id}"}
            
            task_info = self.active_tasks[task_id]
            output_dir = task_info["output_dir"]
            filename = task_info["filename"]
            
            # 下载视频到临时位置
            temp_filename = f"{os.path.splitext(filename)[0]}.mp4"
            download_result = await self.hifly_generator.download_completed_video(
                task_id=task_id,
                output_filename=temp_filename
            )
            
            if "error" not in download_result:
                # 获取下载的原始视频文件
                raw_video_path = download_result.get("output_path")  # 注意：使用output_path而不是output_file

                self.log_callback(f"视频下载成功: {raw_video_path}")

                if raw_video_path and os.path.exists(raw_video_path):
                    # 从文件名提取版型信息
                    layout = self.normalize_layout(self.extract_layout_from_filename(filename))

                    # 如果没有识别到版型信息，按照横版处理
                    if not layout:
                        layout = "横"
                        self.log_callback(f"📋 未识别到版型信息，按照横版处理: {filename}")

                    # 使用传入的output_dir，它应该已经是正确的路径
                    # output_dir 应该是类似：feiyingshuziren/创作任务_YYYYMMDD/快速
                    final_output_dir = output_dir
                    os.makedirs(final_output_dir, exist_ok=True)

                    # 统一处理：不管有没有版型信息，都直接移动到快速文件夹
                    # 构建最终文件名和路径（删除版型后缀）
                    base_name = os.path.splitext(filename)[0]
                    # 删除文件名末尾的版型后缀（-横、-竖、-横版、-竖版等）
                    clean_base_name = self.remove_layout_suffix(base_name)
                    final_filename = f"{clean_base_name}.mp4"
                    final_output_path = os.path.join(final_output_dir, final_filename)

                    # 根据设置决定视频处理方案
                    try:
                        # 获取视频处理方案配置
                        video_processing_mode = self.config_manager.get("video_processing_mode", "watermark_removal") if self.config_manager else "watermark_removal"
                        self.log_callback(f"🔧 当前视频处理方案: {video_processing_mode}, 版型: {layout}")

                        # 只对横版视频根据设置选择处理方案，竖版视频始终使用裁剪
                        if layout == "横" and video_processing_mode == "watermark_removal":
                            # 横版视频使用水印去除方案 - 原始视频保存在临时位置，去水印后保存到快速文件夹
                            self.log_callback(f"🎨 横版视频将使用AI水印去除方案，暂存原始视频到临时位置")

                            # 创建临时目录保存原始视频 - 在当日创作任务文件夹下，不是快速文件夹内
                            # final_output_path 是 .../创作任务_20250715/快速/filename.mp4
                            # 我们需要临时目录在 .../创作任务_20250715/temp_original_videos/
                            quick_folder = os.path.dirname(final_output_path)  # .../创作任务_20250715/快速
                            main_folder = os.path.dirname(quick_folder)  # .../创作任务_20250715
                            temp_dir = os.path.join(main_folder, "temp_original_videos")
                            os.makedirs(temp_dir, exist_ok=True)

                            # 原始视频保存在临时目录
                            temp_video_path = os.path.join(temp_dir, os.path.basename(final_output_path))
                            import shutil
                            shutil.move(raw_video_path, temp_video_path)
                            self.log_callback(f"📁 原始视频已暂存到: {temp_video_path}")

                            # 标记为未处理，后续将进行水印去除
                            download_result["output_file"] = temp_video_path  # 传递临时路径给水印去除
                            download_result["final_output_path"] = final_output_path  # 最终输出路径
                            download_result["layout"] = layout if layout else ""
                            download_result["processed"] = False  # 标记为未处理，需要后续水印去除
                            download_result["processing_mode"] = "watermark_removal"
                        else:
                            # 竖版视频或横版选择裁剪方案 - 进行裁剪处理
                            from .video_processor import VideoProcessor
                            video_processor = VideoProcessor(log_callback=self.log_callback)

                            self.log_callback(f"✂️ 开始裁剪视频: {filename} (版型: {layout or '无'})")
                            crop_success = video_processor.crop_video(raw_video_path, final_output_path, layout)

                            if crop_success:
                                self.log_callback(f"✅ 视频裁剪并保存成功: {final_output_path}")

                                # 删除原始未裁剪的视频文件
                                try:
                                    os.remove(raw_video_path)
                                    self.log_callback(f"🗑️ 已删除原始视频文件: {raw_video_path}")
                                except Exception as e:
                                    self.log_callback(f"⚠️ 删除原始视频文件失败: {e}")

                                # 更新结果中的输出文件路径
                                download_result["output_file"] = final_output_path
                                download_result["layout"] = layout if layout else ""
                                download_result["processed"] = True  # 标记为已处理（裁剪）
                                download_result["processing_mode"] = "cropping"
                            else:
                                # 裁剪失败，使用原始视频
                                self.log_callback(f"❌ 视频裁剪失败，使用原始视频")
                                import shutil
                                shutil.move(raw_video_path, final_output_path)
                                self.log_callback(f"📁 原始视频已保存到: {final_output_path}")

                                # 更新结果中的输出文件路径
                                download_result["output_file"] = final_output_path
                                download_result["layout"] = layout if layout else ""
                                download_result["processed"] = False  # 标记为未处理
                                download_result["processing_mode"] = "none"

                    except Exception as e:
                        self.log_callback(f"❌ 视频处理失败: {e}")
                        # 发生错误时，尝试保存原始视频
                        try:
                            import shutil
                            shutil.move(raw_video_path, final_output_path)
                            self.log_callback(f"📁 原始视频已保存到: {final_output_path}")
                            download_result["output_file"] = final_output_path
                            download_result["layout"] = layout if layout else ""
                            download_result["processed"] = False
                        except Exception as move_error:
                            self.log_callback(f"❌ 保存原始视频也失败: {move_error}")
                            download_result["error"] = f"视频处理和保存都失败: {e}, {move_error}"
                            return download_result

                # 移动到已完成任务
                self.completed_tasks[task_id] = self.active_tasks.pop(task_id)
                self.completed_tasks[task_id]["download_time"] = datetime.now()
                self.completed_tasks[task_id]["output_file"] = download_result.get("output_file")
                self.completed_tasks[task_id]["layout"] = download_result.get("layout", "")

            return download_result
            
        except Exception as e:
            return {"error": f"下载视频失败: {str(e)}"}

    async def process_audio_folder(self, folder_path: str, output_dir: str,
                                 progress_callback=None) -> Dict[str, Any]:
        """
        批量处理音频文件夹（流水线模式）

        Args:
            folder_path: 音频文件夹路径
            output_dir: 输出目录
            progress_callback: 进度回调函数

        Returns:
            处理结果汇总
        """
        if not os.path.exists(folder_path):
            return {"error": f"文件夹不存在: {folder_path}"}

        # 获取所有音频文件
        audio_files = []
        for file in os.listdir(folder_path):
            if file.lower().endswith(('.mp3', '.wav', '.m4a')):
                audio_files.append(os.path.join(folder_path, file))

        if not audio_files:
            return {"error": f"文件夹中未找到音频文件: {folder_path}"}

        self.log_callback(f"开始批量处理 {len(audio_files)} 个音频文件")

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        results = {
            "total_files": len(audio_files),
            "submitted_tasks": [],
            "failed_submissions": [],
            "start_time": datetime.now()
        }

        # 批量提交任务
        for i, audio_file in enumerate(audio_files):
            try:
                if progress_callback:
                    progress_callback(i, len(audio_files), f"提交任务: {os.path.basename(audio_file)}")

                result = await self.process_audio_file(audio_file, output_dir)

                if "error" in result:
                    results["failed_submissions"].append({
                        "file": audio_file,
                        "error": result["error"]
                    })
                else:
                    results["submitted_tasks"].append(result)

                # 短暂延迟避免API限制
                await asyncio.sleep(0.5)

            except Exception as e:
                results["failed_submissions"].append({
                    "file": audio_file,
                    "error": str(e)
                })

        results["submission_time"] = datetime.now()
        self.log_callback(f"任务提交完成: 成功 {len(results['submitted_tasks'])} 个，失败 {len(results['failed_submissions'])} 个")

        return results

    async def monitor_and_download_all(self, check_interval: int = 30,
                                     timeout_minutes: int = 30,
                                     progress_callback=None) -> Dict[str, Any]:
        """
        监控所有活跃任务并下载完成的视频

        Args:
            check_interval: 检查间隔（秒）
            timeout_minutes: 超时时间（分钟）
            progress_callback: 进度回调函数

        Returns:
            监控结果汇总
        """
        if not self.active_tasks:
            return {"message": "没有活跃的任务需要监控"}

        start_time = datetime.now()
        timeout_seconds = timeout_minutes * 60

        results = {
            "completed_downloads": [],
            "failed_downloads": [],
            "timeout_tasks": [],
            "start_time": start_time
        }

        self.log_callback(f"开始监控 {len(self.active_tasks)} 个任务")

        while self.active_tasks:
            current_time = datetime.now()
            elapsed_seconds = (current_time - start_time).total_seconds()

            # 检查超时
            if elapsed_seconds > timeout_seconds:
                results["timeout_tasks"] = list(self.active_tasks.keys())
                self.log_callback(f"监控超时，剩余 {len(self.active_tasks)} 个任务未完成")
                break

            # 检查所有活跃任务的状态
            tasks_to_check = list(self.active_tasks.keys())

            for task_id in tasks_to_check:
                try:
                    status_result = await self.check_task_status(task_id)

                    if "error" in status_result:
                        self.log_callback(f"检查任务 {task_id} 状态失败: {status_result['error']}")
                        continue

                    status = status_result.get("status", "unknown")
                    task_info = self.active_tasks[task_id]
                    filename = task_info["filename"]

                    if progress_callback:
                        remaining = len(self.active_tasks)
                        progress_callback(
                            len(results["completed_downloads"]),
                            len(results["completed_downloads"]) + remaining,
                            f"监控中: {filename} - {status}"
                        )

                    if status == "completed":
                        # 任务完成，下载视频
                        self.log_callback(f"任务 {task_id} 完成，开始下载视频: {filename}")

                        download_result = await self.download_completed_video(task_id)

                        if "error" in download_result:
                            results["failed_downloads"].append({
                                "task_id": task_id,
                                "filename": filename,
                                "error": download_result["error"]
                            })
                        else:
                            results["completed_downloads"].append({
                                "task_id": task_id,
                                "filename": filename,
                                "output_file": download_result.get("output_file")
                            })

                    elif status == "failed":
                        # 任务失败
                        results["failed_downloads"].append({
                            "task_id": task_id,
                            "filename": filename,
                            "error": "任务生成失败"
                        })
                        self.active_tasks.pop(task_id, None)

                except Exception as e:
                    self.log_callback(f"处理任务 {task_id} 时出错: {str(e)}")

            # 等待下次检查
            if self.active_tasks:  # 还有任务在进行中
                await asyncio.sleep(check_interval)

        results["end_time"] = datetime.now()
        results["total_duration"] = (results["end_time"] - results["start_time"]).total_seconds()

        self.log_callback(f"监控完成: 成功下载 {len(results['completed_downloads'])} 个视频")

        return results

    def get_active_tasks_info(self) -> List[Dict[str, Any]]:
        """获取所有活跃任务信息"""
        return [
            {
                "task_id": task_id,
                "filename": info["filename"],
                "material_id": info["material_id"],
                "status": info["status"],
                "submit_time": info["submit_time"].strftime("%Y-%m-%d %H:%M:%S")
            }
            for task_id, info in self.active_tasks.items()
        ]

    def get_completed_tasks_info(self) -> List[Dict[str, Any]]:
        """获取所有已完成任务信息"""
        return [
            {
                "task_id": task_id,
                "filename": info["filename"],
                "material_id": info["material_id"],
                "output_file": info.get("output_file", ""),
                "download_time": info.get("download_time", "").strftime("%Y-%m-%d %H:%M:%S") if info.get("download_time") else ""
            }
            for task_id, info in self.completed_tasks.items()
        ]

    def extract_layout_from_filename(self, filename: str) -> str:
        """从文件名中提取版型信息"""
        # 查找横版或竖版标识
        if "横版" in filename or "横" in filename:
            return "横"
        elif "竖版" in filename or "竖" in filename:
            return "竖"
        else:
            return ""

    async def get_account_info(self) -> Dict[str, Any]:
        """获取账户信息和剩余积分"""
        if not self.hifly_generator:
            return {"error": "HiFly模块未正确初始化"}

        try:
            # 使用HiFly API查询账户信息
            import httpx
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.hifly_generator.client.api_base_url}/account/credit",
                    headers=self.hifly_generator.client._get_auth_headers(),
                    timeout=self.hifly_generator.client.timeout
                )

                if response.status_code != 200:
                    return {
                        "error": f"查询账户信息失败: {response.status_code}",
                        "details": response.json()
                    }

                account_data = response.json()
                return {
                    "success": True,
                    "credits": account_data.get("left", 0),  # 剩余积分
                    "total": account_data.get("total", 0),   # 总积分
                    "used": account_data.get("used", 0),     # 已使用积分
                    "raw_data": account_data
                }

        except Exception as e:
            return {"error": f"查询账户信息失败: {str(e)}"}
