"""
调试最近上传的重命名筛选问题
"""

import os
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>


def debug_recent_upload_filter():
    """调试最近上传的重命名筛选"""
    print("=" * 60)
    print("🔍 调试最近上传的重命名筛选问题")
    print("=" * 60)
    
    avatar_list_path = "data/avatar_list.xlsx"
    if not os.path.exists(avatar_list_path):
        print("❌ 数据文件不存在")
        return
    
    # 读取Excel文件
    df = pd.read_excel(avatar_list_path)
    print(f"📊 总数据量: {len(df)} 条")
    
    # 检查最近更新的记录
    print(f"\n🔍 最近更新的5条记录:")
    df_sorted = df.sort_values('更新日期', ascending=False)
    recent_records = df_sorted.head(5)
    for i, (_, row) in enumerate(recent_records.iterrows(), 1):
        video_id = row.get('ID', '')
        upload_status = row.get('是否上传飞影', '')
        rename_status = row.get('是否重命名', '')
        update_date = row.get('更新日期', '')
        actor_name = row.get('拍摄演员名称', '')
        print(f"  {i}. ID {video_id} ({actor_name}): 上传={upload_status}, 重命名={repr(rename_status)}, 日期={update_date}")
    
    # 检查"是否重命名"列的值类型
    print(f"\n🔍 '是否重命名'列值统计:")
    rename_values = df["是否重命名"].value_counts(dropna=False)
    for value, count in rename_values.items():
        print(f"  {repr(value)}: {count} 条")
    
    # 模拟筛选逻辑
    print(f"\n🔍 模拟筛选逻辑:")
    
    # 计算日期范围
    today = datetime.now().date()
    three_days_ago = today - timedelta(days=3)
    print(f"📅 今天: {today}")
    print(f"📅 3天前: {three_days_ago}")
    
    # 转换更新日期列
    df["更新日期"] = pd.to_datetime(df["更新日期"], errors='coerce').dt.date
    
    # 分别测试各个筛选条件
    mask_upload = (df["是否上传飞影"] == "是")
    mask_rename = (
        (df["是否重命名"].isna()) |  # NaN值
        (df["是否重命名"] == "") |   # 空字符串
        (df["是否重命名"] != "是")   # 其他值（如"重命名失败"）
    )
    mask_date = (df["更新日期"] >= three_days_ago)
    
    print(f"🔍 筛选条件分解:")
    print(f"  上传飞影='是': {mask_upload.sum()} 条")
    print(f"  重命名状态需要处理: {mask_rename.sum()} 条")
    print(f"  更新日期>={three_days_ago}: {mask_date.sum()} 条")
    
    # 组合筛选
    mask_combined = mask_upload & mask_rename & mask_date
    print(f"  组合筛选结果: {mask_combined.sum()} 条")
    
    # 显示符合条件的记录
    if mask_combined.sum() > 0:
        print(f"\n📋 符合条件的记录:")
        result_df = df[mask_combined]
        for i, (_, row) in enumerate(result_df.iterrows(), 1):
            video_id = row.get('ID', '')
            actor_name = row.get('拍摄演员名称', '')
            upload_status = row.get('是否上传飞影', '')
            rename_status = row.get('是否重命名', '')
            update_date = row.get('更新日期', '')
            print(f"  {i}. {actor_name}-{video_id}: 上传={upload_status}, 重命名={repr(rename_status)}, 日期={update_date}")
    else:
        print(f"\n❌ 没有符合条件的记录")
        
        # 分析原因
        print(f"\n🔍 分析原因:")
        
        # 检查是否有上传但未重命名的记录
        mask_upload_not_rename = mask_upload & mask_rename
        if mask_upload_not_rename.sum() > 0:
            print(f"  有 {mask_upload_not_rename.sum()} 条已上传但未重命名的记录")
            
            # 检查这些记录的日期
            upload_not_rename_df = df[mask_upload_not_rename]
            print(f"  这些记录的更新日期:")
            for _, row in upload_not_rename_df.iterrows():
                video_id = row.get('ID', '')
                update_date = row.get('更新日期', '')
                print(f"    ID {video_id}: {update_date}")
                
            # 检查日期筛选是否过于严格
            recent_dates = upload_not_rename_df['更新日期'].value_counts().head(5)
            print(f"  最近的更新日期分布:")
            for date, count in recent_dates.items():
                is_in_range = date >= three_days_ago if pd.notna(date) else False
                print(f"    {date}: {count} 条 {'✅' if is_in_range else '❌'}")
        else:
            print(f"  没有已上传但未重命名的记录")
    
    # 建议解决方案
    print(f"\n💡 建议解决方案:")
    if mask_combined.sum() == 0:
        if mask_upload_not_rename.sum() > 0:
            print(f"  1. 检查日期筛选是否过于严格")
            print(f"  2. 考虑扩大日期范围（如7天）")
            print(f"  3. 或者检查上传完成后是否正确更新了日期")
        else:
            print(f"  1. 检查是否有需要重命名的数据")
            print(f"  2. 检查筛选条件是否正确")


if __name__ == "__main__":
    debug_recent_upload_filter()
