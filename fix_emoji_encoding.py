"""
修复emoji编码问题
"""

import os
import re


def fix_emoji_encoding():
    """修复video_management_runner.py中的emoji编码问题"""
    print("🔧 修复emoji编码问题")
    
    file_path = "src/video_management_runner.py"
    
    # emoji替换映射
    emoji_replacements = {
        '📝': '[LOG]',
        '🚀': '[START]',
        '🎯': '[TARGET]',
        '⏰': '[TIME]',
        '✅': '[SUCCESS]',
        '❌': '[ERROR]',
        '📋': '[LIST]',
        '📤': '[UPLOAD]',
        '🏷️': '[RENAME]',
        '📊': '[STATS]',
        '🎉': '[COMPLETE]',
        '⚠️': '[WARNING]',
        '🏁': '[FINISH]',
        '🔄': '[PROCESS]',
        '🔍': '[SEARCH]',
        'ℹ️': '[INFO]',
        '✏️': '[EDIT]',
        '📈': '[RATE]'
    }
    
    try:
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"原文件大小: {len(content)} 字符")
        
        # 替换emoji
        modified_content = content
        replacement_count = 0
        
        for emoji, replacement in emoji_replacements.items():
            if emoji in modified_content:
                count = modified_content.count(emoji)
                modified_content = modified_content.replace(emoji, replacement)
                replacement_count += count
                print(f"替换 {emoji} -> {replacement}: {count} 次")
        
        print(f"总共替换: {replacement_count} 个emoji")
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print(f"✅ 文件已更新: {file_path}")
        print(f"新文件大小: {len(modified_content)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False


def test_encoding():
    """测试编码问题"""
    print("\n🧪 测试编码")
    
    test_strings = [
        "📝 日志文件",
        "🚀 开始任务", 
        "✅ 任务成功",
        "❌ 任务失败"
    ]
    
    for test_str in test_strings:
        try:
            # 测试GBK编码
            test_str.encode('gbk')
            print(f"✅ GBK编码正常: {test_str}")
        except UnicodeEncodeError:
            print(f"❌ GBK编码失败: {test_str}")
            # 显示安全替换
            safe_str = test_str.encode('ascii', 'ignore').decode('ascii')
            print(f"   安全替换: {safe_str}")


def create_test_script():
    """创建测试脚本"""
    print("\n📝 创建测试脚本")
    
    test_content = '''"""
测试修复后的脚本
"""

import sys
import os

# 添加src路径
sys.path.append('src')

def test_fixed_script():
    """测试修复后的脚本"""
    try:
        # 测试导入
        from video_management_runner import log_message
        
        print("✅ 脚本导入成功")
        
        # 测试日志函数
        log_message("[TEST] 测试日志消息")
        log_message("[SUCCESS] 编码问题已修复")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_fixed_script()
'''
    
    with open("test_fixed_script.py", 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print("✅ 测试脚本已创建: test_fixed_script.py")


if __name__ == "__main__":
    print("=" * 60)
    print("🔧 修复video_management_runner.py编码问题")
    print("=" * 60)
    
    # 测试当前编码问题
    test_encoding()
    
    # 修复emoji编码
    if fix_emoji_encoding():
        print("\n🎉 修复完成！")
        
        # 创建测试脚本
        create_test_script()
        
        print("\n📋 下一步:")
        print("1. 程序内定时任务现在应该可以正常执行")
        print("2. 不会再有Unicode编码错误")
        print("3. 日志中的emoji已替换为文本标记")
        print("4. 可以创建新的定时任务进行测试")
    else:
        print("\n❌ 修复失败，请检查错误信息")
    
    print("=" * 60)
