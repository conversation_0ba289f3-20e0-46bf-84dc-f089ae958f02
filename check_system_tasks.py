"""
检查系统任务创建情况
"""

import subprocess
import sys
import os


def check_system_tasks():
    """检查系统任务计划程序中的任务"""
    print("=" * 60)
    print("🔍 检查系统任务计划程序")
    print("=" * 60)
    
    try:
        # 查询所有任务
        print("📋 查询所有任务...")
        result = subprocess.run(
            ['schtasks', '/query', '/fo', 'csv'],
            capture_output=True,
            text=True,
            encoding='gbk'  # Windows中文系统使用gbk编码
        )
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            print(f"✅ 找到 {len(lines)-1} 个任务")
            
            # 查找视频管理相关的任务
            vm_tasks = []
            for line in lines[1:]:  # 跳过标题行
                if 'VideoManagement' in line or 'FishWin' in line:
                    vm_tasks.append(line)
            
            if vm_tasks:
                print(f"\n🎯 找到 {len(vm_tasks)} 个相关任务:")
                for task in vm_tasks:
                    parts = task.split(',')
                    if len(parts) >= 2:
                        task_name = parts[0].strip('"')
                        status = parts[2].strip('"') if len(parts) > 2 else "未知"
                        print(f"  📝 {task_name} - 状态: {status}")
            else:
                print("\n⚠️ 没有找到 VideoManagement 或 FishWin 相关的任务")
                
                # 显示所有任务名称（前20个）
                print("\n📋 系统中的任务（前20个）:")
                for i, line in enumerate(lines[1:21]):
                    parts = line.split(',')
                    if len(parts) >= 1:
                        task_name = parts[0].strip('"')
                        print(f"  {i+1}. {task_name}")
        else:
            print(f"❌ 查询任务失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 检查系统任务异常: {e}")


def check_vbs_files():
    """检查VBS文件是否存在"""
    print("\n" + "=" * 40)
    print("📄 检查VBS文件")
    print("=" * 40)
    
    vbs_files = [
        "run_video_management_scheduled.vbs",
        "run_digital_human_scheduled.vbs"
    ]
    
    for vbs_file in vbs_files:
        if os.path.exists(vbs_file):
            print(f"✅ {vbs_file} 存在")
            
            # 读取文件内容
            try:
                with open(vbs_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"📝 文件内容:")
                print(content[:200] + "..." if len(content) > 200 else content)
            except Exception as e:
                print(f"❌ 读取文件失败: {e}")
        else:
            print(f"❌ {vbs_file} 不存在")


def check_bat_files():
    """检查BAT文件是否存在"""
    print("\n" + "=" * 40)
    print("📄 检查BAT文件")
    print("=" * 40)
    
    bat_files = [
        "run_video_management_auto.bat",
        "run_digital_human_auto.bat"
    ]
    
    for bat_file in bat_files:
        if os.path.exists(bat_file):
            print(f"✅ {bat_file} 存在")
            
            # 读取文件内容
            try:
                with open(bat_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"📝 文件内容:")
                print(content[:300] + "..." if len(content) > 300 else content)
            except Exception as e:
                print(f"❌ 读取文件失败: {e}")
        else:
            print(f"❌ {bat_file} 不存在")


def test_schtasks_command():
    """测试schtasks命令"""
    print("\n" + "=" * 40)
    print("🧪 测试schtasks命令")
    print("=" * 40)
    
    # 测试创建一个简单的任务
    test_task_name = "TestVideoManagement"
    
    try:
        # 先删除可能存在的测试任务
        print(f"🗑️ 删除可能存在的测试任务...")
        subprocess.run(
            ['schtasks', '/delete', '/tn', test_task_name, '/f'],
            capture_output=True,
            text=True
        )
        
        # 创建测试任务
        print(f"📝 创建测试任务...")
        create_cmd = [
            'schtasks', '/create', 
            '/tn', test_task_name,
            '/tr', 'notepad.exe',
            '/sc', 'once',
            '/st', '23:59',
            '/f'
        ]
        
        result = subprocess.run(create_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ 测试任务创建成功")
            
            # 查询测试任务
            query_result = subprocess.run(
                ['schtasks', '/query', '/tn', test_task_name],
                capture_output=True,
                text=True
            )
            
            if query_result.returncode == 0:
                print(f"✅ 测试任务查询成功")
            else:
                print(f"❌ 测试任务查询失败: {query_result.stderr}")
            
            # 删除测试任务
            delete_result = subprocess.run(
                ['schtasks', '/delete', '/tn', test_task_name, '/f'],
                capture_output=True,
                text=True
            )
            
            if delete_result.returncode == 0:
                print(f"✅ 测试任务删除成功")
            else:
                print(f"❌ 测试任务删除失败: {delete_result.stderr}")
                
        else:
            print(f"❌ 测试任务创建失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 测试schtasks命令异常: {e}")


def debug_system_task_creation():
    """调试系统任务创建问题"""
    print("\n" + "=" * 40)
    print("🔧 调试系统任务创建问题")
    print("=" * 40)
    
    print("可能的问题:")
    print("1. 权限不足 - 需要管理员权限创建系统任务")
    print("2. VBS文件路径错误")
    print("3. BAT文件不存在")
    print("4. schtasks命令参数错误")
    print("5. 任务名称冲突")
    print("")
    
    print("解决方案:")
    print("1. 以管理员身份运行程序")
    print("2. 检查VBS和BAT文件是否存在")
    print("3. 检查文件路径是否正确")
    print("4. 检查schtasks命令语法")
    print("5. 使用唯一的任务名称")


if __name__ == "__main__":
    check_system_tasks()
    check_vbs_files()
    check_bat_files()
    test_schtasks_command()
    debug_system_task_creation()
