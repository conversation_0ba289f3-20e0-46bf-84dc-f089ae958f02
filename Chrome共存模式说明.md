# Chrome共存模式说明

## 问题描述

用户反馈：当打开普通Chrome窗口后，程序再次点击"素材更新"时会强制关闭现有Chrome窗口，影响正常使用。

## 解决方案

实现了**Chrome共存模式**，让程序能够在不影响现有Chrome窗口的情况下正常工作。

## 核心改进

### 🔍 智能检测现有Chrome进程
```python
def check_existing_chrome_processes(self) -> bool:
    """检查是否有现有的Chrome进程在使用默认用户数据目录"""
    # 检测现有Chrome进程，避免冲突
```

### 🔄 独立调试环境
当检测到现有Chrome进程时：
1. **创建独立调试目录**：复制关键用户数据到临时目录
2. **使用不同调试端口**：自动查找可用端口（9222-9226）
3. **保留现有窗口**：不强制关闭用户的Chrome窗口

### 📋 关键数据复制
```python
def copy_essential_chrome_data(self, source_dir: str, target_dir: str) -> bool:
    """复制关键的Chrome用户数据到独立目录"""
    essential_items = [
        "Default/Cookies",           # 登录Cookie
        "Default/Login Data",        # 登录信息
        "Default/Web Data",          # 网站数据
        "Default/Preferences",       # 用户偏好
        "Default/Secure Preferences", # 安全偏好
        "Local State"                # 本地状态
    ]
```

### 🔌 动态端口分配
```python
def find_available_debug_port(self) -> int:
    """查找可用的调试端口"""
    ports_to_try = [9222, 9223, 9224, 9225, 9226]
    # 自动查找未被占用的端口
```

## 工作流程

### 场景1：无现有Chrome进程
```
1. 检测到无现有Chrome进程
2. 直接使用默认用户数据目录
3. 使用默认调试端口9222
4. 启动Chrome调试实例
```

### 场景2：有现有Chrome进程
```
1. 检测到现有Chrome进程使用相同用户数据目录
2. 查找可用的调试端口（9223, 9224等）
3. 创建独立的临时调试目录
4. 复制关键用户数据（Cookie、登录信息等）
5. 启动独立的Chrome调试实例
6. 现有Chrome窗口不受影响
```

## 日志示例

### 无冲突情况
```
🔍 找到Chrome用户数据目录: C:\Users\<USER>\AppData\Local\Google\Chrome\User Data
🔍 检测到现有Chrome进程: 否
🚀 启动Chrome调试实例，PID: 12345, 端口: 9222
```

### 有冲突情况
```
🔍 检测到现有Chrome进程使用相同用户数据目录
🔄 将尝试使用独立的调试配置，不影响现有窗口
🔍 找到可用调试端口: 9223
📋 已复制: Default/Cookies
📋 已复制: Default/Login Data
✅ 已复制 5 个关键数据文件
✅ 已创建独立调试目录: C:\Users\<USER>\AppData\Local\Temp\guangliu-chrome-debug-abc123
🚀 启动Chrome调试实例，PID: 67890, 端口: 9223
```

## 技术细节

### 端口冲突处理
- 默认尝试端口9222
- 如果被占用，依次尝试9223、9224、9225、9226
- 使用socket检测端口可用性

### 数据隔离
- 创建临时调试目录：`guangliu-chrome-debug-{随机ID}`
- 只复制必要的登录和偏好数据
- 不影响原始Chrome用户数据

### 进程管理
- 只在使用独立目录时清理调试进程
- 保留现有Chrome进程不被强制关闭
- 使用进程组隔离避免信号传播

## 用户体验改进

### ✅ 无干扰使用
- 可以同时使用普通Chrome浏览器和程序的素材更新功能
- 现有Chrome窗口和标签页不会被关闭
- 登录状态在两个实例间保持同步

### ✅ 自动适应
- 程序自动检测环境并选择最佳策略
- 无需用户手动配置或干预
- 智能处理端口冲突和数据冲突

### ✅ 状态保持
- 即使使用独立目录，登录状态仍然保持
- Cookie和用户偏好正确复制
- 无需重新登录

## 安全考虑

### 🔒 数据安全
- 临时目录在程序结束后自动清理
- 只复制必要的登录数据，不复制敏感信息
- 不修改原始Chrome用户数据

### 🔒 进程隔离
- 独立的调试端口避免冲突
- 独立的用户数据目录避免数据污染
- 进程组隔离避免意外终止

## 故障排除

### 如果仍然出现冲突
1. 手动关闭所有Chrome窗口
2. 使用"重置登录"功能清理数据
3. 重新启动程序

### 如果登录状态丢失
1. 检查是否成功复制了关键数据文件
2. 确认原始Chrome中的登录状态有效
3. 使用"重置登录"后重新登录

现在程序支持真正的Chrome共存模式，用户可以安心使用！
