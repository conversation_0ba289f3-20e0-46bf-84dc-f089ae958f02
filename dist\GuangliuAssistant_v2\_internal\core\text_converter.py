#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文本转换器，用于处理文案转换功能
"""

import re
import os
import json
from datetime import datetime

class TextConverter:
    """文本转换器类，用于处理文案转换功能"""
    
    def __init__(self):
        """初始化文本转换器"""
        # 默认替换规则
        self.default_replacement_rules = [
            ("种牙", "众牙"),
            ("POS", "pos"),
            ("Ta", "他"),
        ]
        
        # 加载替换规则
        self.replacement_rules = self.load_replacement_rules()
        
        # 默认启用数字转换
        self.convert_numbers = True
    
    def load_replacement_rules(self):
        """加载替换规则，优先使用外部配置，如不存在则使用内置规则"""
        try:
            # 尝试从应用程序目录加载规则文件
            app_dir = self.get_app_dir()
            config_path = os.path.join(app_dir, "text_extractor_rules.json")
            print(f"尝试加载配置文件: {config_path}")
            
            if os.path.exists(config_path):
                print(f"找到配置文件，尝试读取...")
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # 加载规则
                    if "rules" in config:
                        rules = config["rules"]
                        print(f"成功从配置文件加载了 {len(rules)} 条规则")
                        return rules
                    else:
                        print("配置文件中没有找到规则部分，使用默认规则")
                        return self.default_replacement_rules
                except Exception as e:
                    print(f"读取配置文件失败: {e}")
                    return self.default_replacement_rules
            else:
                print("配置文件不存在，使用默认规则")
                return self.default_replacement_rules
        except Exception as e:
            print(f"加载替换规则出错: {e}")
            return self.default_replacement_rules
    
    def get_app_dir(self):
        """获取应用程序目录，确保配置文件可以与应用程序一起迁移"""
        try:
            # 如果是打包后的应用，使用应用所在目录
            import sys
            if getattr(sys, 'frozen', False):
                app_dir = os.path.dirname(sys.executable)
                print(f"应用程序目录 (打包模式): {app_dir}")
                return app_dir
            # 如果是开发环境，使用脚本所在目录
            else:
                app_dir = os.path.dirname(os.path.abspath(__file__))
                # 返回上一级目录，因为当前文件在core子目录中
                app_dir = os.path.dirname(app_dir)
                print(f"应用程序目录 (开发模式): {app_dir}")
                return app_dir
        except Exception as e:
            print(f"获取应用目录出错: {e}")
            return os.path.abspath(".")
    
    def remove_spaces_between_chinese_and_digits(self, text):
        """删除中文与数字之间的空格"""
        if not text:
            return ""
        
        # 使用正则表达式删除中文字符和数字之间的空格
        # 匹配 "中文 数字" 模式 并删除中间的空格
        text = re.sub(r'([\u4e00-\u9fa5])\s+(\d)', r'\1\2', text)
        # 匹配 "数字 中文" 模式 并删除中间的空格
        text = re.sub(r'(\d)\s+([\u4e00-\u9fa5])', r'\1\2', text)
        
        return text
    
    def convert_number_to_chinese(self, num_str):
        """将数字转换为中文汉字表示"""
        if not num_str:
            return num_str
            
        # 处理百分数
        if num_str.endswith('%'):
            num_part = num_str[:-1]
            try:
                # 转换百分数前面的数字部分
                chinese_num = self.convert_number_to_chinese(num_part)
                return f"百分之{chinese_num}"
            except:
                return num_str
        
        # 定义基本数字映射
        chinese_digits = {
            '0': '零', '1': '一', '2': '二', '3': '三', '4': '四',
            '5': '五', '6': '六', '7': '七', '8': '八', '9': '九'
        }
        
        # 判断是否含有小数点
        if '.' in num_str:
            integer_part, decimal_part = num_str.split('.')
            decimal_chinese = ''.join(chinese_digits.get(d, d) for d in decimal_part)
            result = self.convert_integer_to_chinese(integer_part) + '点' + decimal_chinese
            return result
        else:
            return self.convert_integer_to_chinese(num_str)
    
    def convert_integer_to_chinese(self, num_str):
        """将整数转换为中文汉字表示"""
        chinese_digits = {
            '0': '零', '1': '一', '2': '二', '3': '三', '4': '四',
            '5': '五', '6': '六', '7': '七', '8': '八', '9': '九'
        }
        units = ['', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '兆']
        
        # 特殊情况处理
        if num_str == '0':
            return '零'
        
        # 去除前导零
        num_str = num_str.lstrip('0')
        
        # 特殊处理10-19
        if len(num_str) == 2 and num_str[0] == '1':
            if num_str[1] == '0':
                return '十'
            else:
                return '十' + chinese_digits[num_str[1]]
        
        # 特殊处理20-99
        if len(num_str) == 2:
            tens = chinese_digits[num_str[0]] + '十'
            if num_str[1] == '0':
                return tens
            else:
                return tens + chinese_digits[num_str[1]]
        
        result = ''
        num_length = len(num_str)
        
        for i, digit in enumerate(num_str):
            pos = num_length - i - 1
            
            if digit == '0':
                # 处理零的显示规则
                if i < num_length - 1 and num_str[i+1] != '0':
                    result += '零'
            else:
                result += chinese_digits[digit] + units[pos]
        
        # 处理可能多余的零
        while '零零' in result:
            result = result.replace('零零', '零')
        
        # 删除末尾的零
        if result.endswith('零'):
            result = result[:-1]
        
        return result
    
    def convert_year_to_chinese(self, year_str):
        """将年份数字转换为年份读法（如2025转为二零二五）"""
        if not year_str or not year_str.isdigit():
            return year_str
        
        # 年份读法的字典映射
        chinese_digits = {
            '0': '零', '1': '一', '2': '二', '3': '三', '4': '四',
            '5': '五', '6': '六', '7': '七', '8': '八', '9': '九'
        }
        
        # 逐位转换
        result = ''
        for digit in year_str:
            result += chinese_digits.get(digit, digit)
        
        return result
    
    def process_text(self, text):
        """处理文本，确保标点符号正确"""
        if not text:
            return ""
        text = text.replace('\n', ' ')
        sentences = [s.strip() for s in text.split() if s.strip()]
        processed_sentences = []
        for i, sentence in enumerate(sentences):
            if i > 0:
                prev_ends_with_punct = processed_sentences[-1].endswith(('，', '。', '！', '？', '；', '、'))
                curr_starts_with_punct = sentence.startswith(('，', '。', '！', '？', '；', '、'))
                if not prev_ends_with_punct and not curr_starts_with_punct:
                    processed_sentences.append('，')
            processed_sentences.append(sentence)
        return ''.join(processed_sentences)
    
    def process_text_for_modified(self, text, apply_rules=True, apply_number_conversion=True):
        """为配音文案处理文本：应用替换规则和数字转换，确保保留所有标点符号"""
        if not text:
            return ""
        
        # 预处理：删除中文和数字之间的空格
        text = self.remove_spaces_between_chinese_and_digits(text)
        
        # 保存标点符号的位置信息
        punctuation_marks = {}
        for match in re.finditer(r'[，。！？；、：,.!?;:]', text):
            punctuation_marks[match.start()] = match.group(0)
        
        # 先应用替换规则
        if apply_rules and self.replacement_rules:
            # 创建替换规则的副本，防止意外修改源规则
            applied_rules = []
            for original, replacement in self.replacement_rules:
                # 确保规则不会无意删除标点符号
                applied_rules.append((original, replacement))
            
            # 应用处理后的规则
            for original, replacement in applied_rules:
                text = text.replace(original, replacement)
        
        # 再转换数字为汉字
        if apply_number_conversion and self.convert_numbers:
            # 创建新变量存储修改后的文本，而不是覆盖原始text
            result_text = text
            
            # 1. 先处理带"年"的四位数字
            result_text = re.sub(r'(\d{4})\s*年', 
                         lambda m: self.convert_year_to_chinese(m.group(1)) + '年', 
                         result_text)
            
            # 2. 更精确地处理特定年份数字
            special_years = ['2021', '2022', '2023', '2024', '2025', '2026']
            for year in special_years:
                # 使用更严格的边界条件
                pattern = r'(?<!\d)' + year + r'(?!\d)'
                result_text = re.sub(pattern,
                           lambda m: self.convert_year_to_chinese(m.group(0)),
                           result_text)
            
            # 3. 处理其他所有数字，确保2025等年份不被再次处理
            def convert_if_not_year(match):
                num = match.group(0)
                if num in special_years:
                    return num  # 保留已处理的年份
                return self.convert_number_to_chinese(num)
            
            # 使用临时函数处理其他数字
            result_text = re.sub(r'(\d+(\.\d+)?%?|\d+)', 
                      convert_if_not_year, 
                      result_text)
            
            # 4. 最后确保所有特定年份都按年份读法处理
            for year in special_years:
                if year in result_text:
                    result_text = result_text.replace(year, self.convert_year_to_chinese(year))
            
            # 使用修改后的文本
            text = result_text
        
        # 处理文本格式时保留标点符号
        # 先将换行符替换为空格
        text = text.replace('\n', ' ')
        # 然后处理多个连续空格
        text = re.sub(r' {2,}', ' ', text)
        
        # 确保标点符号前后空格处理正确
        # 去除标点符号前的空格
        text = re.sub(r' ([，。！？；、：,.!?;:])', r'\1', text)
        # 中文标点后面通常不加空格，英文标点后面加空格
        text = re.sub(r'([,.!?;:]) ?([\u4e00-\u9fa5])', r'\1 \2', text)
        text = re.sub(r'([，。！？；、：]) ?', r'\1', text)
        
        return text.strip()  # 返回处理后的文本
    
    def convert_text(self, text, apply_rules=True, apply_number_conversion=True):
        """转换文本，应用所有规则和处理"""
        # 先处理文本以确保标点符号正确
        processed_text = self.process_text(text)
        
        # 然后应用替换规则和数字转换
        return self.process_text_for_modified(processed_text, apply_rules, apply_number_conversion) 