"""
测试认证修复
"""

import os
import sys

# 添加src路径
sys.path.append('src')


def test_auth_fix():
    """测试认证修复"""
    print("=" * 60)
    print("🔧 测试认证修复")
    print("=" * 60)
    
    print("🔍 问题发现:")
    print("  线程方案工作正常:")
    print("    ✅ 线程启动成功")
    print("    ✅ 实时日志显示正常")
    print("    ✅ 事件循环正确关闭")
    print("")
    print("  但是认证数据加载失败:")
    print("    ❌ 认证数据加载失败")
    print("    ❌ 获得 0 个结果")
    print("")
    print("  可能的原因:")
    print("    1. 认证文件路径在线程中不正确")
    print("    2. 工作目录问题")
    print("    3. 相对路径解析错误")
    print("")
    
    print("🔧 修复内容:")
    print("  1. ✅ 计算正确的认证文件路径:")
    print("     current_dir = src/ui")
    print("     src_dir = src")
    print("     project_root = 项目根目录")
    print("     auth_file_path = project_root/feiyingshuziren/essential_auth_data.json")
    print("")
    print("  2. ✅ 使用绝对路径:")
    print("     - 不依赖工作目录")
    print("     - 确保在任何环境下都能找到文件")
    print("")
    print("  3. ✅ 添加调试信息:")
    print("     - 显示计算出的文件路径")
    print("     - 显示文件是否存在")
    print("     - 便于排查问题")
    print("")
    
    print("📋 路径计算逻辑:")
    print("  线程中的路径计算:")
    print("    __file__ = src/ui/rename_thread.py")
    print("    current_dir = src/ui")
    print("    src_dir = src")
    print("    project_root = 项目根目录")
    print("    auth_file = project_root/feiyingshuziren/essential_auth_data.json")
    print("")
    
    print("🎯 预期效果:")
    print("  ✅ 认证文件路径正确")
    print("  ✅ 认证数据加载成功")
    print("  ✅ 浏览器初始化成功")
    print("  ✅ 重命名任务正常执行")
    print("  ✅ 获得正确的结果")
    print("")
    
    print("🔍 关键日志标识:")
    print("  路径调试:")
    print("    '🔍 认证文件路径: D:\\project\\guangliu02\\feiyingshuziren\\essential_auth_data.json'")
    print("    '🔍 文件存在: True'")
    print("")
    print("  认证成功:")
    print("    '✅ 认证数据加载成功'")
    print("    '✅ 浏览器初始化完成'")
    print("")
    print("  任务执行:")
    print("    '📊 重命名进度: 1/1'")
    print("    '✅ [1/1] 演员名 重命名成功'")
    print("")
    
    print("🚀 测试建议:")
    print("  1. 重启程序")
    print("  2. 再次进行飞影上传测试")
    print("  3. 观察认证文件路径是否正确")
    print("  4. 检查文件是否存在")
    print("  5. 确认认证数据是否加载成功")
    print("")
    
    print("💡 如果还有问题:")
    print("  可能的原因:")
    print("    1. 认证文件格式错误")
    print("    2. 认证数据过期")
    print("    3. 文件权限问题")
    print("    4. JSON解析错误")
    print("")
    print("  调试方法:")
    print("    1. 检查文件内容是否正确")
    print("    2. 验证JSON格式")
    print("    3. 检查cookie数量")
    print("    4. 重新登录获取认证数据")
    print("")
    
    print("=" * 60)
    print("🔧 认证修复完成")
    print("=" * 60)


def test_auth_file_path():
    """测试认证文件路径计算"""
    print("\n" + "=" * 40)
    print("🧪 测试认证文件路径计算")
    print("=" * 40)
    
    try:
        # 模拟线程中的路径计算
        current_dir = os.path.dirname(os.path.abspath(__file__))  # 测试脚本目录
        project_root = current_dir  # 测试脚本在项目根目录
        auth_file_path = os.path.join(project_root, "feiyingshuziren", "essential_auth_data.json")
        
        print(f"📁 当前目录: {current_dir}")
        print(f"📁 项目根目录: {project_root}")
        print(f"📄 认证文件路径: {auth_file_path}")
        print(f"✅ 文件存在: {os.path.exists(auth_file_path)}")
        
        if os.path.exists(auth_file_path):
            file_size = os.path.getsize(auth_file_path)
            print(f"📊 文件大小: {file_size} 字节")
            
            # 尝试读取文件
            try:
                import json
                with open(auth_file_path, 'r', encoding='utf-8') as f:
                    auth_data = json.load(f)
                cookies_count = len(auth_data.get('cookies', []))
                print(f"📊 包含 {cookies_count} 个cookie")
                print("✅ 认证文件格式正确")
            except Exception as e:
                print(f"❌ 认证文件格式错误: {str(e)}")
        else:
            print("❌ 认证文件不存在")
            
    except Exception as e:
        print(f"❌ 路径计算失败: {str(e)}")


if __name__ == "__main__":
    test_auth_fix()
    test_auth_file_path()
