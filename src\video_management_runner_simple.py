#!/usr/bin/env python3
"""
视频管理自动化任务脚本 - 简化版本
用于测试基本功能
"""

import sys
import os
import asyncio
from datetime import datetime
import traceback

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 全局日志文件句柄
log_file = None

def setup_logging():
    """设置日志文件到feiyingshuziren文件夹"""
    global log_file

    # 创建feiyingshuziren/log文件夹（在项目根目录下）
    # 从src目录向上一级到项目根目录
    root_dir = os.path.dirname(project_root)
    log_dir = os.path.join(root_dir, "feiyingshuziren", "log")
    os.makedirs(log_dir, exist_ok=True)

    # 创建按日期的子文件夹
    today = datetime.now().strftime("%Y-%m-%d")
    daily_log_dir = os.path.join(log_dir, today)
    os.makedirs(daily_log_dir, exist_ok=True)

    # 创建日志文件
    timestamp = datetime.now().strftime("%H-%M-%S")
    log_filename = f"video_management_simple_{timestamp}.log"
    log_filepath = os.path.join(daily_log_dir, log_filename)

    # 打开日志文件
    log_file = open(log_filepath, 'w', encoding='utf-8')
    return log_filepath

def log_message(message):
    """记录日志消息"""
    global log_file
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_line = f"[{timestamp}] {message}"
    
    # 输出到控制台
    print(log_line)
    
    # 写入日志文件
    if log_file:
        log_file.write(log_line + '\n')
        log_file.flush()

def test_material_manager():
    """测试素材管理器"""
    try:
        log_message("🔄 测试素材管理器...")
        
        # 导入视频素材管理器
        from core.video_material_manager import VideoMaterialManager
        from core.config_manager import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建视频素材管理器
        video_manager = VideoMaterialManager(config_manager)
        
        log_message("✅ 素材管理器创建成功")
        
        # 获取最近一周的数据
        recent_data = video_manager.get_recent_week_data()
        log_message(f"📊 获取到 {len(recent_data)} 条最近一周的数据")
        
        return True
        
    except Exception as e:
        log_message(f"❌ 素材管理器测试失败: {str(e)}")
        log_message(f"详细错误: {traceback.format_exc()}")
        return False

def test_upload_manager():
    """测试上传管理器"""
    try:
        log_message("🔄 测试上传管理器...")
        
        # 导入飞影上传管理器
        from core.hifly_upload_manager import HiflyUploadManager
        from core.config_manager import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建飞影上传管理器
        upload_manager = HiflyUploadManager(config_manager)
        
        log_message("✅ 上传管理器创建成功")
        
        # 获取待上传数据
        pending_uploads = upload_manager.load_pending_uploads()
        log_message(f"📊 获取到 {len(pending_uploads)} 条待上传数据")
        
        return True
        
    except Exception as e:
        log_message(f"❌ 上传管理器测试失败: {str(e)}")
        log_message(f"详细错误: {traceback.format_exc()}")
        return False

async def test_rename_automation():
    """测试重命名自动化"""
    try:
        log_message("🔄 测试重命名自动化...")
        
        # 导入重命名自动化
        from core.hifly_rename_automation import HiflyRenameAutomation
        
        # 创建重命名自动化实例
        automation = HiflyRenameAutomation()
        
        log_message("✅ 重命名自动化创建成功")
        
        return True
        
    except Exception as e:
        log_message(f"❌ 重命名自动化测试失败: {str(e)}")
        log_message(f"详细错误: {traceback.format_exc()}")
        return False

async def main():
    """主函数"""
    try:
        # 设置日志
        log_filepath = setup_logging()
        log_message(f"📝 日志文件: {log_filepath}")
        
        log_message("🚀 视频管理自动化任务测试开始")
        log_message(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 测试各个组件
        all_success = True
        
        # 1. 测试素材管理器
        log_message("\n" + "=" * 60)
        log_message("📋 测试一：素材管理器")
        log_message("=" * 60)
        
        material_success = test_material_manager()
        if not material_success:
            log_message("❌ 素材管理器测试失败")
            all_success = False
        
        # 2. 测试上传管理器
        log_message("\n" + "=" * 60)
        log_message("📋 测试二：上传管理器")
        log_message("=" * 60)
        
        upload_success = test_upload_manager()
        if not upload_success:
            log_message("❌ 上传管理器测试失败")
            all_success = False
        
        # 3. 测试重命名自动化
        log_message("\n" + "=" * 60)
        log_message("📋 测试三：重命名自动化")
        log_message("=" * 60)
        
        rename_success = await test_rename_automation()
        if not rename_success:
            log_message("❌ 重命名自动化测试失败")
            all_success = False
        
        # 测试完成总结
        log_message("\n" + "=" * 60)
        log_message("📊 测试执行总结")
        log_message("=" * 60)
        
        if all_success:
            log_message("🎉 所有组件测试成功！")
            log_message("✅ 视频管理自动化系统准备就绪")
        else:
            log_message("⚠️ 部分组件测试失败，请检查日志")
        
        log_message(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        log_message("🚀 视频管理自动化任务测试结束")
        
    except Exception as e:
        log_message(f"❌ 主程序异常: {str(e)}")
        log_message(f"详细错误: {traceback.format_exc()}")
    
    finally:
        # 关闭日志文件
        if log_file:
            log_file.close()

if __name__ == "__main__":
    # 运行主程序
    asyncio.run(main())
