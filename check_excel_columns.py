"""
检查Excel文件的列结构
"""

import os
import pandas as pd


def check_excel_columns():
    """检查Excel文件的列结构"""
    print("=" * 60)
    print("🔍 检查Excel文件的列结构")
    print("=" * 60)
    
    avatar_list_path = "data/avatar_list.xlsx"
    if not os.path.exists(avatar_list_path):
        print("❌ 数据文件不存在")
        return
    
    # 读取Excel文件
    df = pd.read_excel(avatar_list_path)
    print(f"📊 总数据量: {len(df)} 条")
    
    # 检查列结构
    print(f"\n🔍 Excel文件列结构:")
    print(f"  总列数: {len(df.columns)}")
    print(f"  列名列表:")
    for i, col in enumerate(df.columns, 1):
        print(f"    {i}. '{col}'")
    
    # 检查关键列是否存在
    key_columns = ["是否上传飞影", "是否重命名", "更新日期", "拍摄演员名称", "ID"]
    print(f"\n🔍 关键列检查:")
    for col in key_columns:
        exists = col in df.columns
        print(f"  '{col}': {'✅ 存在' if exists else '❌ 不存在'}")
    
    # 如果"是否重命名"列存在，检查其值
    if "是否重命名" in df.columns:
        print(f"\n🔍 '是否重命名'列详细分析:")
        rename_col = df["是否重命名"]
        print(f"  数据类型: {rename_col.dtype}")
        print(f"  非空值数量: {rename_col.notna().sum()}")
        print(f"  空值数量: {rename_col.isna().sum()}")
        
        print(f"  值分布:")
        value_counts = rename_col.value_counts(dropna=False)
        for value, count in value_counts.items():
            print(f"    {repr(value)}: {count} 条")
        
        # 检查最近的记录
        print(f"\n🔍 最近5条记录的'是否重命名'值:")
        df_sorted = df.sort_values('更新日期', ascending=False)
        for i, (_, row) in enumerate(df_sorted.head(5).iterrows(), 1):
            video_id = row.get('ID', '')
            rename_status = row.get('是否重命名', '')
            update_date = row.get('更新日期', '')
            print(f"    {i}. ID {video_id}: {repr(rename_status)} (日期: {update_date})")
    
    # 检查"是否上传飞影"列
    if "是否上传飞影" in df.columns:
        print(f"\n🔍 '是否上传飞影'列分析:")
        upload_col = df["是否上传飞影"]
        upload_counts = upload_col.value_counts(dropna=False)
        for value, count in upload_counts.items():
            print(f"  {repr(value)}: {count} 条")
    
    print(f"\n💡 诊断建议:")
    if "是否重命名" not in df.columns:
        print(f"  ❌ 缺少'是否重命名'列，这可能是问题的根源")
        print(f"  🔧 建议：程序应该自动添加这个列")
    elif "是否上传飞影" not in df.columns:
        print(f"  ❌ 缺少'是否上传飞影'列")
    else:
        # 计算应该被筛选到的记录
        upload_yes = (df["是否上传飞影"] == "是").sum()
        rename_not_yes = (df["是否重命名"] != "是").sum()
        print(f"  📊 筛选统计:")
        print(f"    上传飞影='是': {upload_yes} 条")
        print(f"    重命名状态!='是': {rename_not_yes} 条")
        
        # 组合筛选
        mask = (df["是否上传飞影"] == "是") & (df["是否重命名"] != "是")
        combined = mask.sum()
        print(f"    组合筛选结果: {combined} 条")
        
        if combined > 0:
            print(f"  ✅ 应该能找到需要重命名的视频")
        else:
            print(f"  ❌ 没有符合条件的视频")


if __name__ == "__main__":
    check_excel_columns()
