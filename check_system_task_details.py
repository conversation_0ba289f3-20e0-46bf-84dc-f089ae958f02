"""
检查系统任务详细信息
"""

import subprocess
import sys
import os


def check_system_task_details():
    """检查系统任务详细信息"""
    print("=" * 60)
    print("🔍 检查系统任务详细信息")
    print("=" * 60)
    
    # 查找VideoManagement任务
    try:
        result = subprocess.run(
            ['schtasks', '/query', '/fo', 'csv'],
            capture_output=True,
            text=True,
            encoding='gbk'
        )
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            vm_tasks = []
            
            for line in lines[1:]:  # 跳过标题行
                if 'VideoManagement' in line:
                    vm_tasks.append(line)
            
            if vm_tasks:
                print(f"找到 {len(vm_tasks)} 个VideoManagement任务:")
                for task in vm_tasks:
                    parts = task.split(',')
                    if len(parts) >= 3:
                        task_name = parts[0].strip('"')
                        next_run = parts[1].strip('"')
                        status = parts[2].strip('"')
                        
                        print(f"\n📝 任务: {task_name}")
                        print(f"   下次运行: {next_run}")
                        print(f"   状态: {status}")
                        
                        # 查询任务详细信息
                        check_task_details(task_name)
            else:
                print("❌ 没有找到VideoManagement任务")
        else:
            print(f"❌ 查询任务失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 检查任务异常: {e}")


def check_task_details(task_name):
    """检查单个任务的详细信息"""
    try:
        result = subprocess.run(
            ['schtasks', '/query', '/tn', task_name, '/v', '/fo', 'list'],
            capture_output=True,
            text=True,
            encoding='gbk'
        )
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            
            # 提取关键信息
            key_info = {}
            for line in lines:
                if ':' in line:
                    key, value = line.split(':', 1)
                    key = key.strip()
                    value = value.strip()
                    key_info[key] = value
            
            # 显示关键信息
            important_keys = [
                '任务名称',
                '下次运行时间',
                '状态',
                '要运行的任务',
                '开始于',
                '重复: 间隔',
                '重复: 持续时间',
                '唤醒计算机运行',
                '启用',
                '上次运行时间',
                '上次结果'
            ]
            
            print("   详细信息:")
            for key in important_keys:
                if key in key_info:
                    value = key_info[key]
                    print(f"     {key}: {value}")
                    
                    # 特别检查关键设置
                    if key == '唤醒计算机运行' and value != '启用':
                        print(f"     ⚠️ 唤醒功能未启用!")
                    elif key == '启用' and value != '启用':
                        print(f"     ⚠️ 任务未启用!")
                    elif key == '状态' and '就绪' not in value:
                        print(f"     ⚠️ 任务状态异常!")
                        
        else:
            print(f"   ❌ 查询任务详情失败: {result.stderr}")
            
    except Exception as e:
        print(f"   ❌ 查询任务详情异常: {e}")


def test_vbs_execution():
    """测试VBS文件执行"""
    print("\n" + "=" * 40)
    print("🧪 测试VBS文件执行")
    print("=" * 40)
    
    vbs_path = "run_video_management_scheduled.vbs"
    
    if os.path.exists(vbs_path):
        print(f"✅ VBS文件存在: {vbs_path}")
        
        # 读取VBS内容
        with open(vbs_path, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"📝 VBS内容:")
        print(content)
        
        # 测试手动执行VBS
        print("🔧 测试手动执行VBS...")
        try:
            result = subprocess.run(
                ['wscript.exe', vbs_path],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                print("✅ VBS执行成功")
                if result.stdout:
                    print(f"输出: {result.stdout}")
            else:
                print(f"❌ VBS执行失败，返回码: {result.returncode}")
                if result.stderr:
                    print(f"错误: {result.stderr}")
                    
        except subprocess.TimeoutExpired:
            print("⏰ VBS执行超时（可能正在后台运行）")
        except Exception as e:
            print(f"❌ VBS执行异常: {e}")
    else:
        print(f"❌ VBS文件不存在: {vbs_path}")


def test_bat_execution():
    """测试BAT文件执行"""
    print("\n" + "=" * 40)
    print("🧪 测试BAT文件执行")
    print("=" * 40)
    
    bat_path = "run_video_management_auto.bat"
    
    if os.path.exists(bat_path):
        print(f"✅ BAT文件存在: {bat_path}")
        
        # 测试手动执行BAT（只运行几秒）
        print("🔧 测试手动执行BAT...")
        try:
            result = subprocess.run(
                [bat_path, 'test'],
                capture_output=True,
                text=True,
                timeout=5,
                cwd=os.getcwd()
            )
            
            if result.returncode == 0:
                print("✅ BAT执行成功")
            else:
                print(f"❌ BAT执行失败，返回码: {result.returncode}")
            
            if result.stdout:
                print(f"输出: {result.stdout[:500]}...")
            if result.stderr:
                print(f"错误: {result.stderr[:500]}...")
                
        except subprocess.TimeoutExpired:
            print("⏰ BAT执行超时（正常，因为脚本可能需要较长时间）")
        except Exception as e:
            print(f"❌ BAT执行异常: {e}")
    else:
        print(f"❌ BAT文件不存在: {bat_path}")


def check_task_history():
    """检查任务执行历史"""
    print("\n" + "=" * 40)
    print("📊 检查任务执行历史")
    print("=" * 40)
    
    try:
        # 使用PowerShell查询任务历史
        powershell_script = '''
Get-ScheduledTask | Where-Object {$_.TaskName -like "*VideoManagement*"} | ForEach-Object {
    $taskName = $_.TaskName
    Write-Host "任务: $taskName"
    
    $info = Get-ScheduledTaskInfo -TaskName $taskName -ErrorAction SilentlyContinue
    if ($info) {
        Write-Host "  上次运行: $($info.LastRunTime)"
        Write-Host "  上次结果: $($info.LastTaskResult)"
        Write-Host "  下次运行: $($info.NextRunTime)"
    }
    Write-Host ""
}
'''
        
        result = subprocess.run(
            ['powershell', '-Command', powershell_script],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print("PowerShell查询结果:")
            print(result.stdout)
        else:
            print(f"PowerShell查询失败: {result.stderr}")
            
    except Exception as e:
        print(f"查询任务历史异常: {e}")


if __name__ == "__main__":
    check_system_task_details()
    test_vbs_execution()
    test_bat_execution()
    check_task_history()
