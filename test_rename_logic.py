"""
测试重命名筛选逻辑
"""

import os
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>


def test_rename_logic():
    """测试重命名筛选逻辑"""
    print("=" * 60)
    print("🧪 测试重命名筛选逻辑")
    print("=" * 60)
    
    avatar_list_path = "data/avatar_list.xlsx"
    if not os.path.exists(avatar_list_path):
        print("❌ 数据文件不存在")
        return
    
    # 读取Excel文件
    df = pd.read_excel(avatar_list_path)
    print(f"📊 总数据量: {len(df)} 条")
    
    # 检查必要的列
    required_columns = ["是否上传飞影", "是否重命名", "更新日期", "拍摄演员名称", "ID"]
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"❌ 缺少必要的列: {missing_columns}")
        return
    
    print("✅ 所有必要的列都存在")
    
    # 统计各种状态的数据
    print("\n📈 数据统计:")
    
    # 1. 是否上传飞影的统计
    upload_counts = df["是否上传飞影"].value_counts()
    print(f"  是否上传飞影统计:")
    for status, count in upload_counts.items():
        print(f"    '{status}': {count} 条")
    
    # 2. 是否重命名的统计
    rename_counts = df["是否重命名"].value_counts()
    print(f"  是否重命名统计:")
    for status, count in rename_counts.items():
        print(f"    '{status}': {count} 条")
    
    # 3. 筛选逻辑测试
    print("\n🔍 筛选逻辑测试:")
    
    # 全量重命名筛选
    mask_all = (df["是否上传飞影"] == "是") & (df["是否重命名"] != "是")
    all_need_rename = df[mask_all]
    print(f"  全量重命名筛选: {len(all_need_rename)} 条")
    print(f"    条件: 是否上传飞影='是' AND 是否重命名≠'是'")
    
    # 今天上传的重命名筛选
    today = datetime.now().date()
    df["更新日期"] = pd.to_datetime(df["更新日期"], errors='coerce').dt.date
    mask_today = (df["是否上传飞影"] == "是") & (df["是否重命名"] != "是") & (df["更新日期"] == today)
    today_need_rename = df[mask_today]
    print(f"  今天上传的重命名筛选: {len(today_need_rename)} 条")
    print(f"    条件: 是否上传飞影='是' AND 是否重命名≠'是' AND 更新日期={today}")
    
    # 显示一些示例数据
    if len(all_need_rename) > 0:
        print(f"\n📋 全量重命名示例数据 (前5条):")
        for i, (_, row) in enumerate(all_need_rename.head(5).iterrows()):
            actor_name = str(row.get("拍摄演员名称", "")).strip()
            video_id = str(row.get("ID", "")).strip()
            update_date = row.get("更新日期", "")
            rename_status = row.get("是否重命名", "")
            if actor_name and video_id:
                print(f"    {i+1}. {actor_name}-{video_id}-演员 (更新日期: {update_date}, 重命名状态: '{rename_status}')")
    
    if len(today_need_rename) > 0:
        print(f"\n📅 今天上传的重命名示例数据:")
        for i, (_, row) in enumerate(today_need_rename.head(5).iterrows()):
            actor_name = str(row.get("拍摄演员名称", "")).strip()
            video_id = str(row.get("ID", "")).strip()
            update_date = row.get("更新日期", "")
            if actor_name and video_id:
                print(f"    {i+1}. {actor_name}-{video_id}-演员 (更新日期: {update_date})")
    
    # 建议操作
    print(f"\n💡 建议操作:")
    print(f"  1. 如果要测试飞影上传后的自动重命名:")
    print(f"     - 先上传一些视频到飞影")
    print(f"     - 上传完成后会自动重命名今天上传的 {len(today_need_rename)} 个视频")
    
    print(f"  2. 如果要手动重命名所有符合条件的视频:")
    print(f"     - 点击'自动重命名'按钮")
    print(f"     - 会重命名所有符合条件的 {len(all_need_rename)} 个视频")
    
    print(f"  3. 如果某些视频不需要重命名:")
    print(f"     - 在表格中将其'是否重命名'列设为'是'")
    print(f"     - 这样就会被排除在重命名列表之外")
    
    print("\n" + "=" * 60)
    print("🎯 筛选逻辑测试完成")
    print("=" * 60)


if __name__ == "__main__":
    test_rename_logic()
