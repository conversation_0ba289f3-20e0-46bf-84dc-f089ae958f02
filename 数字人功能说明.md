# 数字人功能说明

## 功能概述

数字人模块提供完整的数字人视频生成和管理功能，支持快速模式、积分模式和暗黑模式的视频生成，以及智能的视频后处理功能。

## 主要功能

### 1. 多模式视频生成
- **快速模式**: 快速生成数字人视频，适合批量处理
- **积分模式**: 使用积分生成高质量视频
- **暗黑模式**: 特殊风格的视频生成

### 2. 智能音频处理
- 自动扫描音频文件夹
- 支持多种音频格式（MP3、WAV等）
- 批量上传和处理

### 3. 视频后处理
- AI去水印技术
- 智能裁剪功能
- 高质量视频输出

## 视频处理方案

系统支持两种视频处理方案：

### 1. AI去水印方案 (推荐)
- **适用场景**: 横版视频
- **处理效果**: 使用AI技术精确去除水印，保持视频质量
- **优势**: 
  - 保持原始视频分辨率和质量
  - 精确去除指定区域水印
  - 支持GPU加速处理
- **配置**: 在设置中选择"AI去水印模式"

### 2. 裁剪方案
- **适用场景**: 竖版视频或不支持AI去水印的情况
- **处理效果**: 通过裁剪去除水印区域
- **特点**:
  - 快速处理
  - 兼容性好
  - 可能会改变视频尺寸
- **配置**: 在设置中选择"裁剪模式"

### 版型识别和处理规则 ✅

- **有版型信息**: 文件名包含"-横"或"-竖"时，按实际版型处理
- **无版型信息**: 文件名不包含版型信息时，**自动按横版处理**
- **处理方案选择**:
  - 横版 + AI去水印模式 → 使用AI去水印
  - 横版 + 裁剪模式 → 使用裁剪
  - 竖版 + 任何模式 → 使用裁剪

## 使用流程

### 1. 准备音频文件
1. 将音频文件放入对应的模式文件夹：
   - `feiyingshuziren/音频文件/快速/`
   - `feiyingshuziren/音频文件/积分/`
   - `feiyingshuziren/音频文件/暗黑/`

### 2. 配置设置
1. 打开设置页面
2. 配置HiFly API令牌
3. 选择视频处理方案（AI去水印/裁剪）
4. 设置并发数量和其他参数

### 3. 开始生成
1. 点击"开始上传"按钮
2. 系统自动扫描音频文件
3. 批量提交生成任务
4. 实时监控任务进度

### 4. 视频处理
1. 任务完成后自动下载视频
2. 根据设置进行AI去水印或裁剪
3. 保存到对应的创作任务文件夹

## 设置说明

### 基础设置
- **启用无头模式**: 控制浏览器是否显示界面
- **API批次大小**: 同时处理的API任务数量（1-10）
- **视频处理并发数**: 视频后处理的并发数量

### 视频处理设置
- **视频处理方案**: 选择AI去水印或裁剪模式
- **水印区域配置**: 自定义水印检测区域
- **GPU加速**: 启用GPU加速AI处理

### 视频下载设置
- **并发浏览器数**: 视频下载时的浏览器并发数
- **搜索天数**: 查找最近几天的任务文件夹
- **启用下载截图**: 调试用的截图功能

## 文件组织结构

```
feiyingshuziren/
├── 音频文件/
│   ├── 快速/          # 快速模式音频文件
│   ├── 积分/          # 积分模式音频文件
│   ├── 暗黑/          # 暗黑模式音频文件
│   └── 已完成/        # 已处理的音频文件
├── 创作任务_YYYYMMDD/
│   ├── 快速/          # 快速模式输出视频
│   ├── 积分/          # 积分模式输出视频
│   ├── 暗黑/          # 暗黑模式输出视频
│   └── temp_original_videos/  # 临时原始视频
└── 生成结果记录_YYYYMMDD.xlsx  # 任务记录表格
```

## 注意事项

### 1. API配置
- 必须配置有效的HiFly API令牌
- 确保账户有足够的积分余额
- 网络连接稳定

### 2. 文件格式
- 支持MP3、WAV、FLAC、M4A、OGG等音频格式
- 建议音频文件大小不超过50MB
- 文件名建议包含版型信息（-横、-竖）

### 3. 性能优化
- 根据机器性能调整并发数量
- 启用GPU加速可提高AI处理速度
- 定期清理临时文件

### 4. 故障排除
- 检查API令牌是否正确
- 确认网络连接正常
- 查看日志区域的详细错误信息
- 重启程序解决临时问题

## 最新更新

### 版型识别修复 ✅
- **问题**: 无版型信息的文件被错误地使用裁剪处理
- **修复**: 无版型信息时自动按横版处理
- **效果**: 正确应用AI去水印设置，提高视频质量

### 界面优化 ✅
- 视频下载按钮使用正确的下载图标
- 统一的界面设计风格
- 详细的处理日志显示

现在数字人模块能够正确识别文件版型，并根据用户设置选择最佳的视频处理方案！
