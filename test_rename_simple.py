#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的重命名进程测试
"""

import sys
import os
from pathlib import Path

print("TEST: 进程开始执行")
sys.stdout.flush()

# 添加src路径
sys.path.append(str(Path(__file__).parent / "src"))

print("TEST: 路径已添加")
sys.stdout.flush()

try:
    from core.hifly_rename_automation import HiflyRenameAutomation
    print("TEST: 模块导入成功")
    sys.stdout.flush()
except Exception as e:
    print(f"TEST: 模块导入失败: {e}")
    sys.stdout.flush()
    sys.exit(1)

try:
    automation = HiflyRenameAutomation()
    print("TEST: 自动化实例创建成功")
    sys.stdout.flush()
except Exception as e:
    print(f"TEST: 实例创建失败: {e}")
    sys.stdout.flush()
    sys.exit(1)

try:
    auth_loaded = automation.load_auth_data()
    print(f"TEST: 认证数据加载结果: {auth_loaded}")
    sys.stdout.flush()
except Exception as e:
    print(f"TEST: 认证数据加载异常: {e}")
    sys.stdout.flush()

print("TEST: 测试完成")
sys.stdout.flush()