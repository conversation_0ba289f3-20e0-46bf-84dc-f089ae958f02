#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证蓝色高亮修改
简单验证搜索高亮颜色已从黄色改为蓝色
"""

import os
import re

def verify_color_change():
    """验证颜色修改"""
    print("🔍 验证搜索高亮颜色修改")
    print("=" * 50)
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键的颜色修改
        checks = [
            # 检查是否还有黄色
            ("黄色残留", r"#ffeb3b", False),
            
            # 检查是否有蓝色
            ("蓝色高亮", r"#4a90e2", True),
            
            # 检查白色文字
            ("白色文字", r"#ffffff", True),
            
            # 检查黑色文字残留
            ("黑色文字残留", r"#000000", False),
        ]
        
        results = {}
        for name, pattern, should_exist in checks:
            matches = re.findall(pattern, content, re.IGNORECASE)
            count = len(matches)
            
            if should_exist:
                success = count > 0
                status = f"✅ 找到 {count} 处" if success else "❌ 未找到"
            else:
                success = count == 0
                status = f"✅ 已清除" if success else f"❌ 仍有 {count} 处"
            
            results[name] = success
            print(f"{name}: {status}")
        
        # 检查具体的高亮设置
        print(f"\n📍 具体的高亮设置:")
        
        # 视频管理模块
        vm_highlights = re.findall(r'highlight_color = QColor\("([^"]+)"\).*# (.+)', content)
        for color, comment in vm_highlights:
            print(f"  视频管理: {color} ({comment})")
        
        # 样式表设置
        style_colors = re.findall(r'background-color: (#[a-fA-F0-9]{6}) !important', content)
        for color in set(style_colors):
            print(f"  样式表背景: {color}")
        
        text_colors = re.findall(r'color: (#[a-fA-F0-9]{6}) !important', content)
        for color in set(text_colors):
            print(f"  样式表文字: {color}")
        
        # 总体结果
        all_passed = all(results.values())
        
        print(f"\n🎯 验证结果: {'✅ 全部通过' if all_passed else '❌ 部分失败'}")
        
        if all_passed:
            print(f"\n🎉 颜色修改成功！")
            print(f"✅ 黄色高亮 → 蓝色高亮 (#4a90e2)")
            print(f"✅ 黑色文字 → 白色文字 (#ffffff)")
            print(f"✅ 搜索结果将以蓝色背景+白色文字显示")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        return False

def show_color_preview():
    """显示颜色预览"""
    print(f"\n🎨 新的高亮配色方案:")
    print(f"┌─────────────────────────────────────┐")
    print(f"│  🔵 蓝色背景 (#4A90E2)              │")
    print(f"│  ⚪ 白色文字 (#FFFFFF)              │")
    print(f"│                                     │")
    print(f"│  示例: [  搜索结果  ]               │")
    print(f"│        ^^^^^^^^^^^^                 │")
    print(f"│        蓝底白字高亮                 │")
    print(f"└─────────────────────────────────────┘")
    
    print(f"\n💡 配色优势:")
    print(f"  - 蓝色更加现代和专业")
    print(f"  - 白色文字在蓝色背景上对比度高")
    print(f"  - 不刺眼，适合长时间使用")
    print(f"  - 与应用整体风格协调")

def main():
    """主函数"""
    success = verify_color_change()
    show_color_preview()
    
    if success:
        print(f"\n🚀 修改完成，用户现在可以看到蓝色的搜索高亮效果！")
    else:
        print(f"\n⚠️ 可能还有部分颜色需要调整")
    
    return success

if __name__ == "__main__":
    main()
