"""
调试重命名筛选逻辑
"""

import os
import pandas as pd
from datetime import datetime, <PERSON><PERSON><PERSON>


def debug_rename_filter():
    """调试重命名筛选逻辑"""
    print("=" * 60)
    print("🔍 调试重命名筛选逻辑")
    print("=" * 60)
    
    avatar_list_path = "data/avatar_list.xlsx"
    if not os.path.exists(avatar_list_path):
        print("❌ 数据文件不存在")
        return
    
    # 读取Excel文件
    df = pd.read_excel(avatar_list_path)
    print(f"📊 总数据量: {len(df)} 条")
    
    # 检查必要的列
    required_columns = ["是否上传飞影", "是否重命名", "更新日期", "拍摄演员名称", "ID"]
    for col in required_columns:
        if col in df.columns:
            print(f"✅ 列 '{col}' 存在")
        else:
            print(f"❌ 列 '{col}' 不存在")
    
    # 统计各种状态
    print(f"\n📈 数据统计:")
    
    # 1. 是否上传飞影统计
    upload_counts = df["是否上传飞影"].value_counts(dropna=False)
    print(f"  是否上传飞影统计:")
    for status, count in upload_counts.items():
        print(f"    '{status}': {count} 条")
    
    # 2. 是否重命名统计
    rename_counts = df["是否重命名"].value_counts(dropna=False)
    print(f"  是否重命名统计:")
    for status, count in rename_counts.items():
        print(f"    '{status}': {count} 条")
    
    # 3. 更新日期统计
    today = datetime.now().date()
    print(f"\n📅 今天日期: {today}")
    
    # 转换更新日期列
    df["更新日期"] = pd.to_datetime(df["更新日期"], errors='coerce').dt.date
    
    # 统计最近几天的更新日期
    recent_dates = df["更新日期"].value_counts().head(10)
    print(f"  最近的更新日期统计:")
    for date, count in recent_dates.items():
        print(f"    {date}: {count} 条")
    
    # 4. 筛选逻辑测试
    print(f"\n🔍 筛选逻辑测试:")
    
    # 基础筛选：是否上传飞影 = "是"
    mask_uploaded = (df["是否上传飞影"] == "是")
    uploaded_count = mask_uploaded.sum()
    print(f"  已上传飞影的视频: {uploaded_count} 条")
    
    # 进一步筛选：是否重命名 != "是"
    mask_not_renamed = (df["是否上传飞影"] == "是") & (df["是否重命名"] != "是")
    not_renamed_count = mask_not_renamed.sum()
    print(f"  已上传但未重命名的视频: {not_renamed_count} 条")
    
    # 今天上传的筛选
    mask_today = (df["是否上传飞影"] == "是") & (df["是否重命名"] != "是") & (df["更新日期"] == today)
    today_count = mask_today.sum()
    print(f"  今天上传且未重命名的视频: {today_count} 条")
    
    # 显示一些示例数据
    if not_renamed_count > 0:
        print(f"\n📋 已上传但未重命名的视频示例 (前10条):")
        not_renamed_df = df[mask_not_renamed]
        for i, (_, row) in enumerate(not_renamed_df.head(10).iterrows()):
            actor_name = str(row.get("拍摄演员名称", "")).strip()
            video_id = str(row.get("ID", "")).strip()
            update_date = row.get("更新日期", "")
            upload_status = row.get("是否上传飞影", "")
            rename_status = row.get("是否重命名", "")
            print(f"    {i+1:2d}. {actor_name}-{video_id} | 上传: '{upload_status}' | 重命名: '{rename_status}' | 日期: {update_date}")
    
    # 分析问题
    print(f"\n💡 问题分析:")
    if today_count == 0 and not_renamed_count > 0:
        print(f"  ❌ 问题：筛选条件太严格")
        print(f"     - 有 {not_renamed_count} 个视频需要重命名")
        print(f"     - 但筛选'今天上传'的条件过滤掉了所有视频")
        print(f"     - 建议：放宽日期筛选条件，或者不使用日期筛选")
        
        # 检查最近几天的数据
        for days_back in [1, 2, 3, 7]:
            target_date = today - timedelta(days=days_back)
            mask_recent = (df["是否上传飞影"] == "是") & (df["是否重命名"] != "是") & (df["更新日期"] == target_date)
            recent_count = mask_recent.sum()
            if recent_count > 0:
                print(f"     - {days_back}天前({target_date})有 {recent_count} 个视频需要重命名")
    
    elif today_count > 0:
        print(f"  ✅ 筛选逻辑正常，找到 {today_count} 个今天上传的视频需要重命名")
    
    else:
        print(f"  ℹ️ 没有需要重命名的视频")
    
    print(f"\n🔧 建议修复:")
    print(f"  1. 修改筛选条件，不限制日期，只要是已上传但未重命名的都处理")
    print(f"  2. 或者放宽日期条件，比如最近3天内上传的")
    print(f"  3. 或者在上传完成后立即更新'更新日期'为今天")
    
    print("\n" + "=" * 60)
    print("🎯 调试完成")
    print("=" * 60)


if __name__ == "__main__":
    debug_rename_filter()
