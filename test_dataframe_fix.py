"""
测试DataFrame遍历修复
"""

import os
import sys
import pandas as pd

# 添加src路径
sys.path.append('src')


def test_dataframe_iteration():
    """测试DataFrame遍历修复"""
    print("=" * 60)
    print("🔧 测试DataFrame遍历修复")
    print("=" * 60)
    
    print("🔍 问题根源分析:")
    print("  原问题: for record in recent_data:")
    print("  问题: recent_data是pandas DataFrame")
    print("  结果: 遍历的是列名，不是行数据")
    print("  日志: [WARNING] 跳过非字典数据: <class 'str'> - ID")
    print("")
    
    print("✅ 修复方案:")
    print("  修复前: for record in recent_data:")
    print("  修复后: for index, row in recent_data.iterrows():")
    print("  结果: 正确遍历每一行数据")
    print("")
    
    # 模拟DataFrame数据
    print("🧪 模拟测试:")
    test_data = pd.DataFrame({
        'ID': ['001', '002', '003'],
        '演员': ['演员A', '演员B', '演员C'],
        '是否上传飞影': ['否', '是', '否'],
        '是否重命名': ['否', '否', '是'],
        '更新日期': ['2025-08-01', '2025-08-01', '2025-07-30']
    })
    
    print(f"  测试数据: {len(test_data)} 行")
    print(f"  数据类型: {type(test_data)}")
    print("")
    
    print("错误的遍历方式:")
    print("  for record in test_data:")
    count = 0
    for record in test_data:
        print(f"    {count+1}. {type(record)} - {record}")
        count += 1
        if count >= 3:
            print("    ...")
            break
    print("  结果: 遍历的是列名！")
    print("")
    
    print("正确的遍历方式:")
    print("  for index, row in test_data.iterrows():")
    upload_count = 0
    rename_count = 0
    for index, row in test_data.iterrows():
        print(f"    行{index}: ID={row['ID']}, 演员={row['演员']}")
        if row.get('是否上传飞影', '否') == '否':
            upload_count += 1
        if row.get('是否重命名', '否') == '否':
            rename_count += 1
    print(f"  结果: 需要上传 {upload_count} 个，需要重命名 {rename_count} 个")
    print("")
    
    print("📊 修复效果预期:")
    print("  修复前日志:")
    print("    [WARNING] 跳过非字典数据: <class 'str'> - ID")
    print("    [WARNING] 跳过非字典数据: <class 'str'> - 演员")
    print("    [INFO] 没有需要上传的视频")
    print("    [INFO] 没有需要重命名的视频")
    print("")
    print("  修复后日志:")
    print("    [DEBUG] 获取到数据类型: <class 'pandas.core.frame.DataFrame'>, 行数: 3")
    print("    [DEBUG] 添加上传任务: 演员A-001")
    print("    [DEBUG] 添加上传任务: 演员C-003")
    print("    [DEBUG] 添加重命名任务: 演员A-001")
    print("    [DEBUG] 添加重命名任务: 演员B-002")
    print("    [SUCCESS] 找到需要处理的任务")
    print("")


def test_video_management_runner():
    """测试修复后的video_management_runner"""
    print("=" * 40)
    print("🧪 测试修复后的脚本")
    print("=" * 40)
    
    print("测试步骤:")
    print("  1. 创建新的程序内定时任务")
    print("  2. 任务名称: DataFrame修复测试")
    print("  3. 重复间隔: 2分钟")
    print("  4. 观察新的调试日志")
    print("")
    
    print("预期的成功日志:")
    print("  [DEBUG] 获取到数据类型: <class 'pandas.core.frame.DataFrame'>, 行数: X")
    print("  [DEBUG] 添加上传任务: 演员名-ID")
    print("  [DEBUG] 添加重命名任务: 演员名-ID")
    print("  [SUCCESS] 飞影上传任务完成")
    print("  [SUCCESS] 自动重命名任务完成")
    print("")
    
    print("不应该再看到:")
    print("  ❌ [WARNING] 跳过非字典数据: <class 'str'> - ID")
    print("  ❌ [INFO] 没有需要上传的视频")
    print("  ❌ [INFO] 没有需要重命名的视频")
    print("")


def show_dataframe_basics():
    """显示DataFrame基础知识"""
    print("=" * 40)
    print("📚 DataFrame遍历基础")
    print("=" * 40)
    
    print("pandas DataFrame的遍历方式:")
    print("")
    print("1. 遍历列名（错误用法）:")
    print("   for col in df:")
    print("       # col是列名字符串")
    print("")
    print("2. 遍历行（正确用法）:")
    print("   for index, row in df.iterrows():")
    print("       # index是行索引，row是Series对象")
    print("       # 可以用row['列名']或row.get('列名')访问")
    print("")
    print("3. 其他遍历方式:")
    print("   for row in df.itertuples():")
    print("       # row是namedtuple对象")
    print("")
    print("   for index in df.index:")
    print("       row = df.loc[index]")
    print("       # 通过索引获取行")
    print("")
    
    print("最佳实践:")
    print("  ✅ 使用iterrows()遍历行数据")
    print("  ✅ 使用row.get()安全访问列值")
    print("  ✅ 检查DataFrame是否为空: len(df) > 0")
    print("  ✅ 添加调试信息显示数据类型和行数")
    print("")


def create_test_command():
    """创建测试命令"""
    print("=" * 40)
    print("🚀 立即测试")
    print("=" * 40)
    
    print("现在可以测试修复效果:")
    print("  1. 程序正在运行，修复已生效")
    print("  2. 创建新的程序内定时任务:")
    print("     - 任务名称: DataFrame修复测试")
    print("     - 任务类型: full_process")
    print("     - 重复间隔: 2分钟")
    print("     - 持续时间: 10分钟")
    print("  3. 观察新的调试日志")
    print("")
    
    print("关键验证点:")
    print("  ✅ 看到[DEBUG]信息显示DataFrame类型和行数")
    print("  ✅ 看到[DEBUG]信息显示添加的具体任务")
    print("  ✅ 不再有[WARNING]跳过非字典数据")
    print("  ✅ 如果有符合条件的数据，会正确处理")
    print("")
    
    print("如果仍然显示'没有需要处理的视频':")
    print("  这可能是正常的，说明:")
    print("  1. 最近7天没有更新日期的数据")
    print("  2. 或者所有数据的'是否上传飞影'和'是否重命名'都不是'否'")
    print("  3. 这时应该看到正确的调试信息，而不是WARNING")


if __name__ == "__main__":
    test_dataframe_iteration()
    test_video_management_runner()
    show_dataframe_basics()
    create_test_command()
