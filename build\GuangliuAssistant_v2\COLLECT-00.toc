([('GuangliuAssistant_v2.exe',
   'D:\\project\\fish_win\\guangliu\\build\\GuangliuAssistant_v2\\GuangliuAssistant_v2.exe',
   'EXECUTABLE'),
  ('playwright\\driver\\node.exe',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\node.exe',
   'BINARY'),
  ('python312.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python312.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('PySide6\\opengl32sw.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\opengl32sw.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qschannelbackend.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\tls\\qschannelbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qopensslbackend.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\tls\\qopensslbackend.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'C:\\Program Files\\Git\\mingw64\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'C:\\Program Files\\Git\\mingw64\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qminimal.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtiff.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtga.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qwindows.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qsvg.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qpdf.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwbmp.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qjpeg.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qico.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qdirect2d.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qdirect2d.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qgif.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qoffscreen.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwebp.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qicns.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PySide6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\greenlet\\_greenlet.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\hashing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\writers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\tslib.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\properties.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\ops.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\parsers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\json.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\lxml\\etree.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\lxml\\_elementpath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PIL\\_webp.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PIL\\_imagingtk.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PIL\\_imagingcms.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PIL\\_imagingmath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PIL\\_imaging.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\groupby.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\interval.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\index.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\algos.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\arrays.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\join.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\sparse.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\missing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\internals.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\indexing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\lib.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\reshape.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\pandas_parser.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PySide6\\QtSvg.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\QtSvg.pyd',
   'EXTENSION'),
  ('shiboken6\\Shiboken.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\shiboken6\\Shiboken.pyd',
   'EXTENSION'),
  ('PySide6\\QtNetwork.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\QtNetwork.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\testing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\sas.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\_libs\\byteswap.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\lxml\\html\\diff.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PySide6\\QtGui.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\QtGui.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\lxml\\sax.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\lxml\\objectify.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp312-win_amd64.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\lxml\\builder.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PySide6\\QtCore.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\QtCore.pyd',
   'EXTENSION'),
  ('PySide6\\QtWidgets.pyd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\QtWidgets.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('shiboken6\\MSVCP140.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\shiboken6\\MSVCP140.dll',
   'BINARY'),
  ('PySide6\\Qt6Core.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\Qt6Core.dll',
   'BINARY'),
  ('PySide6\\Qt6Network.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\Qt6Network.dll',
   'BINARY'),
  ('PySide6\\Qt6Gui.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\Qt6Gui.dll',
   'BINARY'),
  ('PySide6\\Qt6Svg.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\Qt6Svg.dll',
   'BINARY'),
  ('PySide6\\Qt6VirtualKeyboard.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\Qt6VirtualKeyboard.dll',
   'BINARY'),
  ('PySide6\\Qt6Pdf.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\Qt6Pdf.dll',
   'BINARY'),
  ('PySide6\\Qt6Widgets.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\Qt6Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140_1.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python3.dll',
   'BINARY'),
  ('shiboken6\\shiboken6.abi3.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\shiboken6\\shiboken6.abi3.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\VCRUNTIME140.dll',
   'BINARY'),
  ('PySide6\\MSVCP140.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\MSVCP140.dll',
   'BINARY'),
  ('PySide6\\pyside6.abi3.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\pyside6.abi3.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\shiboken6\\VCRUNTIME140.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140_1.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\shiboken6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_2.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\MSVCP140_2.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_1.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\MSVCP140_1.dll',
   'BINARY'),
  ('PySide6\\Qt6Quick.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\Qt6Quick.dll',
   'BINARY'),
  ('PySide6\\Qt6Qml.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\Qt6Qml.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlModels.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\Qt6QmlModels.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlMeta.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\Qt6QmlMeta.dll',
   'BINARY'),
  ('PySide6\\Qt6OpenGL.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\Qt6OpenGL.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlWorkerScript.dll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\Qt6QmlWorkerScript.dll',
   'BINARY'),
  ('config.py', 'D:\\project\\fish_win\\guangliu\\src\\config.py', 'DATA'),
  ('config\\settings.json',
   'D:\\project\\fish_win\\guangliu\\config\\settings.json',
   'DATA'),
  ('config\\text_extractor_rules.json',
   'D:\\project\\fish_win\\guangliu\\config\\text_extractor_rules.json',
   'DATA'),
  ('core\\__init__.py',
   'D:\\project\\fish_win\\guangliu\\src\\core\\__init__.py',
   'DATA'),
  ('core\\__pycache__\\__init__.cpython-312.pyc',
   'D:\\project\\fish_win\\guangliu\\src\\core\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('core\\__pycache__\\clipboard_manager.cpython-312.pyc',
   'D:\\project\\fish_win\\guangliu\\src\\core\\__pycache__\\clipboard_manager.cpython-312.pyc',
   'DATA'),
  ('core\\__pycache__\\clipboard_manager.cpython-313.pyc',
   'D:\\project\\fish_win\\guangliu\\src\\core\\__pycache__\\clipboard_manager.cpython-313.pyc',
   'DATA'),
  ('core\\__pycache__\\config_manager.cpython-312.pyc',
   'D:\\project\\fish_win\\guangliu\\src\\core\\__pycache__\\config_manager.cpython-312.pyc',
   'DATA'),
  ('core\\__pycache__\\config_manager.cpython-313.pyc',
   'D:\\project\\fish_win\\guangliu\\src\\core\\__pycache__\\config_manager.cpython-313.pyc',
   'DATA'),
  ('core\\__pycache__\\digital_human_manager.cpython-312.pyc',
   'D:\\project\\fish_win\\guangliu\\src\\core\\__pycache__\\digital_human_manager.cpython-312.pyc',
   'DATA'),
  ('core\\__pycache__\\processor.cpython-312.pyc',
   'D:\\project\\fish_win\\guangliu\\src\\core\\__pycache__\\processor.cpython-312.pyc',
   'DATA'),
  ('core\\__pycache__\\processor.cpython-313.pyc',
   'D:\\project\\fish_win\\guangliu\\src\\core\\__pycache__\\processor.cpython-313.pyc',
   'DATA'),
  ('core\\__pycache__\\text_converter.cpython-312.pyc',
   'D:\\project\\fish_win\\guangliu\\src\\core\\__pycache__\\text_converter.cpython-312.pyc',
   'DATA'),
  ('core\\auth_updater.py',
   'D:\\project\\fish_win\\guangliu\\src\\core\\auth_updater.py',
   'DATA'),
  ('core\\clipboard_manager.py',
   'D:\\project\\fish_win\\guangliu\\src\\core\\clipboard_manager.py',
   'DATA'),
  ('core\\config_manager.py',
   'D:\\project\\fish_win\\guangliu\\src\\core\\config_manager.py',
   'DATA'),
  ('core\\digital_human_manager.py',
   'D:\\project\\fish_win\\guangliu\\src\\core\\digital_human_manager.py',
   'DATA'),
  ('core\\processor.py',
   'D:\\project\\fish_win\\guangliu\\src\\core\\processor.py',
   'DATA'),
  ('core\\text_converter.py',
   'D:\\project\\fish_win\\guangliu\\src\\core\\text_converter.py',
   'DATA'),
  ('data\\script_template.xlsx',
   'D:\\project\\fish_win\\guangliu\\data\\script_template.xlsx',
   'DATA'),
  ('data\\voice_id_list.xlsx',
   'D:\\project\\fish_win\\guangliu\\data\\voice_id_list.xlsx',
   'DATA'),
  ('feiyingshuziren\\README.md',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\README.md',
   'DATA'),
  ('feiyingshuziren\\essential_auth_data.json',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\essential_auth_data.json',
   'DATA'),
  ('feiyingshuziren\\hifly_auto_runner.py',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\hifly_auto_runner.py',
   'DATA'),
  ('feiyingshuziren\\hifly_automation-bk-old.py',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\hifly_automation-bk-old.py',
   'DATA'),
  ('feiyingshuziren\\hifly_video_downloader.py',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\hifly_video_downloader.py',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_01-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_01-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_02-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_02-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_03-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_03-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_04-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_04-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_05-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_05-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_06-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_06-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_07-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_07-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_08-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_08-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_09-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_09-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_10-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_10-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_10-11-25.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_10-11-25.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_10-15-52.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_10-15-52.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_10-18-10.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_10-18-10.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-09-20.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-09-20.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-19-00.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-19-00.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-28-00.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-28-00.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-30-49.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-30-49.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-33-10.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-33-10.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-40-00.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-40-00.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-44-00.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-44-00.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-45-00.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-45-00.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-52-52.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_11-52-52.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_12-40-00.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_12-40-00.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_12-42-18.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_12-42-18.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-02\\digital_human_auto_12-44-00.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-02\\digital_human_auto_12-44-00.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_01-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_01-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_02-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_02-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_03-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_03-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_04-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_04-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_05-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_05-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_06-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_06-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_07-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_07-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_08-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_08-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_09-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_09-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_10-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_10-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_11-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_11-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_13-10-28.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_13-10-28.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_13-11-00.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_13-11-00.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_14-11-00.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_14-11-00.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_15-11-00.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_15-11-00.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_16-11-00.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_16-11-00.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_17-11-00.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_17-11-00.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_18-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_18-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_19-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_19-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_20-11-14.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_20-11-14.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_21-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_21-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_22-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_22-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-03\\digital_human_auto_23-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-03\\digital_human_auto_23-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_00-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_00-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_01-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_01-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_01-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_01-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_02-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_02-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_02-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_02-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_03-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_03-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_03-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_03-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_04-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_04-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_04-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_04-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_05-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_05-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_05-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_05-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_06-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_06-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_06-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_06-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_07-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_07-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_07-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_07-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_08-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_08-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_08-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_08-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_09-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_09-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_09-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_09-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_10-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_10-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_10-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_10-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_11-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_11-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_11-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_11-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_12-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_12-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_13-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_13-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_14-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_14-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_15-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_15-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_16-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_16-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_17-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_17-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_18-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_18-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_19-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_19-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_20-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_20-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_21-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_21-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_22-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_22-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-04\\digital_human_auto_23-11-04.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-04\\digital_human_auto_23-11-04.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_00-11-08.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_00-11-08.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_01-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_01-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_01-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_01-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_02-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_02-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_02-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_02-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_03-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_03-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_03-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_03-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_04-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_04-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_04-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_04-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_05-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_05-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_05-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_05-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_06-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_06-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_06-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_06-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_07-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_07-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_07-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_07-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_08-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_08-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_08-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_08-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_09-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_09-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_09-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_09-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_10-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_10-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_10-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_10-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_11-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_11-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_11-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_11-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_12-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_12-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_13-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_13-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_13-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_13-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_14-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_14-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_15-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_15-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_16-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_16-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_17-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_17-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_18-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_18-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_19-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_19-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_20-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_20-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_21-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_21-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_22-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_22-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-05\\digital_human_auto_23-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-05\\digital_human_auto_23-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_00-11-03.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_00-11-03.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_01-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_01-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_01-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_01-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_02-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_02-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_02-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_02-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_03-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_03-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_03-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_03-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_04-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_04-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_04-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_04-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_05-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_05-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_05-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_05-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_06-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_06-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_06-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_06-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_07-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_07-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_07-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_07-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_08-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_08-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_08-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_08-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_09-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_09-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_09-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_09-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_10-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_10-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_10-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_10-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_11-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_11-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_11-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_11-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_12-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_12-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_13-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_13-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_13-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_13-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_14-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_14-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_15-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_15-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_16-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_16-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_17-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_17-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_18-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_18-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_19-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_19-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_20-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_20-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_21-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_21-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_22-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_22-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-06\\digital_human_auto_23-11-03.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-06\\digital_human_auto_23-11-03.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_00-11-03.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_00-11-03.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_01-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_01-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_01-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_01-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_02-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_02-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_02-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_02-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_03-00-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_03-00-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_03-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_03-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_04-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_04-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_04-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_04-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_05-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_05-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_05-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_05-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_06-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_06-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_06-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_06-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_07-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_07-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_07-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_07-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_08-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_08-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_08-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_08-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_09-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_09-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_09-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_09-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_10-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_10-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_10-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_10-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_11-00-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_11-00-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_11-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_11-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_12-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_12-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_13-11-01.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_13-11-01.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_13-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_13-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_14-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_14-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_15-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_15-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_16-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_16-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_17-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_17-11-02.log',
   'DATA'),
  ('feiyingshuziren\\log\\2025-07-07\\digital_human_auto_18-11-02.log',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\log\\2025-07-07\\digital_human_auto_18-11-02.log',
   'DATA'),
  ('feiyingshuziren\\run_hifly_auto.bat',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\run_hifly_auto.bat',
   'DATA'),
  ('feiyingshuziren\\update_auth_README.md',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\update_auth_README.md',
   'DATA'),
  ('feiyingshuziren\\update_auth_data.py',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\update_auth_data.py',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\切换URL前_32727.png',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\切换URL前_32727.png',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\切换URL前_33502.png',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\切换URL前_33502.png',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\切换URL前_36524.png',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\切换URL前_36524.png',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\切换URL前_37335.png',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\切换URL前_37335.png',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\切换URL前_37417.png',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\切换URL前_37417.png',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\切换URL前_37441.png',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\切换URL前_37441.png',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\切换URL前_37472.png',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\切换URL前_37472.png',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\切换URL前_37480.png',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\切换URL前_37480.png',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\切换URL后_32727.png',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\切换URL后_32727.png',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\切换URL后_33502.png',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\切换URL后_33502.png',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\切换URL后_36524.png',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\切换URL后_36524.png',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\切换URL后_37335.png',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\切换URL后_37335.png',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\切换URL后_37417.png',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\切换URL后_37417.png',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\切换URL后_37441.png',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\切换URL后_37441.png',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\切换URL后_37472.png',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\切换URL后_37472.png',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\切换URL后_37480.png',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\切换URL后_37480.png',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\已完成\\暗黑\\一组上海-413999-37480.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\已完成\\暗黑\\一组上海-413999-37480.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\已完成\\积分\\二组1-417490-417491-37472.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\已完成\\积分\\二组1-417490-417491-37472.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\已完成\\积分\\二组1-417492-417493-37441.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\已完成\\积分\\二组1-417492-417493-37441.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\已完成\\积分\\二组1-417495-417494-37417.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\已完成\\积分\\二组1-417495-417494-37417.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\已完成\\积分\\二组1-417496-417497-33502.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\已完成\\积分\\二组1-417496-417497-33502.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\已完成\\积分\\二组1-417498-417499-36524.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\已完成\\积分\\二组1-417498-417499-36524.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\已完成\\积分\\二组1-417501-417500-37335.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\已完成\\积分\\二组1-417501-417500-37335.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\已完成\\积分\\二组1-417502-417503-32727.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\已完成\\积分\\二组1-417502-417503-32727.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\成功_32727.txt',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\成功_32727.txt',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\成功_33502.txt',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\成功_33502.txt',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\成功_36524.txt',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\成功_36524.txt',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\成功_37335.txt',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\成功_37335.txt',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\成功_37417.txt',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\成功_37417.txt',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\成功_37441.txt',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\成功_37441.txt',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\成功_37472.txt',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\成功_37472.txt',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\成功_37480.txt',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\成功_37480.txt',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\暗黑\\一组上海-413999-37480.mp4',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\暗黑\\一组上海-413999-37480.mp4',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\生成结果记录.xlsx',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\生成结果记录.xlsx',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\积分\\二组1-417490-417491-37.mp4',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\积分\\二组1-417490-417491-37.mp4',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\积分\\二组1-417492-417493-37.mp4',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\积分\\二组1-417492-417493-37.mp4',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\积分\\二组1-417495-417494-37.mp4',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\积分\\二组1-417495-417494-37.mp4',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\积分\\二组1-417496-417497-33.mp4',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\积分\\二组1-417496-417497-33.mp4',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\积分\\二组1-417498-417499-36.mp4',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\积分\\二组1-417498-417499-36.mp4',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\积分\\二组1-417501-417500-37.mp4',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\积分\\二组1-417501-417500-37.mp4',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250627\\积分\\二组1-417502-417503-32.mp4',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250627\\积分\\二组1-417502-417503-32.mp4',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250630\\暗黑模式_已完成\\二组1-404106-36548-m.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250630\\暗黑模式_已完成\\二组1-404106-36548-m.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250630\\暗黑模式_已完成\\二组1-404415-404414-32850-m.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250630\\暗黑模式_已完成\\二组1-404415-404414-32850-m.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250630\\暗黑模式_已完成\\二组1-404613-404614-32302-m.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250630\\暗黑模式_已完成\\二组1-404613-404614-32302-m.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250630\\暗黑模式_已完成\\二组2-403256-33803-m.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250630\\暗黑模式_已完成\\二组2-403256-33803-m.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250630\\暗黑模式_已完成\\二组2-403257-33693-m.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250630\\暗黑模式_已完成\\二组2-403257-33693-m.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250630\\暗黑模式_已完成\\二组2-403258-32996-m.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250630\\暗黑模式_已完成\\二组2-403258-32996-m.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250630\\暗黑模式_已完成\\二组2-404102-36874-m.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250630\\暗黑模式_已完成\\二组2-404102-36874-m.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250630\\暗黑模式_已完成\\二组2-404103-36134-m.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250630\\暗黑模式_已完成\\二组2-404103-36134-m.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250630\\暗黑模式_已完成\\二组2-404411-404412-36290-m.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250630\\暗黑模式_已完成\\二组2-404411-404412-36290-m.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250630\\生成结果记录.xlsx',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250630\\生成结果记录.xlsx',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250701\\error_details.txt',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250701\\error_details.txt',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\一组上海-412522-35548.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\一组上海-412522-35548.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\一组上海-412526-37220.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\一组上海-412526-37220.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组1-403261-36839-m.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组1-403261-36839-m.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组1-403262-33134-m.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组1-403262-33134-m.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组1-403263-33764-m.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组1-403263-33764-m.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组1-414973-37210.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组1-414973-37210.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组1-418248-36650.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组1-418248-36650.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组1-418249-37611.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组1-418249-37611.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组2-414790-31916.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组2-414790-31916.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组2-418243-37601.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组2-418243-37601.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组2-418244-35580.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组2-418244-35580.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组2-418246-33626.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组2-418246-33626.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组2-418247-37613.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\二组2-418247-37613.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\新闻701-00111.MP3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250701\\已完成\\暗黑\\新闻701-00111.MP3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250701\\暗黑模式_已完成\\二组1-403265-35573-m.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250701\\暗黑模式_已完成\\二组1-403265-35573-m.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250701\\更新失败记录.txt',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250701\\更新失败记录.txt',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250701\\生成结果记录.xlsx',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250701\\生成结果记录.xlsx',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250702\\已完成\\暗黑\\二组1-403262-33134-m.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250702\\已完成\\暗黑\\二组1-403262-33134-m.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250702\\已完成\\暗黑\\二组2-414790-31916.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250702\\已完成\\暗黑\\二组2-414790-31916.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250702\\已完成\\暗黑\\二组2-418247-37613.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250702\\已完成\\暗黑\\二组2-418247-37613.mp3',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250702\\生成结果记录.xlsx',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250702\\生成结果记录.xlsx',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250702\\生成结果记录_20250702.xlsx',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250702\\生成结果记录_20250702.xlsx',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250703\\生成结果记录_20250703.xlsx',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250703\\生成结果记录_20250703.xlsx',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250704\\生成结果记录_20250704.xlsx',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250704\\生成结果记录_20250704.xlsx',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250705\\生成结果记录_20250705.xlsx',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250705\\生成结果记录_20250705.xlsx',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250706\\生成结果记录_20250706.xlsx',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250706\\生成结果记录_20250706.xlsx',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250707\\生成结果记录_20250707.xlsx',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250707\\生成结果记录_20250707.xlsx',
   'DATA'),
  ('feiyingshuziren\\创作任务_20250709\\生成结果记录_20250709.xlsx',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\创作任务_20250709\\生成结果记录_20250709.xlsx',
   'DATA'),
  ('feiyingshuziren\\备份\\essential_auth_data.json',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\备份\\essential_auth_data.json',
   'DATA'),
  ('feiyingshuziren\\设置定时任务指南.md',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\设置定时任务指南.md',
   'DATA'),
  ('feiyingshuziren\\音频文件\\暗黑\\418457-418458-37658.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\音频文件\\暗黑\\418457-418458-37658.mp3',
   'DATA'),
  ('feiyingshuziren\\音频文件\\暗黑\\418459-418460-37641.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\音频文件\\暗黑\\418459-418460-37641.mp3',
   'DATA'),
  ('feiyingshuziren\\音频文件\\暗黑\\二组1-418463-418464-31858.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\音频文件\\暗黑\\二组1-418463-418464-31858.mp3',
   'DATA'),
  ('feiyingshuziren\\音频文件\\暗黑\\二组1-418465-418466-33062.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\音频文件\\暗黑\\二组1-418465-418466-33062.mp3',
   'DATA'),
  ('feiyingshuziren\\音频文件\\暗黑\\二组1-418557-418558-37432.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\音频文件\\暗黑\\二组1-418557-418558-37432.mp3',
   'DATA'),
  ('feiyingshuziren\\音频文件\\暗黑\\二组1-418560-418561-37595.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\音频文件\\暗黑\\二组1-418560-418561-37595.mp3',
   'DATA'),
  ('feiyingshuziren\\音频文件\\暗黑\\二组2-418461-418462-37589.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\音频文件\\暗黑\\二组2-418461-418462-37589.mp3',
   'DATA'),
  ('feiyingshuziren\\音频文件\\暗黑\\二组2-418562-418563-37630.mp3',
   'D:\\project\\fish_win\\guangliu\\feiyingshuziren\\音频文件\\暗黑\\二组2-418562-418563-37630.mp3',
   'DATA'),
  ('ui\\__init__.py',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\__init__.py',
   'DATA'),
  ('ui\\__pycache__\\__init__.cpython-312.pyc',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('ui\\__pycache__\\custom_widgets.cpython-312.pyc',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\__pycache__\\custom_widgets.cpython-312.pyc',
   'DATA'),
  ('ui\\__pycache__\\main_window.cpython-312.pyc',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\__pycache__\\main_window.cpython-312.pyc',
   'DATA'),
  ('ui\\__pycache__\\schedule_manager.cpython-312.pyc',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\__pycache__\\schedule_manager.cpython-312.pyc',
   'DATA'),
  ('ui\\__pycache__\\settings_dialog.cpython-312.pyc',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\__pycache__\\settings_dialog.cpython-312.pyc',
   'DATA'),
  ('ui\\custom_widgets.py',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\custom_widgets.py',
   'DATA'),
  ('ui\\icons\\1.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\1.svg',
   'DATA'),
  ('ui\\icons\\app.ico',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\app.ico',
   'DATA'),
  ('ui\\icons\\arrow_back.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\arrow_back.svg',
   'DATA'),
  ('ui\\icons\\arrow_down.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\arrow_down.svg',
   'DATA'),
  ('ui\\icons\\arrow_forward.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\arrow_forward.svg',
   'DATA'),
  ('ui\\icons\\arrow_up.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\arrow_up.svg',
   'DATA'),
  ('ui\\icons\\avatar.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\avatar.svg',
   'DATA'),
  ('ui\\icons\\check-white.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\check-white.svg',
   'DATA'),
  ('ui\\icons\\chevron-down-gray.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\chevron-down-gray.svg',
   'DATA'),
  ('ui\\icons\\chevron-down-white.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\chevron-down-white.svg',
   'DATA'),
  ('ui\\icons\\chevron-down.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\chevron-down.svg',
   'DATA'),
  ('ui\\icons\\clear.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\clear.svg',
   'DATA'),
  ('ui\\icons\\daily_tasks.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\daily_tasks.svg',
   'DATA'),
  ('ui\\icons\\digital_human.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\digital_human.svg',
   'DATA'),
  ('ui\\icons\\digital_human_blue.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\digital_human_blue.svg',
   'DATA'),
  ('ui\\icons\\guangliu.ico',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\guangliu.ico',
   'DATA'),
  ('ui\\icons\\icon_overall.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\icon_overall.svg',
   'DATA'),
  ('ui\\icons\\load_script.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\load_script.svg',
   'DATA'),
  ('ui\\icons\\menu.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\menu.svg',
   'DATA'),
  ('ui\\icons\\moon.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\moon.svg',
   'DATA'),
  ('ui\\icons\\output_folder.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\output_folder.svg',
   'DATA'),
  ('ui\\icons\\paste_data.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\paste_data.svg',
   'DATA'),
  ('ui\\icons\\refresh_voice.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\refresh_voice.svg',
   'DATA'),
  ('ui\\icons\\schedule.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\schedule.svg',
   'DATA'),
  ('ui\\icons\\search.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\search.svg',
   'DATA'),
  ('ui\\icons\\search_icon.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\search_icon.svg',
   'DATA'),
  ('ui\\icons\\settings.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\settings.svg',
   'DATA'),
  ('ui\\icons\\settings_blue.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\settings_blue.svg',
   'DATA'),
  ('ui\\icons\\settings_icon.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\settings_icon.svg',
   'DATA'),
  ('ui\\icons\\sound.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\sound.svg',
   'DATA'),
  ('ui\\icons\\start_process.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\start_process.svg',
   'DATA'),
  ('ui\\icons\\sun.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\sun.svg',
   'DATA'),
  ('ui\\icons\\user.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\user.svg',
   'DATA'),
  ('ui\\icons\\voice_clone.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\voice_clone.svg',
   'DATA'),
  ('ui\\icons\\voice_clone_blue.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\voice_clone_blue.svg',
   'DATA'),
  ('ui\\icons\\备份\\今日任务.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\备份\\今日任务.svg',
   'DATA'),
  ('ui\\icons\\备份\\今日任务x-01.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\备份\\今日任务x-01.svg',
   'DATA'),
  ('ui\\icons\\备份\\今日任务x.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\备份\\今日任务x.svg',
   'DATA'),
  ('ui\\icons\\备份\\刷新声音.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\备份\\刷新声音.svg',
   'DATA'),
  ('ui\\icons\\备份\\加载文案.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\备份\\加载文案.svg',
   'DATA'),
  ('ui\\icons\\备份\\向前.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\备份\\向前.svg',
   'DATA'),
  ('ui\\icons\\备份\\向后.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\备份\\向后.svg',
   'DATA'),
  ('ui\\icons\\备份\\声音克隆.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\备份\\声音克隆.svg',
   'DATA'),
  ('ui\\icons\\备份\\开始处理.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\备份\\开始处理.svg',
   'DATA'),
  ('ui\\icons\\备份\\开始处理1-01.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\备份\\开始处理1-01.svg',
   'DATA'),
  ('ui\\icons\\备份\\搜索.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\备份\\搜索.svg',
   'DATA'),
  ('ui\\icons\\备份\\数字人提交.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\备份\\数字人提交.svg',
   'DATA'),
  ('ui\\icons\\备份\\画板 1 副本.png',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\备份\\画板 1 副本.png',
   'DATA'),
  ('ui\\icons\\备份\\画板 1 副本.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\备份\\画板 1 副本.svg',
   'DATA'),
  ('ui\\icons\\备份\\粘贴数据.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\备份\\粘贴数据.svg',
   'DATA'),
  ('ui\\icons\\备份\\设置.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\备份\\设置.svg',
   'DATA'),
  ('ui\\icons\\备份\\输出文件夹.svg',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\icons\\备份\\输出文件夹.svg',
   'DATA'),
  ('ui\\main_window.py',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\main_window.py',
   'DATA'),
  ('ui\\schedule_manager.py',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\schedule_manager.py',
   'DATA'),
  ('ui\\settings_dialog.py',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\settings_dialog.py',
   'DATA'),
  ('ui\\styles.qss',
   'D:\\project\\fish_win\\guangliu\\src\\ui\\styles.qss',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiFirefox.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiFirefox.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\userAgent.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\userAgent.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiPdf.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiPdf.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiProtocol.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiProtocol.js',
   'DATA'),
  ('playwright\\driver\\package\\index.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\artifactDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\artifactDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\elementHandlerDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\elementHandlerDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\browserFetcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\browserFetcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\javascript.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\javascript.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\protocol.d.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\protocol.d.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\ariaSnapshot.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\ariaSnapshot.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkProvisionalPage.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkProvisionalPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\codicon.DCmgc-ay.ttf',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\codicon.DCmgc-ay.ttf',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\jsHandle.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\jsHandle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\image_tools\\colorUtils.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\image_tools\\colorUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\android.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\android.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\tracing.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\tracing.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiCommands.d.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiCommands.d.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\test\\inMemorySnapshotter.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\test\\inMemorySnapshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\colors.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\colors.js',
   'DATA'),
  ('playwright\\driver\\package\\types\\structs.d.ts',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\types\\structs.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\deviceDescriptors.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\deviceDescriptors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\cssTokenizer.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\cssTokenizer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\codeMirrorModule-DECADVLv.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\codeMirrorModule-DECADVLv.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\accessibility.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\accessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browser.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\browser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\debugLogger.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\debugLogger.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\fileUtils.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\fileUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderUtils.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\pageDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\pageDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\input.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\input.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crExecutionContext.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\cli.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\cli.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\helper.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\helper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\debugController.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\debugController.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\index.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_linux.sh',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\zipBundle.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\zipBundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\stringUtils.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\stringUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\types.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-eHBmevrY.css',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-eHBmevrY.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserTypeDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserTypeDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crPage.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\wsServer.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\wsServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkBrowser.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorGenerators.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorGenerators.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffExecutionContext.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\pipeTransport.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\pipeTransport.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\artifact.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\artifact.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\jsonl.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\jsonl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\inProcessFactory.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\inProcessFactory.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crDevTools.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crDevTools.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\image_tools\\stats.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\image_tools\\stats.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_mac.sh',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkWorkers.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkWorkers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\clock.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\clock.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\chat.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\chat.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dialog.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dialog.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\languages.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\languages.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\timeoutSettings.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\timeoutSettings.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiSerializer.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiSerializer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\timeoutRunner.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\timeoutRunner.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkPage.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkPage.js',
   'DATA'),
  ('playwright\\py.typed',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\py.typed',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\selectors.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\selectors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\socksInterceptor.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\socksInterceptor.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiBrowser.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\protocolFormatter.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\protocolFormatter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\task.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\task.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\xtermModule-BoAIEibi.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\xtermModule-BoAIEibi.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\protocolError.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\protocolError.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffInput.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiExecutionContext.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.D5wwC2E1.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.D5wwC2E1.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\electron.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\electron.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkExecutionContext.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderRunner.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderRunner.js',
   'DATA'),
  ('playwright\\driver\\package\\index.mjs',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\index.mjs',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\screenshotter.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\screenshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fileChooser.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\fileChooser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffPage.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\protocolMetainfo.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\protocolMetainfo.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiOverCdp.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiOverCdp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fileUploadUtils.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\fileUploadUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\download.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\download.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiProtocolCore.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiProtocolCore.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\tracingDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\tracingDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserContextDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserContextDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\locator.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\locator.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiChromium.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiChromium.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\macEditingCommands.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\macEditingCommands.js',
   'DATA'),
  ('playwright\\driver\\package\\types\\protocol.d.ts',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\types\\protocol.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\frame.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\frame.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\appIcon.png',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\appIcon.png',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clientStackTrace.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\clientStackTrace.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\instrumentation.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\instrumentation.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\rtti.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\rtti.js',
   'DATA'),
  ('playwright\\driver\\package\\browsers.json',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\browsers.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiDeserializer.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiDeserializer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\utilityScriptSource.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\utilityScriptSource.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_linux.sh',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkAccessibility.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\csharp.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\csharp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\javascript.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\javascript.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\network.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.html',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\time.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\time.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\remote\\playwrightServer.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\remote\\playwrightServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\debugControllerDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\debugControllerDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\zipFile.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\zipFile.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\storageScriptSource.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\storageScriptSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotterInjected.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotterInjected.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\types.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\nodePlatform.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\nodePlatform.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crNetworkManager.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\crypto.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\crypto.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\semaphore.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\semaphore.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\waiter.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\waiter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\selectorParser.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\selectorParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\webSocketRouteDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\webSocketRouteDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\frameDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\frameDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\browserServerImpl.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\browserServerImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\sw.bundle.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\sw.bundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crAccessibility.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\webSocket.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\webSocket.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\stackTrace.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\stackTrace.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\headers.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\headers.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_linux.sh',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\playwright-logo.svg',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\playwright-logo.svg',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.CFOW-Ezb.css',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.CFOW-Ezb.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\playwright-logo.svg',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\playwright-logo.svg',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\program.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\cli\\program.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\third_party\\pixelmatch.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\third_party\\pixelmatch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\consoleMessage.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\consoleMessage.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_mac.sh',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\android\\android.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\android\\android.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\dispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\dispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.html',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\jsonPipeDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\jsonPipeDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\download.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\download.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-YwXrOGhp.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-YwXrOGhp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\playwrightDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\playwrightDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\androidServerImpl.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\androidServerImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\inprocess.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\inprocess.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\clockSource.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\clockSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\network.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\har\\harTracer.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\har\\harTracer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\cdpSessionDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\cdpSessionDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\protocol.d.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\protocol.d.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\api.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\api.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\eventEmitter.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\eventEmitter.js',
   'DATA'),
  ('playwright\\driver\\package\\api.json',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\api.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\python.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\python.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\debug.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\debug.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\pipeTransport.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\pipeTransport.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\writableStream.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\writableStream.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\webSocketMockSource.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\webSocketMockSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\transport.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\transport.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_win.ps1',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorUtils.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\tracing.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\tracing.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\codeMirrorModule.C3UTv-Ge.css',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\codeMirrorModule.C3UTv-Ge.css',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_win.ps1',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\zipBundleImpl.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\zipBundleImpl.js',
   'DATA'),
  ('playwright\\driver\\LICENSE',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\LICENSE',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffNetworkManager.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\socksProxy.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\socksProxy.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\channelOwner.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\channelOwner.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiKeyboard.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiKeyboard.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\errors.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\errors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browserContext.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\browserContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\env.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\env.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\videoRecorder.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\videoRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browserContext.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\browserContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\hostPlatform.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\hostPlatform.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\java.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\java.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\validator.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\validator.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderCollection.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderCollection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\types.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils.js',
   'DATA'),
  ('playwright\\driver\\package\\index.d.ts',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\index.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\accessibility.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\accessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browserType.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\browserType.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\connection.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\connection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\fileUtils.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\fileUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_mac.sh',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\comparators.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\comparators.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\formData.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\formData.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\traceUtils.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\traceUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\eventsHelper.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\eventsHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\types\\types.d.ts',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\types\\types.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_mac.sh',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_mac.sh',
   'DATA'),
  ('playwright\\driver\\README.md',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\README.md',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\debugger.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\debugger.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\jsonPipe.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\jsonPipe.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkInterceptableRequest.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkInterceptableRequest.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\image_tools\\compare.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\image_tools\\compare.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\playwright.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\playwright.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crPdf.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crPdf.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\platform.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\platform.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\urlMatch.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\urlMatch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiConnection.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\frameSelectors.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\frameSelectors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\dependencies.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\dependencies.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\oopDownloadBrowserMain.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\oopDownloadBrowserMain.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\language.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\language.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\page.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\page.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\webError.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\webError.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clientInstrumentation.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\clientInstrumentation.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\programWithTestStub.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\cli\\programWithTestStub.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\streamDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\streamDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\callLog.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\callLog.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\index.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\htmlReport\\index.html',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\htmlReport\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\harBackend.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\harBackend.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\har\\harRecorder.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\har\\harRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\deviceDescriptorsSource.json',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\deviceDescriptorsSource.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\manualPromise.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\manualPromise.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\localUtils.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\localUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderFrontend.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderFrontend.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\cdpSession.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\cdpSession.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\processLauncher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\processLauncher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\electronDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\electronDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\mimeType.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\mimeType.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\dialog.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\dialog.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\dialogDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\dialogDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiPage.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.BjQ9je-p.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.BjQ9je-p.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\snapshot.html',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\snapshot.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\throttledFile.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\throttledFile.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\fileChooser.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\fileChooser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\index.html',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\linuxUtils.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\linuxUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffAccessibility.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundleImpl\\index.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utilsBundleImpl\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\elementHandle.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\elementHandle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\localUtils.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\localUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\webkit.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\webkit.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\usKeyboardLayout.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\usKeyboardLayout.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\chromiumSwitches.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\chromiumSwitches.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browser.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\browser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\nativeDeps.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\nativeDeps.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-C3UTv-Ge.css',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-C3UTv-Ge.css',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_mac.sh',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\defaultFontFamilies.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\defaultFontFamilies.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffConnection.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_win.ps1',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_win.ps1',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\coverage.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\coverage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\electron\\electron.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\electron\\electron.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crInput.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkConnection.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\protocol.yml',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\protocol.yml',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crBrowser.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\pollingRecorderSource.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\pollingRecorderSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\video.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\video.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\stream.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\stream.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\progress.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\progress.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\console.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\console.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\injectedScriptSource.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\injectedScriptSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\launchApp.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\launchApp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\happyEyeballs.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\happyEyeballs.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\remote\\playwrightConnection.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\remote\\playwrightConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\cookieStore.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\cookieStore.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorParser.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crConnection.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codicon-DCmgc-ay.ttf',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codicon-DCmgc-ay.ttf',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fetch.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\fetch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.BatfzHMG.css',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.BatfzHMG.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\ascii.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\ascii.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderApp.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderApp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\firefoxPrefs.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\firefoxPrefs.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\harRouter.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\harRouter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\androidDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\androidDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-DRsk21vu.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-DRsk21vu.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\errors.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\errors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundleImpl\\xdg-open',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utilsBundleImpl\\xdg-open',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_linux.sh',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\jsHandleDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\jsHandleDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotter.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\writableStreamDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\writableStreamDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\localUtilsDispatcher.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\localUtilsDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crProtocolHelper.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crProtocolHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\multimap.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\multimap.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\bindingsControllerSource.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\bindingsControllerSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\serializers.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\serializers.js',
   'DATA'),
  ('playwright\\driver\\package\\README.md',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\README.md',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\expectUtils.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\expectUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\ThirdPartyNotices.txt',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\ThirdPartyNotices.txt',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clientHelper.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\clientHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\defaultSettingsView.NYBT19Ch.css',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\defaultSettingsView.NYBT19Ch.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\android\\backendAdb.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\android\\backendAdb.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\electron\\loader.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\electron\\loader.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dom.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dom.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crCoverage.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crCoverage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\worker.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\worker.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\defaultSettingsView-Cjl_e5YM.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\defaultSettingsView-Cjl_e5YM.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\selectors.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\selectors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\image_tools\\imageChannel.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\image_tools\\imageChannel.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\chromium.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\chromium.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\driver.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\cli\\driver.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\events.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\events.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\frames.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\frames.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\profiler.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\profiler.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\networkDispatchers.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\networkDispatchers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundle.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utilsBundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\contextRecorder.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\contextRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\install_media_pack.ps1',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\bin\\install_media_pack.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiNetworkManager.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crDragDrop.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crDragDrop.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_win.ps1',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\fetch.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\fetch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\xtermModule.Beg8tuEN.css',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\xtermModule.Beg8tuEN.css',
   'DATA'),
  ('playwright\\driver\\package\\package.json',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\package.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\socksClientCertificatesInterceptor.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\socksClientCertificatesInterceptor.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiInput.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\types.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\input.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\input.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\httpServer.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\httpServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\utilityScriptSerializers.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\utilityScriptSerializers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\outofprocess.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\outofprocess.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\zones.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\zones.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clock.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\clock.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\validatorPrimitives.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\validatorPrimitives.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\viewer\\traceViewer.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\viewer\\traceViewer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\playwright.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\playwright.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\spawnAsync.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\spawnAsync.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browserType.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\browserType.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkInput.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiProtocolPermissions.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiProtocolPermissions.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\protocol.d.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\protocol.d.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\page.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\client\\page.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\firefox.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\firefox.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\assert.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\assert.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\utils\\network.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\utils\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crServiceWorker.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crServiceWorker.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\cssParser.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\cssParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\artifact.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\artifact.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffBrowser.js',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_linux.sh',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_linux.sh',
   'DATA'),
  ('pypinyin\\style\\bopomofo.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\style\\bopomofo.pyi',
   'DATA'),
  ('pypinyin\\tools\\toneconvert.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\tools\\toneconvert.pyi',
   'DATA'),
  ('pypinyin\\contrib\\tone_sandhi.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\contrib\\tone_sandhi.pyi',
   'DATA'),
  ('pypinyin\\pinyin_dict.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\pinyin_dict.pyi',
   'DATA'),
  ('pypinyin\\py.typed',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\py.typed',
   'DATA'),
  ('pypinyin\\contrib\\_tone_rule.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\contrib\\_tone_rule.pyi',
   'DATA'),
  ('pypinyin\\style\\__init__.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\style\\__init__.pyi',
   'DATA'),
  ('pypinyin\\__init__.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\__init__.pyi',
   'DATA'),
  ('pypinyin\\style\\_tone_convert.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\style\\_tone_convert.pyi',
   'DATA'),
  ('pypinyin\\style\\wadegiles.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\style\\wadegiles.pyi',
   'DATA'),
  ('pypinyin\\style\\_tone_rule.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\style\\_tone_rule.pyi',
   'DATA'),
  ('pypinyin\\converter.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\converter.pyi',
   'DATA'),
  ('pypinyin\\constants.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\constants.pyi',
   'DATA'),
  ('pypinyin\\pinyin_dict.json',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\pinyin_dict.json',
   'DATA'),
  ('pypinyin\\phrases_dict.json',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\phrases_dict.json',
   'DATA'),
  ('pypinyin\\standard.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\standard.pyi',
   'DATA'),
  ('pypinyin\\contrib\\neutral_tone.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\contrib\\neutral_tone.pyi',
   'DATA'),
  ('pypinyin\\style\\cyrillic.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\style\\cyrillic.pyi',
   'DATA'),
  ('pypinyin\\style\\others.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\style\\others.pyi',
   'DATA'),
  ('pypinyin\\utils.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\utils.pyi',
   'DATA'),
  ('pypinyin\\style\\finals.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\style\\finals.pyi',
   'DATA'),
  ('pypinyin\\core.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\core.pyi',
   'DATA'),
  ('pypinyin\\style\\gwoyeu.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\style\\gwoyeu.pyi',
   'DATA'),
  ('pypinyin\\contrib\\uv.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\contrib\\uv.pyi',
   'DATA'),
  ('pypinyin\\phonetic_symbol.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\phonetic_symbol.pyi',
   'DATA'),
  ('pypinyin\\runner.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\runner.pyi',
   'DATA'),
  ('pypinyin\\seg\\simpleseg.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\seg\\simpleseg.pyi',
   'DATA'),
  ('pypinyin\\contrib\\mmseg.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\contrib\\mmseg.pyi',
   'DATA'),
  ('pypinyin\\seg\\mmseg.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\seg\\mmseg.pyi',
   'DATA'),
  ('pypinyin\\phrases_dict.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\phrases_dict.pyi',
   'DATA'),
  ('pypinyin\\style\\_constants.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\style\\_constants.pyi',
   'DATA'),
  ('pypinyin\\compat.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\compat.pyi',
   'DATA'),
  ('pypinyin\\exceptions.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\exceptions.pyi',
   'DATA'),
  ('pypinyin\\contrib\\tone_convert.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\contrib\\tone_convert.pyi',
   'DATA'),
  ('pypinyin\\style\\tone.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\style\\tone.pyi',
   'DATA'),
  ('pypinyin\\style\\_utils.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\style\\_utils.pyi',
   'DATA'),
  ('pypinyin\\style\\initials.pyi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pypinyin\\style\\initials.pyi',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('PySide6\\translations\\qt_help_ar.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_uk.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pt_BR.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_PT.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ru.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nl.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_tr.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_en.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_bg.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_it.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_CN.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_gl.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sk.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_sk.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pl.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_tr.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ru.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nn.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hu.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ka.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_TW.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fa.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fr.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ca.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_da.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_bg.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ko.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fr.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pl.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_uk.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lv.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ru.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_CN.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_CN.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_BR.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hr.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_tr.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_he.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PySide6\\translations\\qt_it.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_es.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_fr.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_en.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nl.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hr.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ca.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ko.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_de.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ca.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hu.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ko.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ka.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sl.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_it.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ja.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_uk.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pl.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pt_BR.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_cs.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_lv.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sl.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sv.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_sv.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_TW.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hu.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fa.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_es.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_de.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_de.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ar.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ja.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_bg.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gd.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_da.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ja.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gl.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sk.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_cs.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nn.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hr.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ar.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_da.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_da.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nl.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_he.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_he.qm',
   'DATA'),
  ('PySide6\\translations\\qt_en.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lt.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_lt.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fi.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_de.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_es.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nn.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_cs.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_TW.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_gd.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ka.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fi.qm',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\PySide6\\translations\\qt_fi.qm',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'D:\\project\\fish_win\\fish_env\\Lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('base_library.zip',
   'D:\\project\\fish_win\\guangliu\\build\\GuangliuAssistant_v2\\base_library.zip',
   'DATA')],)
