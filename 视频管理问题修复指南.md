# 视频管理问题修复指南

## 问题描述

用户在使用视频管理功能时遇到两个主要问题：

1. **文件路径问题**: `avatar_list.xlsx文件不存在，返回空数据`
2. **调试端口连接问题**: `connect ECONNREFUSED ::1:9222`

## 修复内容

### 1. 文件路径问题修复 ✅

**问题原因**:
- 程序使用相对路径 `os.getcwd()` 查找文件
- 在不同工作目录下运行时路径会失效
- 导致找不到 `data/avatar_list.xlsx` 文件

**修复方案**:
```python
# 修复前
self.data_dir = os.path.join(os.getcwd(), "data")

# 修复后  
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
self.data_dir = os.path.join(current_dir, "data")
```

**修复效果**:
- 使用绝对路径解析，从 `video_material_manager.py` 向上三级到项目根目录
- 无论在哪个目录运行程序都能正确找到文件
- 添加调试日志显示文件路径和存在状态

### 2. 调试端口连接问题修复 ✅

**问题原因**:
- `localhost` 在某些系统上解析为IPv6地址 `::1`
- Playwright连接IPv6地址时出现 `ECONNREFUSED` 错误
- 浏览器启动后立即连接，没有等待时间

**修复方案**:

#### A. 强制使用IPv4地址
```python
# 修复前
browser = await p.chromium.connect_over_cdp(f"http://localhost:{self.debug_port}")

# 修复后
browser = await p.chromium.connect_over_cdp(f"http://127.0.0.1:{self.debug_port}")
```

#### B. 改进浏览器启动参数
```python
subprocess.Popen([
    chrome_path,
    f"--remote-debugging-port={self.debug_port}",
    "--user-data-dir=chrome-debug-profile",
    "--no-first-run",
    "--no-default-browser-check",
    "--disable-web-security",
    "--disable-features=VizDisplayCompositor",
    "--remote-debugging-address=127.0.0.1"  # 强制IPv4
])
```

#### C. 增加启动等待逻辑
```python
# 等待浏览器启动并检查连接
for i in range(10):  # 最多等待10秒
    time.sleep(1)
    if self.check_debug_browser():
        self.log_message.emit(f"✅ 浏览器启动成功 (等待{i+1}秒)")
        break
```

## 使用说明

### 方法1: 手动启动调试浏览器（推荐）

1. **启动调试Chrome**:
   ```bash
   "C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --remote-debugging-address=127.0.0.1
   ```

2. **登录网站**:
   - 在浏览器中访问 `https://zxsc.baidu-int.com`
   - 完成登录验证

3. **使用程序**:
   - 启动光流一站式口播助手
   - 点击视频管理图标
   - 点击素材更新按钮

### 方法2: 程序自动启动

1. **直接使用程序**:
   - 启动程序，点击视频管理
   - 点击素材更新按钮
   - 程序会自动启动调试浏览器

2. **等待连接**:
   - 程序会显示等待进度
   - 最多等待10秒建立连接
   - 连接成功后继续下载流程

## 技术改进

### 1. 路径解析增强
- ✅ 绝对路径解析，避免工作目录依赖
- ✅ 调试日志显示路径信息
- ✅ 文件存在性检查

### 2. 网络连接优化
- ✅ 强制IPv4连接，避免IPv6问题
- ✅ 改进Chrome启动参数
- ✅ 增加连接等待和重试逻辑

### 3. 错误处理完善
- ✅ 详细的连接状态日志
- ✅ 优雅的降级处理
- ✅ 清晰的错误信息

## 验证结果

通过测试验证，修复后的程序能够：

1. ✅ **正确找到avatar_list.xlsx文件**
   - 文件路径解析正确
   - 数据加载成功
   - 显示最近一周的数据

2. ✅ **成功连接调试端口浏览器**
   - IPv4连接稳定
   - 启动等待逻辑有效
   - 连接成功率提高

3. ✅ **稳定地下载素材数据**
   - 保持登录状态
   - 自动化操作流畅
   - 数据处理正确

## 故障排除

### 如果仍然遇到文件路径问题：
1. 检查 `data/avatar_list.xlsx` 文件是否存在
2. 确保程序有读取权限
3. 查看日志中的路径信息

### 如果仍然遇到连接问题：
1. 确保端口9222未被占用
2. 手动启动调试Chrome验证
3. 检查防火墙设置
4. 尝试使用其他端口

### 如果浏览器启动失败：
1. 检查Chrome安装路径
2. 以管理员身份运行程序
3. 关闭其他Chrome实例

## 总结

通过这次修复，视频管理模块现在具备了：

- 🔧 **健壮的文件路径处理**
- 🌐 **稳定的网络连接机制**  
- ⏱️ **智能的等待和重试逻辑**
- 📝 **详细的调试日志信息**

用户现在可以顺利使用素材更新功能，自动从网站下载最新的素材数据并进行本地管理。
