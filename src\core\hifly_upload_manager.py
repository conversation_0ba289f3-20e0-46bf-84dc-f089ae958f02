#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
飞影上传管理器
"""

import os
import pandas as pd
import time
from typing import List, Dict, Any
from PySide6.QtCore import QObject, Signal, QThread

from .hifly_client import HiflyClient


class HiflyUploadThread(QThread):
    """飞影上传线程"""
    
    def __init__(self, upload_manager):
        super().__init__()
        self.upload_manager = upload_manager
    
    def run(self):
        """线程运行方法"""
        try:
            self.upload_manager.process_upload_queue()
        except Exception as e:
            self.upload_manager.log_message.emit(f"❌ 飞影上传线程异常: {str(e)}")
            self.upload_manager.upload_completed.emit(False, f"上传线程异常: {str(e)}")


class HiflyUploadManager(QObject):
    """飞影上传管理器"""
    
    # 信号定义
    log_message = Signal(str)
    upload_progress = Signal(int, int, str)  # 当前进度, 总数, 当前任务名称
    upload_completed = Signal(bool, str)  # 是否成功, 消息
    
    def __init__(self, config_manager):
        super().__init__()
        self.config_manager = config_manager
        self.hifly_client = None
        self.upload_thread = None
        self.upload_queue = []
        self.current_index = 0
        self.total_count = 0
        self.success_count = 0
        self.failed_count = 0

        # 文件路径 - 使用绝对路径确保正确性
        current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.data_dir = os.path.join(current_dir, "data")
        self.avatar_list_path = os.path.join(self.data_dir, "avatar_list.xlsx")
        
    def initialize_client(self) -> bool:
        """初始化飞影客户端"""
        try:
            token = self.config_manager.get("hifly_token", "")
            if not token:
                self.log_message.emit("❌ 飞影API Token未配置，请在设置中配置Token")
                return False
            
            self.hifly_client = HiflyClient(token)
            
            # 连接信号
            self.hifly_client.log_message.connect(self.log_message.emit)
            
            # 测试连接
            self.log_message.emit("🔗 测试飞影API连接...")
            if self.hifly_client.test_connection():
                return True
            else:
                self.hifly_client = None
                return False
                
        except Exception as e:
            self.log_message.emit(f"❌ 初始化飞影客户端失败: {str(e)}")
            return False
    
    def load_pending_uploads(self) -> List[Dict[str, Any]]:
        """
        加载待上传的数据
        
        Returns:
            待上传的视频数据列表
        """
        try:
            if not os.path.exists(self.avatar_list_path):
                self.log_message.emit(f"❌ 数据文件不存在: {self.avatar_list_path}")
                return []

            self.log_message.emit(f"📊 读取数据文件: {self.avatar_list_path}")
            
            # 读取Excel文件
            df = pd.read_excel(self.avatar_list_path)
            
            # 筛选条件：是否上传飞影列不为"是"的行
            pending_df = df[df['是否上传飞影'] != '是'].copy()
            
            # 检查必要的列是否存在
            required_columns = ['ID', '视频URL', '拍摄演员名称']
            missing_columns = [col for col in required_columns if col not in pending_df.columns]
            
            if missing_columns:
                self.log_message.emit(f"❌ 数据文件缺少必要列: {missing_columns}")
                return []
            
            # 过滤掉缺少必要信息的行
            valid_df = pending_df.dropna(subset=required_columns)
            
            # 转换为字典列表
            upload_data = valid_df.to_dict('records')
            
            self.log_message.emit(f"📋 找到 {len(upload_data)} 条待上传数据")
            
            return upload_data
            
        except Exception as e:
            self.log_message.emit(f"❌ 加载待上传数据失败: {str(e)}")
            return []
    
    def update_upload_status(self, video_id: str, success: bool) -> bool:
        """
        更新上传状态到Excel文件
        
        Args:
            video_id: 视频ID
            success: 是否上传成功
            
        Returns:
            是否更新成功
        """
        try:
            # 读取Excel文件
            df = pd.read_excel(self.avatar_list_path)
            
            # 找到对应的行并更新
            mask = df['ID'] == video_id
            if mask.any():
                if success:
                    df.loc[mask, '是否上传飞影'] = '是'
                    # 同时更新"更新日期"为今天
                    from datetime import datetime
                    today = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    df.loc[mask, '更新日期'] = today
                    self.log_message.emit(f"✅ 已更新上传状态和日期: ID {video_id}")
                else:
                    # 失败时可以标记为"失败"或保持原状
                    df.loc[mask, '是否上传飞影'] = '失败'
                    self.log_message.emit(f"❌ 已标记上传失败: ID {video_id}")
                
                # 保存文件
                df.to_excel(self.avatar_list_path, index=False)
                return True
            else:
                self.log_message.emit(f"⚠️ 未找到对应的视频记录: ID {video_id}")
                return False
                
        except Exception as e:
            self.log_message.emit(f"❌ 更新上传状态失败: {str(e)}")
            return False
    
    def start_upload(self) -> bool:
        """开始上传流程"""
        try:
            # 初始化客户端
            if not self.initialize_client():
                return False
            
            # 加载待上传数据
            self.upload_queue = self.load_pending_uploads()
            
            if not self.upload_queue:
                self.log_message.emit("📋 没有需要上传的数据")
                self.upload_completed.emit(True, "没有需要上传的数据")
                return True
            
            # 重置计数器
            self.current_index = 0
            self.total_count = len(self.upload_queue)
            self.success_count = 0
            self.failed_count = 0
            
            self.log_message.emit(f"🚀 开始批量上传，共 {self.total_count} 个任务")
            
            # 启动上传线程
            self.upload_thread = HiflyUploadThread(self)
            self.upload_thread.start()
            
            return True
            
        except Exception as e:
            self.log_message.emit(f"❌ 启动上传失败: {str(e)}")
            self.upload_completed.emit(False, f"启动上传失败: {str(e)}")
            return False
    
    def process_upload_queue(self):
        """处理上传队列"""
        try:
            for i, video_data in enumerate(self.upload_queue):
                self.current_index = i + 1
                
                # 提取信息
                video_id = video_data.get("ID")
                actor_name = video_data.get("拍摄演员名称")
                video_url = video_data.get("视频URL")
                
                task_name = f"{actor_name}-{video_id}-演员"
                
                # 发送进度信号
                self.upload_progress.emit(self.current_index, self.total_count, task_name)
                
                self.log_message.emit(f"📤 [{self.current_index}/{self.total_count}] 开始上传: {task_name}")
                
                # 执行上传
                success = self.hifly_client.upload_avatar_from_video_data(video_data)
                
                # 更新状态
                self.update_upload_status(video_id, success)
                
                if success:
                    self.success_count += 1
                    self.log_message.emit(f"✅ [{self.current_index}/{self.total_count}] 上传成功: {task_name}")
                else:
                    self.failed_count += 1
                    self.log_message.emit(f"❌ [{self.current_index}/{self.total_count}] 上传失败: {task_name}")
                
                # 添加延迟，避免API限制
                if i < len(self.upload_queue) - 1:  # 不是最后一个
                    self.log_message.emit("⏳ 等待5秒后继续下一个任务...")
                    time.sleep(5)
            
            # 完成总结
            self.log_message.emit(f"🎉 批量上传完成！")
            self.log_message.emit(f"📊 总计: {self.total_count}, 成功: {self.success_count}, 失败: {self.failed_count}")
            
            if self.failed_count == 0:
                self.upload_completed.emit(True, f"全部上传成功！共 {self.success_count} 个")
            else:
                self.upload_completed.emit(True, f"上传完成，成功 {self.success_count} 个，失败 {self.failed_count} 个")
                
        except Exception as e:
            self.log_message.emit(f"❌ 处理上传队列异常: {str(e)}")
            self.upload_completed.emit(False, f"上传异常: {str(e)}")
    
    def stop_upload(self):
        """停止上传"""
        try:
            if self.upload_thread and self.upload_thread.isRunning():
                self.log_message.emit("🛑 正在停止上传...")
                self.upload_thread.terminate()
                self.upload_thread.wait(5000)  # 等待5秒
                self.log_message.emit("✅ 上传已停止")
            
        except Exception as e:
            self.log_message.emit(f"❌ 停止上传异常: {str(e)}")
    
    def get_upload_statistics(self) -> Dict[str, int]:
        """获取上传统计信息"""
        return {
            "total": self.total_count,
            "current": self.current_index,
            "success": self.success_count,
            "failed": self.failed_count,
            "remaining": self.total_count - self.current_index
        }
