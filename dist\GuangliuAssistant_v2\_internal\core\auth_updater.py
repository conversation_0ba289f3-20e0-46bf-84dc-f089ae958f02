"""
授权更新器 - 简化版本，不依赖playwright
提供手动输入授权信息的功能
"""

import json
import os
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QTextEdit, QPushButton, QMessageBox, QTabWidget,
                               QWidget, QLineEdit, QFormLayout)
from PySide6.QtCore import Qt


class AuthUpdaterDialog(QDialog):
    """授权更新对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("更新HiFly平台授权信息")
        self.setModal(True)
        self.resize(600, 500)
        
        self.auth_file = "feiyingshuziren/essential_auth_data.json"
        
        self.setup_ui()
        self.load_existing_auth()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 说明文本
        info_label = QLabel("""
<b>HiFly平台授权信息更新</b><br><br>
请按照以下步骤获取授权信息：<br>
1. 打开Chrome浏览器，访问 https://light.hifly.cc/<br>
2. 登录您的账户<br>
3. 按F12打开开发者工具<br>
4. 切换到"应用程序"(Application)标签<br>
5. 在左侧找到"本地存储空间"(Local Storage) → https://light.hifly.cc<br>
6. 复制token值到下方输入框中
        """)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 创建标签页
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # Token输入标签页
        token_tab = QWidget()
        token_layout = QFormLayout(token_tab)
        
        self.token_input = QLineEdit()
        self.token_input.setPlaceholderText("请输入从浏览器localStorage中获取的token值")
        token_layout.addRow("Token:", self.token_input)
        
        tab_widget.addTab(token_tab, "Token输入")
        
        # 完整JSON输入标签页
        json_tab = QWidget()
        json_layout = QVBoxLayout(json_tab)
        
        json_info = QLabel("如果您有完整的认证JSON数据，可以直接粘贴到下方：")
        json_layout.addWidget(json_info)
        
        self.json_input = QTextEdit()
        self.json_input.setPlaceholderText("""
{
  "cookies": [...],
  "localStorage": {
    "token": "your_token_here",
    ...
  }
}
        """)
        json_layout.addWidget(self.json_input)
        
        tab_widget.addTab(json_tab, "完整JSON")
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.test_btn = QPushButton("测试连接")
        self.test_btn.clicked.connect(self.test_connection)
        button_layout.addWidget(self.test_btn)
        
        button_layout.addStretch()
        
        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_auth)
        button_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def load_existing_auth(self):
        """加载现有的授权信息"""
        if os.path.exists(self.auth_file):
            try:
                with open(self.auth_file, "r", encoding="utf-8") as f:
                    auth_data = json.load(f)
                
                # 显示现有token（部分隐藏）
                token = auth_data.get("localStorage", {}).get("token", "")
                if token:
                    if len(token) > 20:
                        display_token = token[:10] + "..." + token[-10:]
                    else:
                        display_token = token
                    self.token_input.setText(display_token)
                
                # 显示完整JSON（格式化）
                formatted_json = json.dumps(auth_data, ensure_ascii=False, indent=2)
                self.json_input.setText(formatted_json)
                
            except Exception as e:
                QMessageBox.warning(self, "警告", f"读取现有授权文件失败: {e}")
    
    def test_connection(self):
        """测试连接"""
        # 这里可以添加测试逻辑
        QMessageBox.information(self, "测试连接", "连接测试功能暂未实现\n\n保存后可以通过上传功能验证授权是否有效")
    
    def save_auth(self):
        """保存授权信息"""
        try:
            # 优先使用JSON输入
            json_text = self.json_input.toPlainText().strip()
            if json_text:
                try:
                    auth_data = json.loads(json_text)
                except json.JSONDecodeError as e:
                    QMessageBox.critical(self, "错误", f"JSON格式错误: {e}")
                    return
            else:
                # 使用Token输入
                token = self.token_input.text().strip()
                if not token:
                    QMessageBox.warning(self, "警告", "请输入Token或完整的JSON数据")
                    return
                
                # 构建基本的认证数据结构
                auth_data = {
                    "cookies": [],
                    "localStorage": {
                        "token": token
                    }
                }
            
            # 确保目录存在
            os.makedirs(os.path.dirname(self.auth_file), exist_ok=True)
            
            # 保存到文件
            with open(self.auth_file, "w", encoding="utf-8") as f:
                json.dump(auth_data, f, ensure_ascii=False, indent=2)
            
            QMessageBox.information(self, "成功", f"授权信息已保存到: {self.auth_file}")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存授权信息失败: {e}")


def show_auth_updater(parent=None):
    """显示授权更新对话框"""
    dialog = AuthUpdaterDialog(parent)
    return dialog.exec() == QDialog.Accepted
