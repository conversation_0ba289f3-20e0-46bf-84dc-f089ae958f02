"""
测试筛选条件修复
"""

import os
import pandas as pd
from datetime import datetime
import numpy as np


def test_filter_conditions():
    """测试筛选条件修复"""
    print("=" * 60)
    print("🧪 测试筛选条件修复")
    print("=" * 60)
    
    avatar_list_path = "data/avatar_list.xlsx"
    if not os.path.exists(avatar_list_path):
        print("❌ 数据文件不存在")
        return
    
    # 读取Excel文件
    df = pd.read_excel(avatar_list_path)
    print(f"📊 总数据量: {len(df)} 条")
    
    # 检查"是否重命名"列的数据类型和值
    print(f"\n🔍 '是否重命名'列分析:")
    rename_col = df["是否重命名"]
    print(f"  数据类型: {rename_col.dtype}")
    print(f"  唯一值: {rename_col.unique()}")
    print(f"  值计数:")
    for value, count in rename_col.value_counts(dropna=False).items():
        print(f"    {repr(value)}: {count} 条")
    
    # 测试不同的筛选条件
    print(f"\n🔍 筛选条件测试:")
    
    # 原始条件（可能有问题）
    mask_old = (df["是否上传飞影"] == "是") & (df["是否重命名"] != "是")
    old_count = mask_old.sum()
    print(f"  原始条件 (是否重命名 != '是'): {old_count} 条")
    
    # 新条件（修复后）
    mask_new = (df["是否上传飞影"] == "是") & (
        (df["是否重命名"].isna()) |  # NaN值
        (df["是否重命名"] == "") |   # 空字符串
        (df["是否重命名"] != "是")   # 其他值
    )
    new_count = mask_new.sum()
    print(f"  新条件 (处理NaN): {new_count} 条")
    
    # 分别测试各个条件
    mask_na = (df["是否上传飞影"] == "是") & (df["是否重命名"].isna())
    na_count = mask_na.sum()
    print(f"  只匹配NaN值: {na_count} 条")
    
    mask_empty = (df["是否上传飞影"] == "是") & (df["是否重命名"] == "")
    empty_count = mask_empty.sum()
    print(f"  只匹配空字符串: {empty_count} 条")
    
    mask_not_yes = (df["是否上传飞影"] == "是") & (df["是否重命名"] != "是") & (~df["是否重命名"].isna())
    not_yes_count = mask_not_yes.sum()
    print(f"  只匹配非'是'的其他值: {not_yes_count} 条")
    
    # 显示符合条件的记录
    if new_count > 0:
        print(f"\n📋 符合新条件的记录 (前5条):")
        result_df = df[mask_new]
        for i, (_, row) in enumerate(result_df.head(5).iterrows()):
            actor_name = str(row.get("拍摄演员名称", "")).strip()
            video_id = str(row.get("ID", "")).strip()
            upload_status = row.get("是否上传飞影", "")
            rename_status = row.get("是否重命名", "")
            print(f"    {i+1}. {actor_name}-{video_id} | 上传: '{upload_status}' | 重命名: {repr(rename_status)}")
    
    # 测试今天的筛选
    today = datetime.now().date()
    df["更新日期"] = pd.to_datetime(df["更新日期"], errors='coerce').dt.date
    
    mask_today = (df["是否上传飞影"] == "是") & (
        (df["是否重命名"].isna()) |
        (df["是否重命名"] == "") |
        (df["是否重命名"] != "是")
    ) & (df["更新日期"] == today)
    
    today_count = mask_today.sum()
    print(f"\n📅 今天({today})符合条件的记录: {today_count} 条")
    
    if today_count > 0:
        print(f"📋 今天符合条件的记录:")
        today_df = df[mask_today]
        for i, (_, row) in enumerate(today_df.iterrows()):
            actor_name = str(row.get("拍摄演员名称", "")).strip()
            video_id = str(row.get("ID", "")).strip()
            rename_status = row.get("是否重命名", "")
            print(f"    {i+1}. {actor_name}-{video_id} | 重命名: {repr(rename_status)}")
    
    print(f"\n💡 修复总结:")
    if new_count > old_count:
        print(f"  ✅ 修复成功！新条件找到了更多记录")
        print(f"     原始: {old_count} 条 → 修复后: {new_count} 条")
    elif new_count == old_count and new_count > 0:
        print(f"  ✅ 条件正常，找到 {new_count} 条记录")
    else:
        print(f"  ⚠️ 仍然没有找到符合条件的记录")
    
    print("\n" + "=" * 60)
    print("🎯 测试完成")
    print("=" * 60)


if __name__ == "__main__":
    test_filter_conditions()
