#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试优化后的Chrome进程管理功能
"""

import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, "src")
sys.path.insert(0, src_dir)

def test_video_material_manager():
    """测试视频素材管理器的基本功能"""
    try:
        print("🧪 开始测试视频素材管理器优化...")
        
        # 导入必要的模块
        from core.config_manager import ConfigManager
        from core.video_material_manager import VideoMaterialManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        print("✅ 配置管理器创建成功")
        
        # 创建视频素材管理器
        vm_manager = VideoMaterialManager(config_manager)
        print("✅ 视频素材管理器创建成功")
        
        # 测试Chrome进程检查功能
        has_chrome = vm_manager.check_existing_chrome_processes()
        print(f"🔍 当前Chrome进程状态: {'存在' if has_chrome else '无'}")
        
        # 测试调试端口检查
        debug_available = vm_manager.check_debug_browser()
        print(f"🔍 调试端口状态: {'可用' if debug_available else '不可用'}")
        
        # 测试临时文件清理功能
        vm_manager._cleanup_temp_file()
        print("✅ 临时文件清理功能测试完成")
        
        # 测试最近数据获取
        recent_data = vm_manager.get_recent_week_data()
        print(f"📊 最近7天数据: {len(recent_data)} 条记录")
        
        # 显示列映射测试
        if not recent_data.empty:
            display_data = vm_manager.get_display_columns(recent_data)
            print(f"📋 显示列数: {len(display_data.columns)}")
        
        print("✅ 所有基础功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False

def test_chrome_cleanup():
    """测试Chrome进程清理功能"""
    try:
        print("\n🧪 开始测试Chrome进程清理功能...")
        
        from core.config_manager import ConfigManager
        from core.video_material_manager import VideoMaterialManager
        
        config_manager = ConfigManager()
        vm_manager = VideoMaterialManager(config_manager)
        
        # 测试Chrome进程清理
        print("🧹 执行Chrome进程清理测试...")
        vm_manager.cleanup_chrome_processes()
        print("✅ Chrome进程清理功能测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ Chrome清理测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始优化功能测试...")
    print("=" * 50)
    
    success = True
    
    # 测试基础功能
    if not test_video_material_manager():
        success = False
    
    # 测试Chrome清理功能
    if not test_chrome_cleanup():
        success = False
    
    print("=" * 50)
    if success:
        print("🎉 所有优化功能测试通过！")
        print("\n主要优化内容:")
        print("✅ 修复了临时文件清理的变量名错误")
        print("✅ 添加了Chrome进程生命周期管理")
        print("✅ 改进了导航失败的错误处理")
        print("✅ 添加了应用关闭时的自动清理")
        print("✅ 优化了Chrome进程的精准控制")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
        sys.exit(1)