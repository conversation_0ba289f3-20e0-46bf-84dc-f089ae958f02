"""
测试缓存修复
"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta

# 添加src路径
sys.path.append('src')


def test_cache_and_timing():
    """测试缓存和时机问题的修复"""
    print("=" * 60)
    print("🧪 测试缓存和时机问题的修复")
    print("=" * 60)
    
    avatar_list_path = "data/avatar_list.xlsx"
    if not os.path.exists(avatar_list_path):
        print("❌ 数据文件不存在")
        return
    
    print("📋 修复内容:")
    print("  1. ✅ 在重命名前清除数据管理器缓存")
    print("  2. ✅ 延迟时间从1秒增加到2秒")
    print("  3. ✅ 添加详细的调试日志")
    
    # 模拟读取最新数据
    print(f"\n🔍 模拟读取最新数据:")
    
    # 第一次读取
    df1 = pd.read_excel(avatar_list_path)
    print(f"  第一次读取: {len(df1)} 条记录")
    
    # 模拟清除缓存后再次读取
    import time
    time.sleep(0.1)  # 模拟延迟
    df2 = pd.read_excel(avatar_list_path)
    print(f"  第二次读取: {len(df2)} 条记录")
    
    # 检查数据一致性
    if len(df1) == len(df2):
        print("  ✅ 数据读取一致")
    else:
        print("  ❌ 数据读取不一致")
    
    # 检查最新的上传记录
    print(f"\n🔍 检查最新的上传记录:")
    df2_sorted = df2.sort_values('更新日期', ascending=False)
    today = datetime.now().date()
    
    # 查找今天更新的记录
    df2["更新日期"] = pd.to_datetime(df2["更新日期"], errors='coerce').dt.date
    today_records = df2[df2["更新日期"] == today]
    
    print(f"  今天更新的记录: {len(today_records)} 条")
    
    if len(today_records) > 0:
        print(f"  今天更新的记录详情:")
        for i, (_, row) in enumerate(today_records.iterrows(), 1):
            video_id = row.get('ID', '')
            actor_name = row.get('拍摄演员名称', '')
            upload_status = row.get('是否上传飞影', '')
            rename_status = row.get('是否重命名', '')
            update_time = row.get('更新日期', '')
            print(f"    {i}. {actor_name}-{video_id}: 上传={upload_status}, 重命名={repr(rename_status)}")
    
    # 测试筛选逻辑
    print(f"\n🔍 测试筛选逻辑:")
    
    three_days_ago = today - timedelta(days=3)
    
    mask_upload = (df2["是否上传飞影"] == "是")
    mask_rename = (
        (df2["是否重命名"].isna()) |
        (df2["是否重命名"] == "") |
        (df2["是否重命名"] != "是")
    )
    mask_date = (df2["更新日期"] >= three_days_ago)
    
    print(f"  筛选条件:")
    print(f"    上传飞影='是': {mask_upload.sum()} 条")
    print(f"    重命名状态需要处理: {mask_rename.sum()} 条")
    print(f"    更新日期>={three_days_ago}: {mask_date.sum()} 条")
    
    mask_combined = mask_upload & mask_rename & mask_date
    print(f"    最终筛选结果: {mask_combined.sum()} 条")
    
    if mask_combined.sum() > 0:
        print(f"  ✅ 筛选逻辑正常，找到需要重命名的视频")
        result_df = df2[mask_combined]
        for i, (_, row) in enumerate(result_df.head(3).iterrows(), 1):
            video_id = row.get('ID', '')
            actor_name = row.get('拍摄演员名称', '')
            print(f"    {i}. {actor_name}-{video_id}")
    else:
        print(f"  ❌ 筛选逻辑仍有问题")
    
    print(f"\n💡 修复总结:")
    print(f"  1. ✅ 增加了缓存清理逻辑")
    print(f"  2. ✅ 增加了延迟时间")
    print(f"  3. ✅ 增加了详细调试日志")
    print(f"  4. ✅ 确保读取最新的Excel数据")
    
    print(f"\n🚀 下一步测试:")
    print(f"  1. 重启主程序")
    print(f"  2. 进行飞影上传测试")
    print(f"  3. 观察调试日志输出")
    print(f"  4. 确认是否能找到需要重命名的视频")


if __name__ == "__main__":
    test_cache_and_timing()
