[2025-08-05 13:54:06] 日志文件: D:\project\guangliu02\feiyingshuziren\log\2025-08-05\video_management_controller_13-54-06.log
[2025-08-05 13:54:06] 视频管理自动化控制器启动
[2025-08-05 13:54:06] 任务类型: full_process
[2025-08-05 13:54:06] 无头模式: False
[2025-08-05 13:54:06] 开始时间: 2025-08-05 13:54:06
[2025-08-05 13:54:06] 控制器初始化完成
[2025-08-05 13:54:06] 任务类型: full_process
[2025-08-05 13:54:06] 无头模式: False
[2025-08-05 13:54:06] 工作目录: D:\project\guangliu02
[2025-08-05 13:54:06] 源码目录: D:\project\guangliu02\src
[2025-08-05 13:54:06] [CONTROLLER] 将执行 3 个任务: material, upload, rename
[2025-08-05 13:54:06] 
[CONTROLLER] 第1步：素材更新
[2025-08-05 13:54:06] ============================================================
[2025-08-05 13:54:06] [MATERIAL] 开始执行素材更新任务
[2025-08-05 13:54:06] ============================================================
[2025-08-05 13:54:07] [MATERIAL] 开始下载素材数据...
[2025-08-05 13:54:57] [MATERIAL] 素材更新任务完成
[2025-08-05 13:54:57] 
[CONTROLLER] 第2步：飞影上传
[2025-08-05 13:54:57] ============================================================
[2025-08-05 13:54:57] [UPLOAD] 开始执行飞影上传任务
[2025-08-05 13:54:57] ============================================================
[2025-08-05 13:54:57] [UPLOAD] 获取待上传的视频列表...
[2025-08-05 13:55:00] [UPLOAD] 获取到数据: <class 'pandas.core.frame.DataFrame'>, 行数: 33
[2025-08-05 13:55:00] [UPLOAD] 没有需要上传的视频
[2025-08-05 13:55:00] 
[CONTROLLER] 第3步：自动重命名
[2025-08-05 13:55:00] ============================================================
[2025-08-05 13:55:00] [RENAME] 开始执行自动重命名任务
[2025-08-05 13:55:00] ============================================================
[2025-08-05 13:55:00] [RENAME] 获取需要重命名的视频列表...
[2025-08-05 13:55:03] [RENAME] 获取到数据: <class 'pandas.core.frame.DataFrame'>, 行数: 33
[2025-08-05 13:55:03] [RENAME] 没有需要重命名的视频
[2025-08-05 13:55:03] 
============================================================
[2025-08-05 13:55:03] [CONTROLLER] 任务执行总结
[2025-08-05 13:55:03] ============================================================
[2025-08-05 13:55:03] 任务类型: full_process
[2025-08-05 13:55:03] 总任务数: 3
[2025-08-05 13:55:03] 完成任务: 3
[2025-08-05 13:55:03] 失败任务: 0
[2025-08-05 13:55:03] 成功率: 100.0%
[2025-08-05 13:55:03] [CONTROLLER] 所有任务执行成功！
[2025-08-05 13:55:03] 开始时间: 2025-08-05 13:54:06
[2025-08-05 13:55:03] 结束时间: 2025-08-05 13:55:03
[2025-08-05 13:55:03] 总耗时: 56.2秒
[2025-08-05 13:55:03] [CONTROLLER] 视频管理自动化任务结束
