"""
测试最终修复
"""

import os
import sys
import json
import subprocess

# 添加src路径
sys.path.append('src')


def test_final_fix():
    """测试最终修复"""
    print("=" * 60)
    print("🎯 测试最终修复")
    print("=" * 60)
    
    print("🔧 修复内容总结:")
    print("  1. ✅ 编码问题修复:")
    print("     - subprocess使用UTF-8编码")
    print("     - 移除emoji字符，使用ASCII安全前缀")
    print("     - JSON数据使用ensure_ascii=True")
    print("")
    print("  2. ✅ 路径问题修复:")
    print("     - 正确计算项目根目录")
    print("     - 使用绝对路径到认证文件")
    print("     - 验证文件存在性")
    print("")
    print("  3. ✅ 异步清理修复:")
    print("     - 浏览器关闭后额外等待")
    print("     - 清理所有挂起任务")
    print("     - 安全关闭事件循环")
    print("")
    
    print("🎯 预期效果:")
    print("  ✅ 不再有编码错误")
    print("  ✅ 认证文件能正确加载")
    print("  ✅ 浏览器能正常工作")
    print("  ✅ 重命名任务能成功")
    print("  ✅ 进程能优雅退出")
    print("  ✅ 不再有asyncio错误")
    print("")
    
    print("📋 完整的执行流程:")
    print("  主程序:")
    print("    1. 用户点击飞影上传")
    print("    2. 清理video_list数据")
    print("    3. 生成ASCII安全JSON")
    print("    4. 启动独立进程")
    print("    5. 监控进程状态")
    print("    6. 解析结果并更新UI")
    print("")
    print("  独立进程:")
    print("    1. 设置UTF-8输出")
    print("    2. 计算认证文件路径")
    print("    3. 加载认证数据")
    print("    4. 初始化浏览器")
    print("    5. 执行重命名任务")
    print("    6. 安全关闭浏览器")
    print("    7. 清理异步资源")
    print("    8. 输出JSON结果")
    print("")
    
    print("🔍 关键日志标识:")
    print("  进程启动:")
    print("    '[START] 独立进程开始重命名...'")
    print("    '[AUTH] 认证文件路径: ...'")
    print("    '[AUTH] 文件存在: True'")
    print("")
    print("  浏览器操作:")
    print("    '[INIT] 正在初始化浏览器...'")
    print("    '[OK] 浏览器初始化完成'")
    print("    '[PROCESS] [1/1] 处理视频: ...'")
    print("")
    print("  任务完成:")
    print("    '[SUCCESS] ... 重命名成功'")
    print("    '[CLOSE] 正在关闭浏览器...'")
    print("    '[CLEANUP] 清理挂起任务...'")
    print("    '[OK] 事件循环已关闭'")
    print("")
    
    print("🚀 测试建议:")
    print("  1. 重启主程序: python src/main.py")
    print("  2. 进行飞影上传测试")
    print("  3. 观察详细的进程日志")
    print("  4. 检查是否出现以上关键日志")
    print("  5. 确认重命名成功率")
    print("")
    
    print("💡 如果还有问题:")
    print("  可以独立测试子进程:")
    print("  test_data = '{\"video_list\":[{\"actor_name\":\"测试\",\"video_id\":\"123\"}],\"headless\":true}'")
    print("  python src/core/rename_process.py \"$test_data\"")
    print("")
    
    print("=" * 60)
    print("🎯 最终修复完成")
    print("=" * 60)


def test_auth_file_exists():
    """测试认证文件是否存在"""
    print("\n" + "=" * 40)
    print("🔍 检查认证文件")
    print("=" * 40)
    
    # 使用修复后的路径计算逻辑
    current_dir = os.getcwd()
    auth_file_path = os.path.join(current_dir, "feiyingshuziren", "essential_auth_data.json")
    
    print(f"📁 项目目录: {current_dir}")
    print(f"📄 认证文件路径: {auth_file_path}")
    print(f"✅ 文件存在: {os.path.exists(auth_file_path)}")
    
    if os.path.exists(auth_file_path):
        try:
            with open(auth_file_path, 'r', encoding='utf-8') as f:
                auth_data = json.load(f)
            cookies_count = len(auth_data.get('cookies', []))
            print(f"📊 包含 {cookies_count} 个cookie")
            print("✅ 认证文件格式正确")
        except Exception as e:
            print(f"❌ 认证文件格式错误: {str(e)}")
    else:
        print("❌ 认证文件不存在，请确保已完成飞影登录")


if __name__ == "__main__":
    test_final_fix()
    test_auth_file_exists()
