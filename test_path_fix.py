"""
测试路径修复
"""

import os
import sys

# 添加src路径
sys.path.append('src')


def test_path_fix():
    """测试路径修复"""
    print("=" * 60)
    print("🔧 测试路径修复")
    print("=" * 60)
    
    print(f"📂 当前工作目录: {os.getcwd()}")
    
    # 模拟main_window.py中的路径计算
    # 假设当前文件在项目根目录，main_window.py在src/ui/目录下
    
    # 模拟从src/ui/main_window.py计算项目根目录
    # __file__ 相当于 src/ui/main_window.py
    simulated_main_window_path = os.path.join("src", "ui", "main_window.py")
    print(f"🔍 模拟main_window.py路径: {simulated_main_window_path}")
    
    # 计算项目根目录：从src/ui/main_window.py向上3级
    # os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(simulated_main_window_path)))
    print(f"🔍 计算的项目根目录: {project_root}")
    
    # 如果项目根目录是空字符串，说明是当前目录
    if not project_root:
        project_root = "."
    
    # 构建Excel文件路径
    avatar_list_path = os.path.join(project_root, "data", "avatar_list.xlsx")
    print(f"🔍 构建的Excel路径: {avatar_list_path}")
    print(f"🔍 绝对路径: {os.path.abspath(avatar_list_path)}")
    print(f"🔍 文件是否存在: {os.path.exists(avatar_list_path)}")
    
    # 测试实际的路径计算
    print(f"\n🔍 实际路径测试:")
    
    # 模拟在src目录下运行时的情况
    os.chdir("src")
    print(f"📂 切换到src目录: {os.getcwd()}")
    
    # 重新计算路径
    current_file = os.path.join("ui", "main_window.py")  # 模拟__file__
    project_root_from_src = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
    if not project_root_from_src:
        project_root_from_src = ".."  # 上一级目录
    
    avatar_list_path_from_src = os.path.join(project_root_from_src, "data", "avatar_list.xlsx")
    print(f"🔍 从src目录计算的路径: {avatar_list_path_from_src}")
    print(f"🔍 绝对路径: {os.path.abspath(avatar_list_path_from_src)}")
    print(f"🔍 文件是否存在: {os.path.exists(avatar_list_path_from_src)}")
    
    # 切换回原目录
    os.chdir("..")
    print(f"📂 切换回原目录: {os.getcwd()}")
    
    print(f"\n💡 修复总结:")
    print(f"  ✅ 使用项目根目录的绝对路径")
    print(f"  ✅ 不依赖当前工作目录")
    print(f"  ✅ 确保在任何目录下运行都能找到文件")
    
    # 验证修复后的路径逻辑
    print(f"\n🔍 验证修复后的路径逻辑:")
    
    # 这是修复后的逻辑
    script_dir = os.path.dirname(os.path.abspath(__file__))  # 当前脚本目录
    project_root_fixed = script_dir  # 当前脚本在项目根目录
    avatar_list_path_fixed = os.path.join(project_root_fixed, "data", "avatar_list.xlsx")
    
    print(f"🔍 修复后的路径: {avatar_list_path_fixed}")
    print(f"🔍 文件是否存在: {os.path.exists(avatar_list_path_fixed)}")
    
    if os.path.exists(avatar_list_path_fixed):
        print(f"✅ 路径修复成功！")
    else:
        print(f"❌ 路径修复失败")


if __name__ == "__main__":
    test_path_fix()
