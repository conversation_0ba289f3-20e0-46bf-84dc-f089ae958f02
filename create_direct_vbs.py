"""
创建直接调用控制器的VBS文件
"""

import os
import sys


def create_direct_controller_vbs():
    """创建直接调用控制器的VBS文件"""
    print("=" * 60)
    print("🔧 创建直接调用控制器的VBS文件")
    print("=" * 60)
    
    print("🔍 问题分析:")
    print("  BAT文件有编码问题，导致执行失败")
    print("  解决方案: VBS直接调用Python控制器")
    print("")
    
    # 获取Python可执行文件路径
    python_exe = sys.executable
    print(f"Python路径: {python_exe}")
    
    # 获取控制器路径
    current_dir = os.getcwd()
    controller_path = os.path.join(current_dir, "src", "video_management_controller.py")
    controller_path = os.path.abspath(controller_path)
    
    print(f"控制器路径: {controller_path}")
    print(f"控制器存在: {os.path.exists(controller_path)}")
    print("")
    
    # 创建新的VBS文件内容
    vbs_content = f'''Set WshShell = CreateObject("WScript.Shell")
WshShell.CurrentDirectory = "{current_dir}"
WshShell.Run """{python_exe} {controller_path} --task-type full_process""", 0, False
'''
    
    print("新VBS文件内容:")
    print("  " + vbs_content.replace('\n', '\n  '))
    
    # 写入VBS文件
    vbs_path = os.path.join(current_dir, "run_video_management_scheduled.vbs")
    
    try:
        with open(vbs_path, 'w', encoding='utf-8') as f:
            f.write(vbs_content)
        print(f"✅ VBS文件已更新: {vbs_path}")
        return vbs_path
    except Exception as e:
        print(f"❌ 更新VBS文件失败: {e}")
        return None


def test_direct_vbs(vbs_path):
    """测试直接VBS文件"""
    print("\n" + "=" * 40)
    print("🧪 测试直接VBS文件")
    print("=" * 40)
    
    if not vbs_path or not os.path.exists(vbs_path):
        print("❌ VBS文件不存在")
        return False
    
    print(f"测试执行: wscript.exe \"{vbs_path}\"")
    print("注意: 这会直接调用Python控制器")
    print("")
    
    import subprocess
    try:
        result = subprocess.run([
            "wscript.exe", vbs_path
        ], capture_output=True, text=True, timeout=60)
        
        print(f"执行结果: 返回码 {result.returncode}")
        if result.stdout:
            print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
        
        if result.returncode == 0:
            print("✅ VBS文件执行成功")
            
            # 检查日志文件
            print("\n📋 检查日志文件:")
            log_dir = "feiyingshuziren/log"
            if os.path.exists(log_dir):
                import datetime
                today = datetime.datetime.now().strftime("%Y-%m-%d")
                today_log_dir = os.path.join(log_dir, today)
                
                if os.path.exists(today_log_dir):
                    log_files = os.listdir(today_log_dir)
                    controller_logs = [f for f in log_files if 'controller' in f]
                    
                    if controller_logs:
                        print(f"  ✅ 找到日志文件: {len(controller_logs)} 个")
                        
                        # 显示最新的几个日志
                        latest_logs = sorted(controller_logs)[-3:]
                        for log in latest_logs:
                            log_path = os.path.join(today_log_dir, log)
                            mod_time = os.path.getmtime(log_path)
                            mod_datetime = datetime.datetime.fromtimestamp(mod_time)
                            print(f"    {log} - {mod_datetime.strftime('%H:%M:%S')}")
                    else:
                        print("  ⚠️ 没有找到controller日志文件")
                else:
                    print(f"  ⚠️ 今天的日志目录不存在: {today_log_dir}")
            else:
                print(f"  ⚠️ 日志根目录不存在: {log_dir}")
            
            return True
        else:
            print("❌ VBS文件执行失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ VBS文件执行超时（60秒）")
        return False
    except Exception as e:
        print(f"❌ 执行VBS文件时出错: {e}")
        return False


def update_system_task_creation():
    """更新系统任务创建逻辑"""
    print("\n" + "=" * 40)
    print("🔧 系统任务创建建议")
    print("=" * 40)
    
    print("建议修改系统任务创建逻辑:")
    print("")
    print("修改前 (通过BAT文件):")
    print("  VBS → BAT → Python控制器")
    print("  问题: BAT文件编码问题")
    print("")
    print("修改后 (直接调用):")
    print("  VBS → Python控制器")
    print("  优势: 避免BAT文件编码问题")
    print("")
    
    print("修改代码:")
    print("""
# 在create_video_management_vbs_file方法中
def create_video_management_vbs_file(self, vbs_path, controller_path):
    \"\"\"创建视频管理VBS文件 - 直接调用控制器\"\"\"
    import sys
    python_exe = sys.executable
    
    # 确保使用绝对路径
    if not os.path.isabs(controller_path):
        controller_path = os.path.abspath(controller_path)
    
    # 获取工作目录
    work_dir = os.path.dirname(controller_path)
    if work_dir.endswith('src'):
        work_dir = os.path.dirname(work_dir)
    
    # VBS直接调用Python控制器
    vbs_content = f'''Set WshShell = CreateObject("WScript.Shell")
WshShell.CurrentDirectory = "{work_dir}"
WshShell.Run """{python_exe} {controller_path} --task-type full_process""", 0, False
'''
    
    with open(vbs_path, 'w', encoding='utf-8') as f:
        f.write(vbs_content)
""")
    print("")


def show_final_solution():
    """显示最终解决方案"""
    print("\n" + "=" * 40)
    print("🎯 最终解决方案")
    print("=" * 40)
    
    print("问题根源:")
    print("  ✅ 路径问题已修复")
    print("  ✅ 删除逻辑已修复")
    print("  ❌ BAT文件编码问题")
    print("")
    
    print("解决方案:")
    print("  1. VBS文件直接调用Python控制器")
    print("  2. 绕过有编码问题的BAT文件")
    print("  3. 保持所有其他修复不变")
    print("")
    
    print("优势:")
    print("  ✅ 避免BAT文件编码问题")
    print("  ✅ 减少调用链条")
    print("  ✅ 更直接的错误处理")
    print("  ✅ 更好的性能")
    print("")
    
    print("现在可以:")
    print("  1. 使用新的VBS文件")
    print("  2. 在程序中创建系统任务")
    print("  3. 验证任务正常执行")
    print("  4. 测试删除功能")


if __name__ == "__main__":
    print("🔧 直接调用控制器VBS创建工具")
    print("=" * 60)
    
    # 创建直接调用控制器的VBS文件
    vbs_path = create_direct_controller_vbs()
    
    if vbs_path:
        # 询问是否测试
        print("\n" + "=" * 60)
        test_choice = input("是否测试新的VBS文件? (y/n): ").lower().strip()
        if test_choice == 'y':
            test_direct_vbs(vbs_path)
    
    # 显示系统任务创建建议
    update_system_task_creation()
    
    # 显示最终解决方案
    show_final_solution()
    
    print("\n" + "=" * 60)
    print("🎉 直接调用方案创建完成！")
    print("现在VBS文件会直接调用Python控制器")
    print("避免了BAT文件的编码问题")
    print("=" * 60)
