"""
修复系统任务VBS文件问题
"""

import os
import sys
import subprocess

# 添加src路径
sys.path.append('src')


def fix_vbs_file():
    """修复VBS文件路径问题"""
    print("=" * 60)
    print("🔧 修复系统任务VBS文件问题")
    print("=" * 60)
    
    print("🔍 问题分析:")
    print("  系统任务返回码: 0 (成功)")
    print("  但没有生成日志文件")
    print("  原因: VBS文件使用相对路径，工作目录不正确")
    print("")
    
    print("📁 当前VBS文件内容:")
    vbs_path = "run_video_management_scheduled.vbs"
    if os.path.exists(vbs_path):
        with open(vbs_path, 'r', encoding='utf-8') as f:
            content = f.read()
        print("  " + content.replace('\n', '\n  '))
    else:
        print("  VBS文件不存在")
    print("")
    
    print("🔧 修复方案:")
    print("  1. 使用绝对路径")
    print("  2. 设置正确的工作目录")
    print("  3. 确保BAT文件可以找到")
    print("")
    
    # 生成修复后的VBS文件
    current_dir = os.getcwd()
    bat_path = os.path.join(current_dir, "run_video_management_auto.bat")
    bat_path = os.path.abspath(bat_path)
    bat_path = bat_path.replace('/', '\\')
    bat_dir = os.path.dirname(bat_path)
    
    print("📝 生成修复后的VBS文件:")
    print(f"  BAT文件路径: {bat_path}")
    print(f"  工作目录: {bat_dir}")
    print("")
    
    # 新的VBS内容
    new_vbs_content = f'''Set WshShell = CreateObject("WScript.Shell")
WshShell.CurrentDirectory = "{bat_dir}"
WshShell.Run """{bat_path} scheduled""", 0, False
'''
    
    print("新的VBS文件内容:")
    print("  " + new_vbs_content.replace('\n', '\n  '))
    
    # 写入新的VBS文件
    try:
        with open(vbs_path, 'w', encoding='utf-8') as f:
            f.write(new_vbs_content)
        print("✅ VBS文件已更新")
    except Exception as e:
        print(f"❌ 更新VBS文件失败: {e}")
        return False
    
    return True


def test_vbs_file():
    """测试VBS文件"""
    print("\n" + "=" * 40)
    print("🧪 测试VBS文件")
    print("=" * 40)
    
    vbs_path = "run_video_management_scheduled.vbs"
    
    if not os.path.exists(vbs_path):
        print("❌ VBS文件不存在")
        return False
    
    print("🚀 手动执行VBS文件测试:")
    print(f"  执行命令: wscript.exe \"{vbs_path}\"")
    
    try:
        # 手动执行VBS文件
        result = subprocess.run([
            "wscript.exe", vbs_path
        ], capture_output=True, text=True, timeout=30)
        
        print(f"  返回码: {result.returncode}")
        if result.stdout:
            print(f"  输出: {result.stdout}")
        if result.stderr:
            print(f"  错误: {result.stderr}")
        
        if result.returncode == 0:
            print("✅ VBS文件执行成功")
            
            # 检查是否生成了日志文件
            print("\n📋 检查日志文件:")
            log_dir = "feiyingshuziren/log"
            if os.path.exists(log_dir):
                import datetime
                today = datetime.datetime.now().strftime("%Y-%m-%d")
                today_log_dir = os.path.join(log_dir, today)
                
                if os.path.exists(today_log_dir):
                    log_files = os.listdir(today_log_dir)
                    controller_logs = [f for f in log_files if 'controller' in f]
                    
                    if controller_logs:
                        print(f"  ✅ 找到日志文件: {controller_logs}")
                        
                        # 显示最新日志的前几行
                        latest_log = max(controller_logs)
                        log_path = os.path.join(today_log_dir, latest_log)
                        
                        print(f"\n📄 最新日志内容 ({latest_log}):")
                        try:
                            with open(log_path, 'r', encoding='utf-8') as f:
                                lines = f.readlines()
                                for i, line in enumerate(lines[:10]):  # 显示前10行
                                    print(f"    {line.strip()}")
                                if len(lines) > 10:
                                    print(f"    ... (还有 {len(lines)-10} 行)")
                        except Exception as e:
                            print(f"    读取日志失败: {e}")
                    else:
                        print("  ⚠️ 没有找到controller日志文件")
                else:
                    print(f"  ⚠️ 今天的日志目录不存在: {today_log_dir}")
            else:
                print(f"  ⚠️ 日志根目录不存在: {log_dir}")
            
            return True
        else:
            print("❌ VBS文件执行失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ VBS文件执行超时（30秒）")
        return False
    except Exception as e:
        print(f"❌ 执行VBS文件时出错: {e}")
        return False


def check_system_task():
    """检查系统任务状态"""
    print("\n" + "=" * 40)
    print("📊 检查系统任务状态")
    print("=" * 40)
    
    task_name = "VideoManagement_12_22c361bf"
    
    try:
        result = subprocess.run([
            "schtasks", "/query", "/tn", task_name, "/fo", "list"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 系统任务存在")
            
            # 解析任务信息
            lines = result.stdout.split('\n')
            for line in lines:
                if '上次运行时间' in line or 'Last Run Time' in line:
                    print(f"  {line.strip()}")
                elif '上次结果' in line or 'Last Result' in line:
                    print(f"  {line.strip()}")
                elif '下次运行时间' in line or 'Next Run Time' in line:
                    print(f"  {line.strip()}")
                elif '状态' in line or 'Status' in line:
                    print(f"  {line.strip()}")
            
            # 手动触发任务测试
            print(f"\n🚀 手动触发任务测试:")
            print(f"  可以执行: schtasks /run /tn {task_name}")
            
        else:
            print("❌ 系统任务不存在或查询失败")
            print(f"  错误: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 查询系统任务失败: {e}")


def provide_solutions():
    """提供解决方案"""
    print("\n" + "=" * 40)
    print("💡 解决方案")
    print("=" * 40)
    
    print("方案1: 重新创建系统任务（推荐）")
    print("  1. 在程序中删除当前系统任务")
    print("  2. 重新创建系统任务")
    print("  3. 新任务会使用修复后的VBS文件")
    print("")
    
    print("方案2: 手动触发测试")
    print("  1. 执行: schtasks /run /tn VideoManagement_12_22c361bf")
    print("  2. 观察是否生成日志文件")
    print("  3. 检查任务执行结果")
    print("")
    
    print("方案3: 检查BAT文件")
    print("  1. 手动执行: run_video_management_auto.bat scheduled")
    print("  2. 确认BAT文件本身没有问题")
    print("  3. 检查新控制器是否正常工作")
    print("")
    
    print("⚠️ 注意事项:")
    print("  1. 系统任务需要管理员权限")
    print("  2. VBS文件会隐藏运行，看不到窗口")
    print("  3. 检查日志文件确认执行结果")
    print("  4. 确保防病毒软件没有阻止执行")


def manual_trigger_test():
    """手动触发测试"""
    print("\n" + "=" * 40)
    print("🔥 手动触发系统任务")
    print("=" * 40)
    
    task_name = "VideoManagement_12_22c361bf"
    
    print(f"正在手动触发任务: {task_name}")
    
    try:
        result = subprocess.run([
            "schtasks", "/run", "/tn", task_name
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 任务触发成功")
            print("  请等待30-60秒，然后检查日志文件")
            print("  日志位置: feiyingshuziren/log/今天日期/")
        else:
            print("❌ 任务触发失败")
            print(f"  错误: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 触发任务失败: {e}")


if __name__ == "__main__":
    print("🔧 系统任务VBS文件修复工具")
    print("=" * 60)
    
    # 修复VBS文件
    if fix_vbs_file():
        # 测试VBS文件
        if test_vbs_file():
            print("\n🎉 VBS文件修复并测试成功！")
        else:
            print("\n⚠️ VBS文件修复成功，但测试失败")
    else:
        print("\n❌ VBS文件修复失败")
    
    # 检查系统任务
    check_system_task()
    
    # 提供解决方案
    provide_solutions()
    
    # 询问是否手动触发
    print("\n" + "=" * 60)
    trigger = input("是否立即手动触发系统任务进行测试? (y/n): ").lower().strip()
    if trigger == 'y':
        manual_trigger_test()
    
    print("\n🎯 下一步建议:")
    print("  1. 如果手动触发成功，说明VBS文件已修复")
    print("  2. 如果仍有问题，建议重新创建系统任务")
    print("  3. 新创建的任务会使用修复后的逻辑")
    print("  4. 观察日志文件确认执行结果")
