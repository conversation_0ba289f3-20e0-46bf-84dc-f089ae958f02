{"app_name": "光流一站式口播助手", "version": "2.0.0", "author": "光流团队", "description": "集成声音克隆和数字人功能的一站式口播助手", "settings": {"enable_clipboard_monitoring": true, "enable_dark_mode": false, "auto_save_interval": 300, "max_concurrent_tasks": 5, "default_audio_format": "mp3", "default_audio_quality": "high"}, "paths": {"data_dir": "data", "config_dir": "config", "logs_dir": "logs", "output_dir": "output", "temp_dir": "temp"}, "api": {"fish_audio_base_url": "https://api.fish.audio", "timeout": 30, "retry_count": 3}}