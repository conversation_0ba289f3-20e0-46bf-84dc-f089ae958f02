[2025-08-01 18:49:48] 日志文件: d:\project\guangliu02\feiyingshuziren\log\2025-08-01\video_management_auto_18-49-48.log
[2025-08-01 18:49:48] 视频管理自动化任务开始
[2025-08-01 18:49:48] 任务类型: full_process
[2025-08-01 18:49:48] 开始时间: 2025-08-01 18:49:48
[2025-08-01 18:49:48] [LIST] 将执行 3 个任务: material, upload, rename
[2025-08-01 18:49:48] 
============================================================
[2025-08-01 18:49:48] [LIST] 第1步：素材更新
[2025-08-01 18:49:48] ============================================================
[2025-08-01 18:49:48] ============================================================
[2025-08-01 18:49:48] [PROCESS] 开始执行素材更新任务
[2025-08-01 18:49:48] ============================================================
[2025-08-01 18:49:48] [PROCESS] 开始更新视频素材数据...
[2025-08-01 18:49:48] [ERROR] 素材更新任务异常: asyncio.run() cannot be called from a running event loop
[2025-08-01 18:49:48] 详细错误: Traceback (most recent call last):
  File "d:\project\guangliu02\src\video_management_runner.py", line 100, in run_material_update
    success = asyncio.run(video_manager.download_material_data())
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 190, in run
    raise RuntimeError(
RuntimeError: asyncio.run() cannot be called from a running event loop

[2025-08-01 18:49:48] [ERROR] 素材更新任务失败
[2025-08-01 18:49:48] 
============================================================
[2025-08-01 18:49:48] [UPLOAD] 第2步：飞影上传
[2025-08-01 18:49:48] ============================================================
[2025-08-01 18:49:48] ============================================================
[2025-08-01 18:49:48] [START] 开始执行飞影上传任务
[2025-08-01 18:49:48] ============================================================
[2025-08-01 18:49:48] [SEARCH] 获取待上传的视频列表...
[2025-08-01 18:49:51] [ERROR] 飞影上传任务异常: 'str' object has no attribute 'get'
[2025-08-01 18:49:51] 详细错误: Traceback (most recent call last):
  File "d:\project\guangliu02\src\video_management_runner.py", line 151, in run_hifly_upload
    if record.get('是否上传飞影', '否') == '否':
       ^^^^^^^^^^
AttributeError: 'str' object has no attribute 'get'

[2025-08-01 18:49:51] [ERROR] 飞影上传任务失败
[2025-08-01 18:49:51] 
============================================================
[2025-08-01 18:49:51] [RENAME] 第3步：自动重命名
[2025-08-01 18:49:51] ============================================================
[2025-08-01 18:49:51] ============================================================
[2025-08-01 18:49:51] [EDIT] 开始执行自动重命名任务
[2025-08-01 18:49:51] ============================================================
[2025-08-01 18:49:51] [SEARCH] 获取需要重命名的视频列表...
[2025-08-01 18:49:54] [ERROR] 自动重命名任务异常: 'str' object has no attribute 'get'
[2025-08-01 18:49:54] 详细错误: Traceback (most recent call last):
  File "d:\project\guangliu02\src\video_management_runner.py", line 209, in run_auto_rename
    if record.get('是否重命名', '否') == '否':
       ^^^^^^^^^^
AttributeError: 'str' object has no attribute 'get'

[2025-08-01 18:49:54] [ERROR] 自动重命名任务失败
[2025-08-01 18:49:54] 
============================================================
[2025-08-01 18:49:54] [STATS] 任务执行总结
[2025-08-01 18:49:54] ============================================================
[2025-08-01 18:49:54] [TARGET] 任务类型: full_process
[2025-08-01 18:49:54] [LIST] 总任务数: 3
[2025-08-01 18:49:54] [SUCCESS] 完成任务: 0
[2025-08-01 18:49:54] [ERROR] 失败任务: 3
[2025-08-01 18:49:54] [RATE] 成功率: 0.0%
[2025-08-01 18:49:54] [WARNING] 部分任务执行失败，请检查日志
[2025-08-01 18:49:54] [TIME] 结束时间: 2025-08-01 18:49:54
[2025-08-01 18:49:54] [START] 视频管理自动化任务结束
