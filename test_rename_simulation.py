"""
模拟重命名筛选过程
"""

import os
import sys
import pandas as pd
from datetime import datetime

# 添加src路径
sys.path.append('src')


def simulate_rename_filter(only_recent_uploaded=True):
    """模拟重命名筛选过程"""
    print("=" * 60)
    print(f"🧪 模拟重命名筛选过程 (only_recent_uploaded={only_recent_uploaded})")
    print("=" * 60)
    
    try:
        avatar_list_path = "data/avatar_list.xlsx"
        if not os.path.exists(avatar_list_path):
            print("❌ 数据文件不存在")
            return []
        
        # 读取Excel文件
        df = pd.read_excel(avatar_list_path)
        print(f"🔍 读取Excel文件: {len(df)} 条记录")
        
        # 筛选已上传但未重命名的视频
        if "是否上传飞影" not in df.columns:
            print("❌ 缺少'是否上传飞影'列")
            return []
        
        # 添加是否重命名列（如果不存在）
        if "是否重命名" not in df.columns:
            print("⚠️ 添加'是否重命名'列")
            df["是否重命名"] = ""
            df.to_excel(avatar_list_path, index=False)
        else:
            print("✅ '是否重命名'列已存在")
        
        # 调试：统计各种状态的数量
        upload_yes_count = (df["是否上传飞影"] == "是").sum()
        rename_not_yes_count = (df["是否重命名"] != "是").sum()
        print(f"🔍 已上传飞影的记录: {upload_yes_count} 条")
        print(f"🔍 重命名状态不为'是'的记录: {rename_not_yes_count} 条")
        
        if only_recent_uploaded:
            # 只重命名最近上传的视频（今天上传的）
            today = datetime.now().date()
            
            # 筛选条件：是否上传飞影 = "是" 且 是否重命名 != "是" 且 更新日期是今天
            if "更新日期" in df.columns:
                # 转换更新日期列为日期类型
                df["更新日期"] = pd.to_datetime(df["更新日期"], errors='coerce').dt.date
                
                # 修复筛选条件：正确处理NaN值和字符串
                mask = (df["是否上传飞影"] == "是") & (
                    (df["是否重命名"].isna()) |  # NaN值
                    (df["是否重命名"] == "") |   # 空字符串
                    (df["是否重命名"] != "是")   # 其他值（如"重命名失败"）
                ) & (df["更新日期"] == today)
                
                print(f"📅 筛选今天({today})上传的视频进行重命名")
            else:
                # 如果没有更新日期列，则使用所有符合条件的
                mask = (df["是否上传飞影"] == "是") & (
                    (df["是否重命名"].isna()) |  # NaN值
                    (df["是否重命名"] == "") |   # 空字符串
                    (df["是否重命名"] != "是")   # 其他值（如"重命名失败"）
                )
                print("⚠️ 没有更新日期列，将重命名所有符合条件的视频")
        else:
            # 重命名所有符合条件的视频
            mask = (df["是否上传飞影"] == "是") & (
                (df["是否重命名"].isna()) |  # NaN值
                (df["是否重命名"] == "") |   # 空字符串
                (df["是否重命名"] != "是")   # 其他值（如"重命名失败"）
            )
            print("📋 筛选所有已上传但未重命名的视频")
        
        need_rename_df = df[mask]
        
        # 添加调试信息
        print(f"🔍 筛选结果: {len(need_rename_df)} 条记录符合条件")
        if len(need_rename_df) > 0:
            print(f"📋 符合条件的视频ID: {list(need_rename_df['ID'].head(5))}")
        
        video_list = []
        for _, row in need_rename_df.iterrows():
            try:
                actor_name = str(row.get("拍摄演员名称", "")).strip()
                video_id = str(row.get("ID", "")).strip()
                
                if actor_name and video_id:
                    video_list.append({
                        "actor_name": actor_name,
                        "video_id": video_id
                    })
                    print(f"  ✅ 添加到重命名列表: {actor_name}-{video_id}")
            except Exception as e:
                print(f"⚠️ 处理记录时出错: {e}")
                continue
        
        print(f"\n📊 最终结果: {len(video_list)} 个视频需要重命名")
        return video_list
        
    except Exception as e:
        print(f"❌ 获取重命名视频列表失败: {str(e)}")
        return []


def main():
    """主测试函数"""
    # 测试两种模式
    print("测试1: 只重命名今天上传的视频")
    result1 = simulate_rename_filter(only_recent_uploaded=True)
    
    print("\n" + "="*60)
    print("测试2: 重命名所有符合条件的视频")
    result2 = simulate_rename_filter(only_recent_uploaded=False)
    
    print("\n" + "="*60)
    print("🎯 测试总结")
    print("="*60)
    print(f"今天上传的视频: {len(result1)} 个")
    print(f"所有符合条件的视频: {len(result2)} 个")
    
    if len(result1) > 0:
        print("✅ 筛选逻辑修复成功！")
    else:
        print("❌ 仍然没有找到需要重命名的视频")


if __name__ == "__main__":
    main()
