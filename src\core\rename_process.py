"""
独立的重命名进程
使用独立进程避免Qt线程与asyncio的冲突
"""

import asyncio
import sys
import json
import os
from pathlib import Path

# 添加src路径
sys.path.append(str(Path(__file__).parent.parent))

from core.hifly_rename_automation import HiflyRenameAutomation


async def rename_videos_process(video_list, headless=True):
    """
    在独立进程中执行重命名任务
    
    Args:
        video_list: 视频列表
        headless: 是否无头模式
    
    Returns:
        重命名结果列表
    """
    results = []
    automation = None
    
    try:
        print(f"[START] 独立进程开始重命名 {len(video_list)} 个视频...")

        # 创建重命名自动化实例
        automation = HiflyRenameAutomation()

        # 初始化浏览器
        await automation.init_browser(headless=headless)
        print("[OK] 浏览器初始化完成")
        
        # 逐个处理视频
        for i, video_info in enumerate(video_list, 1):
            try:
                video_id = video_info.get('video_id', '')
                actor_name = video_info.get('actor_name', '')
                
                print(f"[PROCESS] [{i}/{len(video_list)}] 处理视频: {actor_name} (ID: {video_id})")

                # 执行重命名
                success = await automation.rename_video(video_id, actor_name)

                if success:
                    result_msg = f"[SUCCESS] {actor_name} 重命名成功"
                    print(result_msg)
                    results.append({
                        "actor_name": actor_name,
                        "video_id": video_id,
                        "result": "重命名成功"
                    })
                else:
                    result_msg = f"[FAILED] {actor_name} 重命名失败"
                    print(result_msg)
                    results.append({
                        "actor_name": actor_name,
                        "video_id": video_id,
                        "result": "重命名失败"
                    })
                
                # 短暂延迟避免过快操作
                await asyncio.sleep(1)
                
            except Exception as e:
                error_msg = f"[ERROR] {actor_name} 重命名异常: {str(e)}"
                print(error_msg)
                results.append({
                    "actor_name": actor_name,
                    "video_id": video_id,
                    "result": f"重命名异常: {str(e)}"
                })

        # 统计结果
        success_count = sum(1 for r in results if "成功" in r["result"])
        total_count = len(results)

        print(f"[COMPLETE] 批量重命名完成！")
        print(f"[STATS] 成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
    except Exception as e:
        print(f"[ERROR] 重命名进程异常: {str(e)}")
        import traceback
        print(f"[ERROR] 异常详情: {traceback.format_exc()}")

    finally:
        # 安全关闭浏览器
        if automation:
            try:
                print("[CLOSE] 正在关闭浏览器...")
                await automation.close_browser()
                print("[OK] 浏览器已关闭")
            except Exception as e:
                print(f"[WARN] 关闭浏览器时出错: {str(e)}")
    
    return results


def main():
    """主函数"""
    try:
        # 设置输出编码为UTF-8（Windows兼容）
        import sys
        import io
        if sys.platform == "win32":
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
            sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')
        # 从命令行参数获取输入
        if len(sys.argv) < 2:
            print("[ERROR] 缺少参数")
            sys.exit(1)
        
        # 解析参数
        input_data = json.loads(sys.argv[1])
        video_list = input_data.get('video_list', [])
        headless = input_data.get('headless', True)
        
        # 设置Windows事件循环策略
        if sys.platform == "win32":
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        # 运行重命名任务
        results = asyncio.run(rename_videos_process(video_list, headless))
        
        # 输出结果
        output = {
            "success": True,
            "results": results
        }
        print("RESULT_JSON:" + json.dumps(output, ensure_ascii=False))
        
    except Exception as e:
        # 输出错误
        output = {
            "success": False,
            "error": str(e),
            "results": []
        }
        print("RESULT_JSON:" + json.dumps(output, ensure_ascii=False))
        sys.exit(1)


if __name__ == "__main__":
    main()
