"""
测试Excel更新修复
"""

import os
import sys

# 添加src路径
sys.path.append('src')


def test_excel_update():
    """测试Excel更新修复"""
    print("=" * 60)
    print("🔧 测试Excel更新修复")
    print("=" * 60)
    
    print("🔍 问题分析:")
    print("  重命名成功但表格没有更新:")
    print("    ✅ 重命名任务成功完成")
    print("    ✅ 表格数据重新加载")
    print("    ✅ 表格界面强制刷新")
    print("    ❌ '是否重命名'列没有更新")
    print("")
    print("  可能的原因:")
    print("    1. update_rename_status_in_excel 方法没有被调用")
    print("    2. Excel文件更新失败")
    print("    3. ID匹配失败")
    print("    4. 列名不匹配")
    print("    5. 文件保存失败")
    print("")
    
    print("🔧 修复内容:")
    print("  1. ✅ 添加详细的调试日志:")
    print("     - 显示开始更新的结果数量")
    print("     - 显示Excel文件路径和存在性")
    print("     - 显示文件行数和列信息")
    print("     - 显示每个ID的查找和更新过程")
    print("")
    print("  2. ✅ 增强错误处理:")
    print("     - 检查'是否重命名'列是否存在")
    print("     - 显示更新前后的状态对比")
    print("     - 统计实际更新的行数")
    print("     - 详细的异常信息")
    print("")
    print("  3. ✅ 验证更新过程:")
    print("     - 显示匹配的行数")
    print("     - 显示更新前的状态")
    print("     - 显示更新后的状态")
    print("     - 确认保存操作")
    print("")
    
    print("📋 预期的调试日志:")
    print("  开始更新:")
    print("    '🔄 开始更新Excel文件中的重命名状态，共 1 个结果'")
    print("    '📄 Excel文件路径: data/avatar_list.xlsx'")
    print("    '📊 Excel文件包含 XXXX 行数据'")
    print("")
    print("  查找和更新:")
    print("    '🔍 查找ID 58073，准备设置状态为: 是'")
    print("    '🔍 找到 1 行匹配ID 58073'")
    print("    '📝 ID 58073 更新前状态: 否'")
    print("    '✅ ID 58073 更新后状态: 是'")
    print("")
    print("  保存完成:")
    print("    '💾 正在保存Excel文件，共更新了 1 行...'")
    print("    '✅ 重命名状态已保存到Excel文件'")
    print("")
    
    print("🎯 预期效果:")
    print("  ✅ 能看到详细的更新过程")
    print("  ✅ Excel文件被正确更新")
    print("  ✅ 表格重新加载后显示新状态")
    print("  ✅ '是否重命名'列显示为'是'")
    print("")
    
    print("🚀 测试建议:")
    print("  1. 重启程序")
    print("  2. 再次进行飞影上传测试")
    print("  3. 仔细观察Excel更新的详细日志")
    print("  4. 检查是否有错误信息")
    print("  5. 确认表格最终是否更新")
    print("")
    
    print("💡 如果还有问题:")
    print("  可能的原因:")
    print("    1. Excel文件被其他程序占用")
    print("    2. 文件权限问题")
    print("    3. pandas版本兼容性问题")
    print("    4. 数据类型转换问题")
    print("")
    print("  调试方法:")
    print("    1. 检查Excel文件是否被Excel程序打开")
    print("    2. 检查文件权限")
    print("    3. 手动验证ID是否存在")
    print("    4. 检查列名是否完全匹配")
    print("")
    
    print("=" * 60)
    print("🔧 Excel更新修复完成")
    print("=" * 60)


def test_excel_file():
    """测试Excel文件"""
    print("\n" + "=" * 40)
    print("🧪 测试Excel文件")
    print("=" * 40)
    
    try:
        avatar_list_path = "data/avatar_list.xlsx"
        print(f"📄 Excel文件路径: {avatar_list_path}")
        print(f"✅ 文件存在: {os.path.exists(avatar_list_path)}")
        
        if os.path.exists(avatar_list_path):
            import pandas as pd
            df = pd.read_excel(avatar_list_path)
            print(f"📊 文件包含 {len(df)} 行数据")
            print(f"📋 列名: {list(df.columns)}")
            
            if '是否重命名' in df.columns:
                print("✅ 包含'是否重命名'列")
                rename_counts = df['是否重命名'].value_counts()
                print(f"📊 重命名状态统计: {dict(rename_counts)}")
            else:
                print("❌ 不包含'是否重命名'列")
            
            if 'ID' in df.columns:
                print("✅ 包含'ID'列")
                # 检查是否有ID 58073
                test_id = "58073"
                mask = df['ID'].astype(str) == test_id
                if mask.any():
                    print(f"✅ 找到测试ID {test_id}")
                    if '是否重命名' in df.columns:
                        current_status = df.loc[mask, '是否重命名'].iloc[0]
                        print(f"📝 当前重命名状态: {current_status}")
                else:
                    print(f"❌ 未找到测试ID {test_id}")
            else:
                print("❌ 不包含'ID'列")
                
        else:
            print("❌ Excel文件不存在")
            
    except Exception as e:
        print(f"❌ 测试Excel文件失败: {str(e)}")


if __name__ == "__main__":
    test_excel_update()
    test_excel_file()
