#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试语法修复
验证程序能正常启动和导入
"""

import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_import():
    """测试导入功能"""
    print("=== 测试导入功能 ===")
    
    try:
        # 测试导入主窗口
        from ui.main_window import MainWindow
        print("✅ MainWindow 导入成功")
        
        # 测试导入其他核心模块
        from core.video_material_manager import VideoMaterialManager
        print("✅ VideoMaterialManager 导入成功")

        # 测试主要的修复相关模块
        print("✅ 核心模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        return False

def test_syntax():
    """测试语法正确性"""
    print("\n=== 测试语法正确性 ===")
    
    try:
        import ast
        
        # 检查主要文件的语法
        files_to_check = [
            "src/ui/main_window.py",
            "src/core/video_material_manager.py",
            "src/core/processor.py",
            "src/main.py"
        ]
        
        for file_path in files_to_check:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                try:
                    ast.parse(content)
                    print(f"✅ {file_path} 语法正确")
                except SyntaxError as e:
                    print(f"❌ {file_path} 语法错误: {str(e)}")
                    return False
            else:
                print(f"⚠️ {file_path} 文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 语法检查失败: {str(e)}")
        return False

def test_indentation():
    """测试缩进正确性"""
    print("\n=== 测试缩进正确性 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        # 检查关键区域的缩进
        critical_lines = []
        for i, line in enumerate(lines, 1):
            if "for original_col_name in" in line:
                critical_lines.append((i, line.rstrip()))
            elif "try:" in line and i > 5200 and i < 5220:
                critical_lines.append((i, line.rstrip()))
            elif "except Exception as" in line and i > 5220 and i < 5240:
                critical_lines.append((i, line.rstrip()))
        
        print("关键行的缩进检查:")
        for line_num, line_content in critical_lines:
            indent_level = len(line_content) - len(line_content.lstrip())
            print(f"  第{line_num}行: 缩进{indent_level}个空格 - {line_content.strip()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 缩进检查失败: {str(e)}")
        return False

def test_method_structure():
    """测试方法结构完整性"""
    print("\n=== 测试方法结构完整性 ===")
    
    try:
        with open("src/ui/main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查populate_vm_table方法的结构
        method_features = [
            "def populate_vm_table(self, df):",
            "try:",
            "self.vm_table.blockSignals(True)",
            "for row_idx, (_, row_data) in enumerate(df.iterrows()):",
            "for original_col_name in",
            "except Exception as",
            "self.vm_table.blockSignals(False)",
        ]
        
        for feature in method_features:
            if feature in content:
                print(f"✅ 方法结构存在: {feature}")
            else:
                print(f"❌ 方法结构缺失: {feature}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 方法结构检查失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 语法修复验证测试")
    print("=" * 60)
    
    # 测试导入功能
    if not test_import():
        print("❌ 导入功能测试失败")
        return False
    
    # 测试语法正确性
    if not test_syntax():
        print("❌ 语法正确性测试失败")
        return False
    
    # 测试缩进正确性
    if not test_indentation():
        print("❌ 缩进正确性测试失败")
        return False
    
    # 测试方法结构完整性
    if not test_method_structure():
        print("❌ 方法结构完整性测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！")
    print("\n📋 修复总结:")
    print("✅ 1. 语法错误修复:")
    print("  - 修复了 for 循环后的缩进错误")
    print("  - 修复了 try-except 块的缩进问题")
    print("  - 确保了所有语句的正确缩进")
    
    print("\n✅ 2. 导入功能正常:")
    print("  - MainWindow 可以正常导入")
    print("  - 核心模块导入无错误")
    print("  - 程序可以正常启动")
    
    print("\n✅ 3. 代码结构完整:")
    print("  - populate_vm_table 方法结构完整")
    print("  - 异常处理机制正确")
    print("  - 信号管理逻辑完善")
    
    print("\n🎯 解决的问题:")
    print("  - IndentationError: expected an indented block → 修复缩进错误")
    print("  - SyntaxError: expected 'except' or 'finally' → 修复try-except结构")
    print("  - 程序无法启动 → 语法错误全部修复")
    
    print("\n🚀 预期效果:")
    print("  - 程序可以正常启动")
    print("  - 表格刷新功能正常工作")
    print("  - 异常处理机制完善")
    print("  - 代码结构清晰正确")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
