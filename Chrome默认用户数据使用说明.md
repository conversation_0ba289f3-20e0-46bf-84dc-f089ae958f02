# Chrome默认用户数据使用说明

## 重要更新

程序现在会使用你的**默认Chrome浏览器**中已保存的登录记录和Cookie，无需重新登录！

## 功能说明

### ✅ 自动获取现有登录状态
- 程序会自动检测并使用你的默认Chrome用户数据目录
- 包含你已保存的所有登录信息、Cookie、密码等
- 首次使用时如果已在Chrome中登录过目标网站，将自动保持登录状态

### 🔍 智能目录检测
程序会按优先级检测以下Chrome用户数据目录：

**Windows:**
```
~/AppData/Local/Google/Chrome/User Data
~/AppData/Local/Chromium/User Data
C:/Users/<USER>/AppData/Local/Google/Chrome/User Data
```

**macOS:**
```
~/Library/Application Support/Google/Chrome
~/Library/Application Support/Chromium
```

**Linux:**
```
~/.config/google-chrome
~/.config/chromium
```

### 🚀 启动流程优化
1. **检测现有Chrome进程**：避免与正在运行的Chrome冲突
2. **使用现有用户数据**：保留所有登录状态和设置
3. **启动调试实例**：在新窗口中打开，不影响现有浏览器
4. **自动恢复登录**：无需重新输入用户名密码

## 使用方法

### 首次使用
1. 确保你已在Chrome浏览器中登录过目标网站
2. 点击"素材更新"按钮
3. 程序会自动打开Chrome调试窗口
4. 登录状态会自动恢复，无需重新登录

### 日常使用
- 每次素材更新都会自动使用已保存的登录状态
- 无需重复登录操作
- 保持与你的默认Chrome浏览器同步

### 问题排查
如果登录状态出现问题，可以使用"重置登录"功能：
1. 点击"重置登录"按钮
2. 确认清理操作
3. 下次使用时重新登录

## 技术实现

### Chrome启动参数
```python
startup_args = [
    chrome_path,
    f"--remote-debugging-port={self.debug_port}",
    f"--user-data-dir={default_chrome_user_data}",  # 使用默认用户数据
    "--new-window",  # 在新窗口中打开
    "--disable-web-security",  # 允许跨域访问
    # 保留扩展和同步功能
    self.website_url
]
```

### 用户数据目录检测
```python
def get_default_chrome_user_data_dir(self) -> str:
    """获取默认Chrome用户数据目录"""
    system = platform.system()
    
    if system == "Windows":
        chrome_dirs = [
            os.path.expanduser("~/AppData/Local/Google/Chrome/User Data"),
            # ... 其他可能的路径
        ]
    # ... 其他系统的路径检测
    
    for chrome_dir in chrome_dirs:
        if os.path.exists(chrome_dir):
            return chrome_dir
```

### 登录数据清理
```python
def clear_chrome_debug_data(self) -> bool:
    """清理Chrome登录相关数据"""
    login_related_items = [
        "Default/Cookies",
        "Default/Login Data", 
        "Default/Web Data",
        "Default/Local Storage",
        "Default/Session Storage",
        # ... 其他登录相关文件
    ]
    # 只清理登录相关文件，保留其他用户数据
```

## 安全考虑

### ✅ 数据安全
- 只读取现有的Chrome用户数据，不修改原始文件
- 清理功能只删除登录相关数据，不影响其他设置
- 不会影响你的默认Chrome浏览器的正常使用

### ✅ 进程隔离
- 使用独立的调试端口（9222）
- 在新窗口中运行，不干扰现有Chrome实例
- 自动检测并避免进程冲突

### ✅ 用户控制
- 提供"重置登录"功能，随时清理登录数据
- 详细的确认对话框，防止误操作
- 完整的操作日志，便于问题排查

## 注意事项

### ⚠️ 重要提醒
1. **影响默认Chrome**：清理登录数据会影响你的默认Chrome浏览器
2. **关闭现有Chrome**：如果Chrome正在运行，建议先关闭以避免冲突
3. **备份重要数据**：清理前请确保重要登录信息已备份

### 💡 使用建议
1. **首次使用前**：在默认Chrome中登录目标网站
2. **遇到问题时**：使用"重置登录"功能重新开始
3. **定期维护**：如果登录状态异常，可以清理后重新登录

## 常见问题

### Q: 为什么还是需要重新登录？
A: 可能的原因：
- Chrome用户数据目录检测错误
- 目标网站的登录信息已过期
- Chrome安全策略限制

解决方法：使用"重置登录"功能，然后重新登录

### Q: 会不会影响我的默认Chrome浏览器？
A: 正常使用不会影响，但"重置登录"功能会清理登录数据，影响默认Chrome的登录状态

### Q: 如何确认程序使用了正确的Chrome目录？
A: 查看程序日志，会显示检测到的Chrome用户数据目录路径

现在程序能够智能使用你的现有Chrome登录状态，大大提升使用体验！
