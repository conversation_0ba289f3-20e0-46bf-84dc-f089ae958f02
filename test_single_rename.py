"""
测试单个重命名任务
"""

import asyncio
import os
import sys

# 添加项目路径到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from feiyingshuziren.hifly_rename_automation import HiflyRenameAutomation


async def test_single_rename():
    """测试单个重命名任务"""
    print("=" * 60)
    print("🧪 测试单个重命名任务")
    print("=" * 60)
    
    automation = HiflyRenameAutomation()
    
    try:
        print("\n🔧 初始化浏览器自动化...")
        
        # 加载认证数据
        if not automation.load_auth_data():
            print("❌ 加载认证数据失败")
            return
        
        # 初始化浏览器（非无头模式，便于观察）
        if not await automation.init_browser(headless=False):
            print("❌ 初始化浏览器失败")
            return
        
        print("\n🎯 开始测试韩卫军重命名...")
        
        result = await automation.process_rename_task("韩卫军", "48074")
        
        print(f"\n📊 测试结果: {result}")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n🔧 清理资源...")
        await automation.close_browser()
        print("✓ 测试完成")


def main():
    """主函数"""
    try:
        # 运行测试
        asyncio.run(test_single_rename())
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
