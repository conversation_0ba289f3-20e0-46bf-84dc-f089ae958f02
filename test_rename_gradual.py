#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
渐进式重命名进程测试
"""

import asyncio
import sys
import json
import os
from pathlib import Path

# 强制刷新输出
sys.stdout.reconfigure(line_buffering=True)
sys.stderr.reconfigure(line_buffering=True)

def flush_print(msg):
    print(msg)
    sys.stdout.flush()
    sys.stderr.flush()

async def test_rename_process():
    try:
        flush_print("[TEST] 开始测试...")
        
        # 添加src路径
        sys.path.append(str(Path(__file__).parent / "src"))
        flush_print("[TEST] 路径已添加")
        
        # 导入模块
        from core.hifly_rename_automation import HiflyRenameAutomation
        flush_print("[TEST] 模块导入成功")
        
        # 创建实例
        automation = HiflyRenameAutomation()
        flush_print("[TEST] 实例创建成功")
        
        # 加载认证数据
        auth_loaded = automation.load_auth_data()
        flush_print(f"[TEST] 认证数据加载: {auth_loaded}")
        
        if not auth_loaded:
            flush_print("[TEST] 认证数据加载失败，结束测试")
            return []
            
        flush_print("[TEST] 开始初始化浏览器...")
        # 这里是最可能卡住的地方
        try:
            browser_init = await automation.init_browser(headless=False)
            flush_print(f"[TEST] 浏览器初始化结果: {browser_init}")
            
            if browser_init:
                flush_print("[TEST] 浏览器初始化成功，立即关闭...")
                # 立即关闭浏览器避免卡住
                try:
                    await automation.close_browser()
                    flush_print("[TEST] 浏览器已关闭")
                except Exception as close_e:
                    flush_print(f"[TEST] 关闭浏览器失败: {close_e}")
            
        except Exception as e:
            flush_print(f"[TEST] 浏览器初始化异常: {e}")
            import traceback
            flush_print(f"[TEST] 异常详情: {traceback.format_exc()}")
            return []
            
        flush_print("[TEST] 测试完成")
        return [{"test": "success"}]
        
    except Exception as e:
        flush_print(f"[TEST] 测试过程异常: {e}")
        import traceback
        flush_print(f"[TEST] 异常详情: {traceback.format_exc()}")
        return []

def main():
    try:
        flush_print("[TEST] 主函数开始")
        
        # 解析参数
        if len(sys.argv) >= 2:
            input_data = json.loads(sys.argv[1])
            flush_print(f"[TEST] 输入数据: {len(input_data.get('video_list', []))} 个视频")
        
        # 设置事件循环
        flush_print("[TEST] 设置事件循环...")
        if sys.platform == "win32":
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        flush_print("[TEST] 事件循环创建成功")
        
        try:
            # 运行测试
            flush_print("[TEST] 开始运行异步测试...")
            results = loop.run_until_complete(test_rename_process())
            flush_print(f"[TEST] 异步测试完成，结果: {len(results)} 项")
            
        finally:
            flush_print("[TEST] 清理事件循环...")
            try:
                # 简化清理过程
                if loop.is_running():
                    flush_print("[TEST] 事件循环仍在运行，等待停止...")
                
                # 获取所有待完成的任务
                pending = asyncio.all_tasks(loop)
                flush_print(f"[TEST] 发现 {len(pending)} 个待完成任务")
                
                if pending:
                    # 设置短暂超时
                    try:
                        loop.run_until_complete(asyncio.wait_for(
                            asyncio.gather(*pending, return_exceptions=True), 
                            timeout=5.0
                        ))
                        flush_print("[TEST] 所有任务已完成")
                    except asyncio.TimeoutError:
                        flush_print("[TEST] 任务清理超时，强制取消...")
                        for task in pending:
                            task.cancel()
                
                loop.close()
                flush_print("[TEST] 事件循环已关闭")
            except Exception as e:
                flush_print(f"[TEST] 关闭事件循环失败: {e}")
                # 强制关闭
                try:
                    if not loop.is_closed():
                        loop.close()
                        flush_print("[TEST] 强制关闭事件循环成功")
                except:
                    flush_print("[TEST] 强制关闭事件循环也失败")
        
        # 输出结果
        output = {"success": True, "results": results}
        flush_print("RESULT_JSON:" + json.dumps(output, ensure_ascii=True))
        
    except Exception as e:
        flush_print(f"[TEST] 主函数异常: {e}")
        import traceback
        flush_print(f"[TEST] 异常详情: {traceback.format_exc()}")
        output = {"success": False, "error": str(e), "results": []}
        flush_print("RESULT_JSON:" + json.dumps(output, ensure_ascii=True))

if __name__ == "__main__":
    main()