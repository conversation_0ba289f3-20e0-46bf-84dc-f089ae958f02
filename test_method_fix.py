"""
测试方法调用修复
"""

import os
import sys

# 添加src路径
sys.path.append('src')


def test_method_fix():
    """测试方法调用修复"""
    print("=" * 60)
    print("🔧 测试方法调用修复")
    print("=" * 60)
    
    print("🔍 问题发现:")
    print("  根本问题:")
    print("    ❌ 在rename_process.py中调用了不存在的方法")
    print("    ❌ automation.rename_video() 方法不存在")
    print("    ❌ 应该调用 automation.process_rename_task()")
    print("")
    print("  错误表现:")
    print("    1. 浏览器打开，cookie认证成功")
    print("    2. 尝试调用不存在的方法")
    print("    3. 方法调用失败，抛出异常")
    print("    4. 异常被捕获，返回失败结果")
    print("    5. 浏览器关闭，进程结束")
    print("")
    
    print("🔧 修复内容:")
    print("  1. ✅ 方法调用修复:")
    print("     修复前: automation.rename_video(video_id, actor_name)")
    print("     修复后: automation.process_rename_task(actor_name, video_id)")
    print("")
    print("  2. ✅ 参数顺序修复:")
    print("     process_rename_task(actor_name, video_id)")
    print("     # 注意参数顺序：演员名在前，视频ID在后")
    print("")
    print("  3. ✅ 返回值处理修复:")
    print("     result = await automation.process_rename_task(...)")
    print("     success = '成功' in result")
    print("     # process_rename_task返回字符串，不是布尔值")
    print("")
    print("  4. ✅ 增强调试信息:")
    print("     - 添加方法调用前后的调试日志")
    print("     - 显示详细的失败原因")
    print("     - 追踪方法执行过程")
    print("")
    
    print("📋 修复后的执行流程:")
    print("  1. 浏览器初始化成功")
    print("  2. 认证数据加载成功")
    print("  3. 导航到数字人页面")
    print("  4. 调用 process_rename_task(actor_name, video_id)")
    print("  5. 搜索目标卡片")
    print("  6. 执行重命名操作")
    print("  7. 返回操作结果")
    print("  8. 安全关闭浏览器")
    print("")
    
    print("🎯 预期效果:")
    print("  ✅ 方法调用不再出错")
    print("  ✅ 能正确搜索到目标卡片")
    print("  ✅ 重命名操作能正常执行")
    print("  ✅ 返回正确的成功/失败状态")
    print("  ✅ 浏览器不会过早关闭")
    print("")
    
    print("🔍 关键调试日志:")
    print("  应该看到的日志:")
    print("    [DEBUG] 开始调用 process_rename_task...")
    print("    🎯 开始处理重命名任务: 演员名-视频ID-演员")
    print("    ✓ 已确认在数字人页面")
    print("    🔍 搜索卡片: 演员名-视频ID-演员")
    print("    ✅ 找到完全匹配的卡片 或 ✅ 找到ID匹配的卡片")
    print("    🖱️ 悬浮到卡片上...")
    print("    🔘 点击三个点按钮...")
    print("    ✓ 找到下拉菜单")
    print("    🎯 匹配到重命名元素")
    print("    🎉 重命名成功: 演员名-视频ID-演员")
    print("    [DEBUG] process_rename_task 返回结果: 重命名成功")
    print("    [SUCCESS] 演员名 重命名成功")
    print("")
    
    print("🚀 测试建议:")
    print("  1. 重启程序")
    print("  2. 再次进行飞影上传测试")
    print("  3. 仔细观察调试日志")
    print("  4. 检查是否出现上述关键日志")
    print("  5. 确认重命名成功率是否提高")
    print("")
    
    print("💡 如果还有问题:")
    print("  可能的原因:")
    print("    1. 卡片搜索失败（页面结构变化）")
    print("    2. 重命名按钮找不到（UI更新）")
    print("    3. 权限问题（认证过期）")
    print("    4. 网络问题（页面加载失败）")
    print("")
    print("  调试方法:")
    print("    观察具体在哪一步失败")
    print("    检查返回的错误信息")
    print("    确认页面是否正确加载")
    print("")
    
    print("=" * 60)
    print("🔧 方法调用修复完成")
    print("=" * 60)


def check_hifly_automation_methods():
    """检查HiflyRenameAutomation的方法"""
    print("\n" + "=" * 40)
    print("🔍 检查HiflyRenameAutomation方法")
    print("=" * 40)
    
    try:
        from core.hifly_rename_automation import HiflyRenameAutomation
        
        # 创建实例
        automation = HiflyRenameAutomation()
        
        # 检查方法是否存在
        methods_to_check = [
            'process_rename_task',
            'rename_video',
            'init_browser',
            'load_auth_data',
            'close_browser',
            'search_card_by_name',
            'rename_card',
            'ensure_on_avatar_page'
        ]
        
        print("📋 方法存在性检查:")
        for method_name in methods_to_check:
            exists = hasattr(automation, method_name)
            status = "✅" if exists else "❌"
            print(f"  {status} {method_name}")
            
            if exists:
                method = getattr(automation, method_name)
                is_async = hasattr(method, '__code__') and method.__code__.co_flags & 0x80
                async_info = " (async)" if is_async else " (sync)"
                print(f"      {async_info}")
        
        print("\n✅ 方法检查完成")
        
    except Exception as e:
        print(f"❌ 检查方法时出错: {str(e)}")


if __name__ == "__main__":
    test_method_fix()
    check_hifly_automation_methods()
