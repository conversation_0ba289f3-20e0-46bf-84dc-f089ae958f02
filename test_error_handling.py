"""
测试重命名错误处理功能
"""

import asyncio
import os
import sys

# 添加项目路径到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from feiyingshuziren.hifly_rename_automation import HiflyRenameAutomation


async def test_error_handling():
    """测试错误处理功能"""
    print("=" * 60)
    print("🧪 测试重命名错误处理功能")
    print("=" * 60)
    
    # 测试数据 - 使用相同的名称来触发"名字已被占用"错误
    test_data = [
        {"actor_name": "张卓铭", "video_id": "38050"},
        {"actor_name": "张卓铭", "video_id": "38050"}  # 重复的名称，应该会失败
    ]
    
    automation = HiflyRenameAutomation()
    
    try:
        print("\n🔧 初始化浏览器自动化...")
        
        # 加载认证数据
        if not automation.load_auth_data():
            print("❌ 加载认证数据失败")
            return
        
        # 初始化浏览器（非无头模式，便于观察）
        if not await automation.init_browser(headless=False):
            print("❌ 初始化浏览器失败")
            return
        
        print("\n🎯 开始测试错误处理...")
        
        # 处理每个测试数据
        results = []
        for i, data in enumerate(test_data, 1):
            print(f"\n--- 测试 {i}/{len(test_data)} ---")
            
            result = await automation.process_rename_task(
                data["actor_name"],
                data["video_id"]
            )
            
            # 记录结果
            results.append({
                "ID": data["video_id"],
                "演员名称": data["actor_name"],
                "结果": result
            })
            
            print(f"📊 测试结果: {result}")
            
            # 等待一段时间再处理下一个
            if i < len(test_data):
                print("⏳ 等待3秒后处理下一个...")
                await asyncio.sleep(3)
        
        # 显示测试结果
        print("\n" + "=" * 60)
        print("📊 错误处理测试结果")
        print("=" * 60)
        
        for result in results:
            status_icon = "✅" if "成功" in result["结果"] else "❌"
            print(f"{status_icon} {result['演员名称']}-{result['ID']}-演员: {result['结果']}")
        
        print("\n🎯 测试要点:")
        print("- 第一次重命名应该成功")
        print("- 第二次重命名应该失败（名字已被占用）")
        print("- 失败后应该正确关闭弹窗，不影响后续操作")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n🔧 清理资源...")
        await automation.close_browser()
        print("✓ 测试完成")


def main():
    """主函数"""
    try:
        # 运行测试
        asyncio.run(test_error_handling())
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
